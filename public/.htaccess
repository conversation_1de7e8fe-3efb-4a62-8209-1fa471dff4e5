<IfModule mod_rewrite.c>
    <IfModule mod_negotiation.c>
        Options -MultiViews -Indexes
    </IfModule>



    RewriteEngine On

    # Handle Authorization Header
    RewriteCond %{HTTP:Authorization} .
    RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]

    # Redirect Trailing Slashes If Not A Folder...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} (.+)/$
    RewriteRule ^ %1 [L,R=301]

    # SPENDEN.KURIERLERNHAUS.AT auf Wunsch derzeit weiterleiten
    RewriteCond %{HTTP_HOST} ^spenden\.kurierlernhaus\.at
    RewriteCond %{REQUEST_URI} !^/\.well-known/acme-challenge/[0-9a-zA-Z_-]+$
    RewriteRule ^(.*)$ https://kurierlernhaus.at/$1 [R=301,L]

    # FREIZEITLIVE
    RewriteCond %{HTTP_HOST} ^(www\.)?freizeitlive\.at
    RewriteRule ^(.*)$ https://freizeitlive.kurier.at/$1 [R=301,L]

    # WORTANZEIGEN
    RewriteCond %{HTTP_HOST} ^wortanzeigen\.mediaprint\.at
    RewriteRule ^(.*)$ https://www.mediaprint.at/wortanzeigen/$1 [R=301,L]

    RewriteCond %{HTTP_HOST} ^mediaprint.at$ [NC]
    RewriteRule ^(.*)$ https://www.mediaprint.at/$1 [L,R=301]

    # Send Requests To Front Controller...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ index.php [L]

</IfModule>
