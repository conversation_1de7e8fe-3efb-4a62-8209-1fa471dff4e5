class VimeoVideoUploader {

    form;
    tus;

    constructor(form) {
        this.form = form;
        this.onInit()
        this.tus = window.tus
    }


    onProgress = (bytesUploaded, bytesTotal) => {
        const percentage = (bytesUploaded / bytesTotal * 100).toFixed(2)
        console.log(bytesUploaded, bytesTotal, percentage + "%")
    }

    onSuccess = (upload) => {

        if(!upload){
            console.log('Updated metadata')
            return
        }

        console.log("Download %s from %s", upload.file.name, upload.url)
    }


    onError = (error) => {
        console.error('Error:', error)
    }


    onStart = () => {
        console.log('onStart')
    }

    onComplete = () => {
        console.log('onComplete')
    }

    startUpload(videoFile, uploadUrl) {

        const upload = new this.tus.Upload(videoFile, {
            uploadUrl: uploadUrl,
            onError: (error) => {
                // this.submitButton.disabled = false;
                this.onError(error)
            },
            onProgress: (bytesUploaded, bytesTotal) => this.onProgress(bytesUploaded, bytesTotal),
            onSuccess: () => {
                this.onSuccess(upload)
            }
        })

        upload.findPreviousUploads().then(function (previousUploads) {
            // Found previous uploads so we select the first one.
            if (previousUploads.length) {
                //    upload.resumeFromPreviousUpload(previousUploads[0])
            }

            // Start the upload
            upload.start()
        })


    }

    onInit() {

        this.form.addEventListener('submit', async (event) => {
            event.preventDefault();
            event.stopPropagation();

            // Disable the submit button immediately after preventing the default form submission
            /*
            * Call On start
            */
            this.onStart()
            /*
            * Getting inputs
            */
            const inputs = Array.from(form.querySelectorAll('input, textarea, select'));

            const formData = new FormData();
            /*
            * File default null
            */
            let videoFile = null;

            for (let input of inputs) {


                if (input.type === 'file' && input.files.length) {
                    if (input.hasAttribute('data-vimeo-video')) {
                        /*
                                          * set file
                                          */
                        videoFile = input.files[0];
                        /*
                        * send to laravel only the file size & name
                        */
                        formData.append('vimeo_video_size', videoFile.size.toString());
                        formData.append('vimeo_video_name', videoFile.name);
                        /*
                        * Reset input
                        */
                        input.value = '';
                    } else {
                        formData.append(input.name, input.files[0]);
                    }
                } else if(input.type === 'checkbox') {

                    formData.append(input.name, (input.checked) ? input.value : '')
                } else {

                    formData.append(input.name, input.value)
                }

            }




            const response = await fetch(form.action, {method: 'POST', body: formData})

            const data = await response.json();


            if (!response.ok) {
                console.log('HER I AM')
                this.onError(data)

                return;
            }


            if (!videoFile) {
                /*
                *  only a Video container without Object
                */
                this.onSuccess(null)
                return;
            }


            const uploadLink = data['vimeo_upload_link'];
            if (!uploadLink) {
                const errorText = 'vimeo_upload_link MUST BE IN THE RESPONSE';
                console.error(errorText, data)
                this.onError({message: errorText})
            } else {
                this.startUpload(videoFile, uploadLink)
            }


        })
    }
}
