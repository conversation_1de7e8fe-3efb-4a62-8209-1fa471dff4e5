{"_from": "tus-js-client", "_id": "tus-js-client@3.0.1", "_inBundle": false, "_integrity": "sha512-2EZIUvswv1xG3KtzJO9FZ2wvTtVzltUN23Nse/nZwWE06dbn/8RBeRqi2clV/ilpEomOQOOMdHkIPUQmTum/xg==", "_location": "/tus-js-client", "_phantomChildren": {}, "_requested": {"type": "tag", "registry": true, "raw": "tus-js-client", "name": "tus-js-client", "escapedName": "tus-js-client", "rawSpec": "", "saveSpec": null, "fetchSpec": "latest"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmjs.org/tus-js-client/-/tus-js-client-3.0.1.tgz", "_shasum": "d81fea22b8fb23769f973aa49501afef0f97928a", "_spec": "tus-js-client", "_where": "C:\\Users\\<USER>\\Desktop\\Neuer Ordner", "browser": {"./lib.es5/node/index.js": "./lib.es5/browser/index.js", "./lib.esm/node/index.js": "./lib.esm/browser/index.js", "./lib/node/index.js": "./lib/browser/index.js"}, "bugs": {"url": "https://github.com/tus/tus-js-client/issues"}, "bundleDependencies": false, "dependencies": {"buffer-from": "^1.1.2", "combine-errors": "^3.0.3", "is-stream": "^2.0.0", "js-base64": "^3.7.2", "lodash.throttle": "^4.1.1", "proper-lockfile": "^4.1.2", "url-parse": "^1.5.7"}, "deprecated": false, "description": "A pure JavaScript client for the tus resumable upload protocol", "devDependencies": {"@babel/cli": "^7.0.0", "@babel/core": "^7.13.10", "@babel/eslint-parser": "^7.13.10", "@babel/eslint-plugin": "^7.13.10", "@babel/helper-get-function-arity": "^7.16.7", "@babel/plugin-syntax-jsx": "^7.12.13", "@babel/plugin-transform-modules-commonjs": "^7.9.6", "@babel/preset-env": "^7.0.0", "axios": "^0.27.2", "babelify": "^10.0.0", "browserify": "^17.0.0", "chokidar-cli": "^3.0.0", "eslint": "^7.22.0", "eslint-config-transloadit": "^2.0.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jest": "^24.3.1", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.3.1", "eslint-plugin-react": "^7.22.0", "eslint-plugin-react-hooks": "^4.2.0", "exorcist": "^2.0.0", "into-stream": "^6.0.0", "jasmine": "^4.1.0", "jasmine-ajax": "^4.0.0", "jasmine-core": "^4.1.1", "karma": "^6.4.0", "karma-browserstack-launcher": "^1.6.0", "karma-chrome-launcher": "^3.1.1", "karma-jasmine": "^5.1.0", "npm-run-all": "^4.1.5", "puppeteer": "^17.1.3", "regenerator-runtime": "^0.13.5", "temp": "^0.9.4", "throttle": "^1.0.3", "tsd": "0.23.0", "uglify-js": "^3.9.2"}, "files": ["lib/**/*", "lib.es5/**/*", "lib.esm/**/*", "dist/**/*", "lib/index.d.ts"], "homepage": "https://github.com/tus/tus-js-client", "keywords": ["tus", "resumable", "upload", "protocol", "progress", "file", "browser"], "license": "MIT", "main": "lib.es5/node/index.js", "module": "lib.esm/node/index.js", "name": "tus-js-client", "repository": {"type": "git", "url": "git+https://github.com/tus/tus-js-client.git"}, "scripts": {"build": "npm-run-all build-bundle build-minify build-transpile build-test-bundle", "build-bundle": "mkdir -p dist && browserify lib/browser/index.js -t [ babelify --plugins [ @babel/transform-modules-commonjs ] ] -s tus -d | exorcist ./dist/tus.js.map > dist/tus.js", "build-minify": "uglifyjs ./dist/tus.js -o ./dist/tus.min.js --compress --mangle --source-map \"content='./dist/tus.js.map',url='tus.min.js.map'\"", "build-test-bundle": "mkdir -p dist && browserify test/spec/browser-index.js -t [ babelify --plugins [ @babel/transform-modules-commonjs ] ] -d -o dist/browser-test-bundle.js", "build-transpile": "npm-run-all build-transpile-esm build-transpile-cjs", "build-transpile-cjs": "babel --no-babelrc --plugins @babel/transform-modules-commonjs -d lib.es5/ lib.esm/", "build-transpile-esm": "babel -d lib.esm/ lib/", "lint": "eslint . --cache", "test-browserstack": "karma start test/karma/browserstack.conf.js", "test-node": "jasmine test/spec/node-index.js", "test-puppeteer": "karma start test/karma/puppeteer.conf.js", "test-types": "tsd", "watch": "npm-run-all --parallel watch-*", "watch-bundle": "chokidar --initial \"lib/**/*\" -c \"npm run build-bundle\"", "watch-test-bundle": "chokidar --initial \"test/spec/**/*\" -c \"npm run build-test-bundle\"", "watch-transpile": "chokidar --initial \"lib/**/*\" -c \"npm run build-transpile\""}, "types": "./lib/index.d.ts", "version": "3.0.1"}