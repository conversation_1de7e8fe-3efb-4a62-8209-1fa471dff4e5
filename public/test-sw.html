<!DOCTYPE html>
<html>
<body>

<p>Count numbers: <output id="result"></output></p>
<button onclick="startWorker()">Start Worker</button> 
<!-- <button onclick="stopWorker()">Stop Worker</button> -->
<br><br>

<ul id="screen"></ul>

<script>

    const screen = document.getElementById('screen');

    let loop = 0;
    let prev = Date.now();

    function logTime() {
        const now = Date.now();
        const diff = (now - prev) / 1000; // Convert milliseconds to seconds
        prev = now;

        const currentTime = diff.toFixed(1);

        if (diff > 5) {
            screen.innerHTML += `<li style="color: red;">${loop++}) ${currentTime} seconds</li>`;
        } else {
            screen.innerHTML += `<li>${loop++}) ${currentTime} seconds</li>`;
        }
    }


var w;

function startWorker()
{
    if(typeof(Worker) !== "undefined")
    {
        if(typeof(w) == "undefined")
        {
            w = new Worker("worker.js");
        }

        w.onmessage = function (event) {
            document.getElementById("result").innerHTML = event.data;
            logTime()
        };
    }
    else
    {
        document.getElementById("result").innerHTML = "Unsupported!";
    }
}

function stopWorker()
{ 
    w.terminate();
}
</script>

</body>
</html>