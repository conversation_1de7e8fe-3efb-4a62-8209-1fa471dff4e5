// Built directly into Dart Sass (not part of project files or Bootstrap)
@use "sass:map";


$bgMainColor: #f6f2f1;

// Define custom theme colors
$custom-colors: (
    "red": #B60017,
    "main": $bgMainColor
);

// redefine $theme-colors before importing Bootstrap
$theme-colors: map.merge(
        (
            "primary": #0d6efd,
            "secondary": #6c757d,
            "success": #198754,
            "info": #0dcaf0,
            "warning": #ffc107,
            "danger": #dc3545,
            "light": #f8f9fa,
            "dark": #3e4b63,
        ),
        $custom-colors
);

// Now use Bootstrap with variable overrides
@use "../../bootstrap-5.2.2/scss/bootstrap" with (
  $theme-colors: $theme-colors
);

@import "fonts";
@import "hero";
@import "navbar";
@import "highlights";
@import "speakers";
@import "partners";
@import "overview_page";
@import "carousel";

:root {
    --main-nav-height: 5.8rem;
    --hero-min-height: 0;
}

@media (min-width: 1200px) {
    :root {
        --hero-min-height:  90vh;
    }
}


#mainNav{
    min-height: var(--main-nav-height);
}

#hero  {
    min-height: var(--hero-min-height);
}

body {
    margin-top: var(--main-nav-height);
    height: calc(100vh - var(--main-nav-height));
   // background: $bgMainColor;
}

html, body {
    font-size: 15px;
    font-family: 'Open Sans', sans-serif;
    color: #3e4b63;
}

@media (min-width: 1200px) {
    .container {
        max-width: 1600px;
    }
}


.hoverable-card{
    img{
        transition: transform .2s ease-out;
    }
    &:hover{
        img{
            transform: scale(1.02);
        }
    }
}

.main-section {
    background: linear-gradient(to bottom, rgba(246, 242, 241, 0.27) 0%, #f6f2f1 100%);
}

.main-section-header {
    padding: 3rem 0;
}

.truncate-2-lines {
    display: -webkit-box;
    -webkit-line-clamp: 2; // limits to 2 lines
    -webkit-box-orient: vertical;
    overflow: hidden;
    min-height: 3em;
}



.link-animation > p:first-of-type {
    position: relative;
    display: inline-block;
    cursor: pointer;
}

.link-animation > p:first-of-type::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 2px;
    background: currentColor;
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.3s ease;
}

.link-animation:hover > p:first-of-type::after {
    transform: scaleX(1);
}
