



#mainNav {
    background-color: rgba(255, 255, 255, 0.91);
    font-weight: 600;
    padding: 1rem 0;

    .navbar-nav {
        @media (min-width: 992px) {
            margin-top: 0;
        }

        li.nav-item {
            a.nav-link {
                font-size: 1rem;
                padding: 1rem 0.75rem;
                border-radius: calc(0.5rem + 20px);
                /*               color: #000;*/

                @media (min-width: 992px) {
                    margin-left: 1rem;
                }

                &:hover {
                    color: #b60017;
                }

                &.special-btn {
                    background-color: #3e4b63;
                    color: #fff;

                    &:hover {
                        background-color: #b60017;
                    }
                }
            }
        }
    }

    .navbar-toggler {
        font-size: 1.5rem;

        &:focus {
            box-shadow: none;
        }
    }

    .navbar-brand img {
        height: 50px;
    }

    @media (max-width: 450px){
        .navbar-brand img {
            height: 30px;
        }
    }
}

