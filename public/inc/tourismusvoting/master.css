/******************************************************
******************** ALLGEMEIN ************************
*******************************************************/
*{
    margin:0;
    padding:0;
    box-shadow:none !important;
}

html{
    margin:0;
    padding:0;
    font-size:17px;
}

body{
    background-color:#FFFFFF;
    color:#333333 !important;
    font-family:Roboto, sans-serif;
    font-weight:300;
}

.container-fluid{
    max-width:1300px;
    padding:0 1rem;
}

h1, .h1, h2, .h2{
    font-size:1.85rem;
    font-family:Roboto, Arial, Helvetica, sans-serif;
    font-weight:500;
    color:#003060 !important;
}

.claim{
    font-size:1.5rem !important;
    color:#003060 !important;
    font-weight:500 !important;
}

a{
    color:#333333;
}
a:hover{
    color:#333333;
}

b, strong{
    font-weight:700;
}

.content-wrapper{
    min-height:calc(100vh - 186px);
}

.header-logo{
    height:65px;
    margin-top:1rem;
}

.teaser-logo{
    width:100%;
    max-width:620px;
}

.blue-line{
    height:20px;
    background-color:#005A9A !important;
    display:block;
    width:100%;
    margin:1rem 0 3rem 0;
}

hr{
    margin:0;
}

.bg-1{
    background-color:#DBE8D1;
}
.bg-2{
    background-color:#E1EBF8;
}
.bg-3{
    background-color:#FDF3CA !important;
}

.card{
    border:none !important;
}

.card-footer{
    background-color:transparent !important;
    padding:0.5rem 0 0 0 !important;
    border:none !important;
}

.voting-body{
    cursor:pointer;
}

.project-title{
    color:#003060 !important;
    font-weight:700;
    font-size:0.9rem;
}

.form-check{
    padding:0 !important;
}

.form-check input[type=radio]{
    transform:scale(1.5);
    box-shadow:none !important;
    cursor:pointer;
    margin:1rem 0 0.8rem 0;
}
.form-check-input:checked{
    background-color:#005A9A !important;
    border-color:#005A9A !important;
}

.form-control{
    background-color:transparent !important;
    border-color:#CCCCCC !important;
    box-shadow:none !important;
    color:#333333 !important;
    border-radius:2px !important;
    transition:all .3s !important;
    transition-timing-function:ease-in-out;
}

.form-control:hover, .form-control:active, .form-control:focus{
    border-color:#005A9A !important;
}


.btn-primary{
    background-color:#005A9A;
    border-color:#005A9A;
    transition:all .3s !important;
    transition-timing-function:ease-in-out;
    border-radius:2px;
}

.btn-outline-primary{
    color:#005A9A;
    border-color:#005A9A;
    transition:all .3s !important;
    transition-timing-function:ease-in-out;
    border-radius:2px;
}

.btn-primary:hover, .btn-outline-primary:hover{
    border-color:#FFD500 !important;
    background-color:#FFD500;
    color:#222222;
}

.footer-wrapper{
    background-color:#003060;
    color:#FFFFFF !important;
    font-size:14px;
}

.footer-wrapper .nav-link{
    color:#C4C4C4 !important;
    text-decoration:none;
}

.footer-wrapper .nav-link:hover{
    color:#FFFFFF !important;
    text-decoration:underline;
}


#captchaReload{
    font-size:1.4rem;
    margin-left:0.5rem;
    padding:4px 10px;
}

#optanon-cookie-policy *{
    color:#333333 !important;
}

#optanon-cookie-policy thead *{
    color:#333333 !important;
}

#optanon-cookie-policy table a{
    color:#333333 !important;
}

.optanon-toggle-display{
    cursor:pointer !important;
}
