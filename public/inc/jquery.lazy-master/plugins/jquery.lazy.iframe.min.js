/*! jQuery & Zepto Lazy - iFrame Plugin v1.5 - http://jquery.eisbehr.de/lazy - MIT&GPL-2.0 license - Copyright 2012-2018 Daniel 'Eisbehr' Kern */
!function(t){t.lazy(["frame","iframe"],"iframe",function(r,a){var e=this;if("iframe"===r[0].tagName.toLowerCase()){var o=r.attr("data-error-detect");"true"!==o&&"1"!==o?(r.attr("src",r.attr("data-src")),e.config("removeAttribute")&&r.removeAttr("data-src data-error-detect")):t.ajax({url:r.attr("data-src"),dataType:"html",crossDomain:!0,xhrFields:{withCredentials:!0},success:function(t){r.html(t).attr("src",r.attr("data-src")),e.config("removeAttribute")&&r.removeAttr("data-src data-error-detect")},error:function(){a(!1)}})}else a(!1)})}(window.jQuery||window.Zepto);