{"name": "j<PERSON>y-lazy", "description": "Lazy is a fast, feature-rich and lightweight delayed content loading plugin for jQuery and Zepto. It's designed to speed up page loading times and decrease traffic to your users by only loading the content in view. You can use <PERSON><PERSON> in all vertical and horizontal scroll ways. It supports images in 'img' tags and backgrounds, supplied with css like 'background-image', by default. On those elements <PERSON><PERSON> can set an default image or a placeholder while loading and supports retina displays as well. But <PERSON><PERSON> is even able to load any other content you want by plugins and custom loaders.", "version": "1.7.10", "main": "jquery.lazy.min.js", "license": ["MIT", "GPL-2.0"], "ignore": ["*.md", "*.json"], "keywords": ["lazy", "lazyload", "load", "loader", "image", "images", "background", "content", "speed", "delay", "delayed", "pageload", "performance", "retina", "placeholder", "j<PERSON>y", "jquery-plugin", "zepto", "zepto-plugin"], "authors": [{"name": "<PERSON> '<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.eisbehr.de/"}], "homepage": "http://jquery.eisbehr.de/lazy/", "repository": {"type": "git", "url": "git://github.com/eisbehr-/jquery.lazy.git"}, "dependencies": {"jquery": ">=1.7.2"}}