{"name": "bootstrap", "description": "The most popular front-end framework for developing responsive, mobile first projects on the web.", "version": "5.2.2", "config": {"version_short": "5.2"}, "keywords": ["css", "sass", "mobile-first", "responsive", "front-end", "framework", "web"], "homepage": "https://getbootstrap.com/", "author": "The Bootstrap Authors (https://github.com/twbs/bootstrap/graphs/contributors)", "contributors": ["Twitter, Inc."], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/twbs/bootstrap.git"}, "bugs": {"url": "https://github.com/twbs/bootstrap/issues"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/twbs"}, {"type": "opencollective", "url": "https://opencollective.com/bootstrap"}], "main": "dist/js/bootstrap.js", "module": "dist/js/bootstrap.esm.js", "sass": "scss/bootstrap.scss", "style": "dist/css/bootstrap.css", "scripts": {"start": "npm-run-all --parallel watch docs-serve", "bundlewatch": "bundlewatch --config .bundlewatch.config.json", "css": "npm-run-all css-compile css-prefix css-rtl css-minify", "css-compile": "sass --style expanded --source-map --embed-sources --no-error-css scss/:dist/css/", "css-rtl": "cross-env NODE_ENV=RTL postcss --config build/postcss.config.js --dir \"dist/css\" --ext \".rtl.css\" \"dist/css/*.css\" \"!dist/css/*.min.css\" \"!dist/css/*.rtl.css\"", "css-lint": "npm-run-all --aggregate-output --continue-on-error --parallel css-lint-*", "css-lint-stylelint": "stylelint \"**/*.{css,scss}\" --cache --cache-location .cache/.stylelintcache --rd", "css-lint-vars": "fusv scss/ site/assets/scss/", "css-minify": "npm-run-all --aggregate-output --parallel css-minify-*", "css-minify-main": "cleancss -O1 --format breakWith=lf --with-rebase --source-map --source-map-inline-sources --output dist/css/ --batch --batch-suffix \".min\" \"dist/css/*.css\" \"!dist/css/*.min.css\" \"!dist/css/*rtl*.css\"", "css-minify-rtl": "cleancss -O1 --format breakWith=lf --with-rebase --source-map --source-map-inline-sources --output dist/css/ --batch --batch-suffix \".min\" \"dist/css/*rtl.css\" \"!dist/css/*.min.css\"", "css-prefix": "npm-run-all --aggregate-output --parallel css-prefix-*", "css-prefix-main": "postcss --config build/postcss.config.js --replace \"dist/css/*.css\" \"!dist/css/*.rtl*.css\" \"!dist/css/*.min.css\"", "css-prefix-examples": "postcss --config build/postcss.config.js --replace \"site/content/**/*.css\"", "css-prefix-examples-rtl": "cross-env-shell NODE_ENV=RTL postcss --config build/postcss.config.js --dir \"site/content/docs/$npm_package_config_version_short/examples/\" --ext \".rtl.css\" --base \"site/content/docs/$npm_package_config_version_short/examples/\" \"site/content/docs/$npm_package_config_version_short/examples/{blog,carousel,dashboard,cheatsheet}/*.css\" \"!site/content/docs/$npm_package_config_version_short/examples/{blog,carousel,dashboard,cheatsheet}/*.rtl.css\"", "js": "npm-run-all js-compile js-minify", "js-compile": "npm-run-all --aggregate-output --parallel js-compile-*", "js-compile-standalone": "rollup --environment BUNDLE:false --config build/rollup.config.js --sourcemap", "js-compile-standalone-esm": "rollup --environment ESM:true,BUNDLE:false --config build/rollup.config.js --sourcemap", "js-compile-bundle": "rollup --environment BUNDLE:true --config build/rollup.config.js --sourcemap", "js-compile-plugins": "node build/build-plugins.js", "js-lint": "eslint --cache --cache-location .cache/.eslintcache --report-unused-disable-directives --ext .html,.js .", "js-minify": "npm-run-all --aggregate-output --parallel js-minify-*", "js-minify-standalone": "terser --compress passes=2 --mangle --comments \"/^!/\" --source-map \"content=dist/js/bootstrap.js.map,includeSources,url=bootstrap.min.js.map\" --output dist/js/bootstrap.min.js dist/js/bootstrap.js", "js-minify-standalone-esm": "terser --compress passes=2 --mangle --comments \"/^!/\" --source-map \"content=dist/js/bootstrap.esm.js.map,includeSources,url=bootstrap.esm.min.js.map\" --output dist/js/bootstrap.esm.min.js dist/js/bootstrap.esm.js", "js-minify-bundle": "terser --compress passes=2 --mangle --comments \"/^!/\" --source-map \"content=dist/js/bootstrap.bundle.js.map,includeSources,url=bootstrap.bundle.min.js.map\" --output dist/js/bootstrap.bundle.min.js dist/js/bootstrap.bundle.js", "js-test": "npm-run-all --aggregate-output --parallel js-test-karma js-test-jquery js-test-integration-*", "js-debug": "cross-env DEBUG=true npm run js-test-karma", "js-test-karma": "karma start js/tests/karma.conf.js", "js-test-integration-bundle": "rollup --config js/tests/integration/rollup.bundle.js", "js-test-integration-modularity": "rollup --config js/tests/integration/rollup.bundle-modularity.js", "js-test-cloud": "cross-env BROWSERSTACK=true npm run js-test-karma", "js-test-jquery": "cross-env JQUERY=true npm run js-test-karma", "lint": "npm-run-all --aggregate-output --continue-on-error --parallel js-lint css-lint lockfile-lint", "docs": "npm-run-all docs-build docs-lint", "docs-build": "hugo --cleanDestinationDir", "docs-compile": "npm run docs-build", "docs-vnu": "node build/vnu-jar.js", "docs-lint": "npm run docs-vnu", "docs-serve": "hugo server --port 9001 --disableFastRender", "docs-serve-only": "npx sirv-cli _site --port 9001", "lockfile-lint": "lockfile-lint --allowed-hosts npm --allowed-schemes https: --empty-hostname false --type npm --path package-lock.json", "update-deps": "ncu -u -x globby,karma-browserstack-launcher,karma-rollup-preprocessor && echo Manually update site/assets/js/vendor", "release": "npm-run-all dist release-sri docs-build release-zip*", "release-sri": "node build/generate-sri.js", "release-version": "node build/change-version.js", "release-zip": "cross-env-shell \"rm -rf bootstrap-$npm_package_version-dist && cp -r dist/ bootstrap-$npm_package_version-dist && zip -r9 bootstrap-$npm_package_version-dist.zip bootstrap-$npm_package_version-dist && rm -rf bootstrap-$npm_package_version-dist\"", "release-zip-examples": "node build/zip-examples.js", "dist": "npm-run-all --aggregate-output --parallel css js", "test": "npm-run-all lint dist js-test docs-build docs-lint", "netlify": "cross-env-shell HUGO_BASEURL=$DEPLOY_PRIME_URL npm-run-all dist release-sri docs-build", "watch": "npm-run-all --parallel watch-*", "watch-css-main": "nodemon --watch scss/ --ext scss --exec \"npm-run-all css-lint css-compile css-prefix\"", "watch-css-dist": "nodemon --watch dist/css/ --ext css --ignore \"dist/css/*.rtl.*\" --exec \"npm run css-rtl\"", "watch-css-docs": "nodemon --watch site/assets/scss/ --ext scss --exec \"npm run css-lint\"", "watch-js-main": "nodemon --watch js/src/ --ext js --exec \"npm-run-all js-lint js-compile\"", "watch-js-docs": "nodemon --watch site/assets/js/ --ext js --exec \"npm run js-lint\""}, "peerDependencies": {"@popperjs/core": "^2.11.6"}, "devDependencies": {"@babel/cli": "^7.19.3", "@babel/core": "^7.19.3", "@babel/preset-env": "^7.19.3", "@popperjs/core": "^2.11.6", "@rollup/plugin-babel": "^5.3.1", "@rollup/plugin-commonjs": "^22.0.2", "@rollup/plugin-node-resolve": "^14.1.0", "@rollup/plugin-replace": "^4.0.0", "autoprefixer": "^10.4.12", "bundlewatch": "^0.3.3", "clean-css-cli": "^5.6.1", "cross-env": "^7.0.3", "eslint": "^8.24.0", "eslint-config-xo": "^0.42.0", "eslint-plugin-html": "^7.1.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-markdown": "^3.0.0", "eslint-plugin-unicorn": "^44.0.0", "find-unused-sass-variables": "^4.0.4", "globby": "^11.1.0", "hammer-simulator": "0.0.1", "hugo-bin": "^0.92.2", "ip": "^2.0.0", "jquery": "^3.6.1", "karma": "^6.4.1", "karma-browserstack-launcher": "1.4.0", "karma-chrome-launcher": "^3.1.1", "karma-coverage-istanbul-reporter": "^3.0.3", "karma-detect-browsers": "^2.3.3", "karma-firefox-launcher": "^2.1.2", "karma-jasmine": "^5.1.0", "karma-jasmine-html-reporter": "^2.0.0", "karma-rollup-preprocessor": "7.0.7", "lockfile-lint": "^4.9.5", "nodemon": "^2.0.20", "npm-run-all": "^4.1.5", "postcss": "^8.4.17", "postcss-cli": "^10.0.0", "rollup": "^2.79.1", "rollup-plugin-istanbul": "^3.0.0", "rtlcss": "^4.0.0", "sass": "^1.55.0", "shelljs": "^0.8.5", "stylelint": "^14.13.0", "stylelint-config-twbs-bootstrap": "^6.0.0", "terser": "^5.15.0", "vnu-jar": "22.9.29"}, "files": ["dist/{css,js}/*.{css,js,map}", "js/{src,dist}/**/*.{js,map}", "scss/**/*.scss"], "hugo-bin": {"buildTags": "extended"}, "jspm": {"registry": "npm", "main": "js/bootstrap", "directories": {"lib": "dist"}, "shim": {"js/bootstrap": {"deps": ["@popperjs/core"]}}, "dependencies": {}, "peerDependencies": {"@popperjs/core": "^2.11.6"}}}