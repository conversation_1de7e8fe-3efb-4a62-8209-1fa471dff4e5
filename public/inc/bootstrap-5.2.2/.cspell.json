{"version": "0.2", "words": ["affordance", "allowfullscreen", "Analyser", "autohide", "autohiding", "autoplay", "autoplays", "blazingly", "Blockquotes", "Bootstrappers", "borderless", "<PERSON><PERSON><PERSON>", "browserslist", "browserslistrc", "bt<PERSON><PERSON>", "btnradio", "callout", "callouts", "camelCase", "clearfix", "Codesniffer", "combinator", "Contentful", "Cpath", "Crossfade", "crossfading", "cssgrid", "Csvg", "Datalists", "<PERSON><PERSON>", "discoverability", "docsearch", "docsref", "dropend", "dropleft", "dropright", "dropstart", "dropup", "errorf", "favicon", "favicons", "fieldsets", "flexbox", "fullscreen", "getbootstrap", "Grayscale", "Hoverable", "hreflang", "hstack", "importmap", "js<PERSON><PERSON><PERSON>", "Jumpstart", "keyframes", "libera", "libman", "<PERSON><PERSON><PERSON>", "lightboxes", "Lowercased", "markdownify", "mediaqueries", "minifiers", "misfunction", "mkdir", "monospace", "mouseleave", "navbars", "navs", "Neue", "noindex", "Noto", "offcanvas", "<PERSON>canvas<PERSON>", "Packagist", "popper<PERSON>s", "prebuild", "prefersreducedmotion", "prepended", "printf", "rects", "relref", "rgba", "roboto", "RTLCSS", "ruleset", "screenreaders", "scrollbars", "scrollspy", "Segoe", "semibold", "socio", "srcset", "stackblitz", "stickied", "Stylelint", "subnav", "tabbable", "textareas", "toggleable", "topbar", "touchend", "twbs", "unitless", "unstylable", "unstyled", "Uppercased", "urlize", "vbtn", "viewports", "Vite", "vstack", "walkthroughs", "WCAG", "zindex"], "language": "en-US", "files": ["**/*.md"], "ignorePaths": [".cspell.json", "dist/", "*.min.*", "**/*rtl*", "**/tests/**"], "useGitignore": true}