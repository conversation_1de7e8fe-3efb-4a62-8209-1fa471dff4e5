name: BrowserStack

on:
  push:
  workflow_dispatch:

env:
  FORCE_COLOR: 2
  NODE: 16

jobs:
  browserstack:
    runs-on: ubuntu-latest
    if: github.repository == 'twbs/bootstrap' && (!contains(github.event.commits[0].message, '[ci skip]') && !contains(github.event.commits[0].message, '[skip ci]'))
    timeout-minutes: 30

    steps:
      - name: Clone repository
        uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: "${{ env.NODE }}"
          cache: npm

      - name: Install npm dependencies
        run: npm ci

      - name: Run dist
        run: npm run dist

      - name: Run BrowserStack tests
        run: npm run js-test-cloud
        env:
          BROWSER_STACK_ACCESS_KEY: "${{ secrets.BROWSER_STACK_ACCESS_KEY }}"
          BROWSER_STACK_USERNAME: "${{ secrets.BROWSER_STACK_USERNAME }}"
          GITHUB_SHA: "${{ github.sha }}"
