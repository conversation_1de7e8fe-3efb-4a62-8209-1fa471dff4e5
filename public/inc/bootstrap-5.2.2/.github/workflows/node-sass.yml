name: CSS (node-sass)

on:
  push:
    branches-ignore:
      - "dependabot/**"
  pull_request:
  workflow_dispatch:

env:
  FORCE_COLOR: 2
  NODE: 16

jobs:
  css:
    runs-on: ubuntu-latest

    steps:
      - name: Clone repository
        uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: "${{ env.NODE }}"

      - name: Build CSS with node-sass
        run: |
          npx --package node-sass@latest node-sass --version
          npx --package node-sass@latest node-sass --output-style expanded --source-map true --source-map-contents true --precision 6 scss/ -o dist-sass/css/
          ls -Al dist-sass/css
