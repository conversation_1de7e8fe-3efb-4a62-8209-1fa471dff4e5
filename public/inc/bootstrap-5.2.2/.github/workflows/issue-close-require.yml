name: Close Issue Awaiting Reply

on:
  schedule:
    - cron: "0 0 * * *"

jobs:
  issue-close-require:
    runs-on: ubuntu-latest
    if: github.repository == 'twbs/bootstrap'
    steps:
      - name: awaiting reply
        uses: actions-cool/issues-helper@v3
        with:
          actions: "close-issues"
          labels: "awaiting-reply"
          inactive-day: 14
          body: |
            As the issue was labeled with `awaiting-reply`, but there has been no response in 14 days, this issue will be closed. If you have any questions, you can comment/reply.
