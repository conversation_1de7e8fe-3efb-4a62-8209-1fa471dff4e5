languageCode:           "en"
title:                  "Bootstrap"
baseURL:                "https://getbootstrap.com"

security:
  enableInlineShortcodes: true
  funcs:
    getenv:
      - ^HUGO_
      - NETLIFY

markup:
  goldmark:
    renderer:
      unsafe:           true
  highlight:
    noClasses:          false
  tableOfContents:
    startLevel:         2
    endLevel:           6

buildDrafts:            true
buildFuture:            true

enableRobotsTXT:        true
metaDataFormat:         "yaml"
disableKinds:           ["404", "taxonomy", "term", "RSS"]

publishDir:             "_site"

module:
  mounts:
    - source:           dist
      target:           static/docs/5.2/dist
    - source:           site/assets
      target:           assets
    - source:           site/content
      target:           content
    - source:           site/data
      target:           data
    - source:           site/layouts
      target:           layouts
    - source:           site/static
      target:           static
    - source:           site/static/docs/5.2/assets/img/favicons/apple-touch-icon.png
      target:           static/apple-touch-icon.png
    - source:           site/static/docs/5.2/assets/img/favicons/favicon.ico
      target:           static/favicon.ico

params:
  subtitle:             "The most popular HTML, CSS, and JS library in the world."
  description:          "Powerful, extensible, and feature-packed frontend toolkit. Build and customize with Sass, utilize prebuilt grid system and components, and bring projects to life with powerful JavaScript plugins."
  authors:              "Mark Otto, <PERSON> Thornton, and <PERSON>trap contributors"

  current_version:      "5.2.2"
  current_ruby_version: "5.2.2"
  docs_version:         "5.2"
  rfs_version:          "v9.0.6"
  github_org:           "https://github.com/twbs"
  repo:                 "https://github.com/twbs/bootstrap"
  twitter:              "getbootstrap"
  opencollective:       "https://opencollective.com/bootstrap"
  blog:                 "https://blog.getbootstrap.com/"
  themes:               "https://themes.getbootstrap.com/"
  icons:                "https://icons.getbootstrap.com/"
  swag:                 "https://cottonbureau.com/people/bootstrap"

  download:
    source:             "https://github.com/twbs/bootstrap/archive/v5.2.2.zip"
    dist:               "https://github.com/twbs/bootstrap/releases/download/v5.2.2/bootstrap-5.2.2-dist.zip"
    dist_examples:      "https://github.com/twbs/bootstrap/releases/download/v5.2.2/bootstrap-5.2.2-examples.zip"

  cdn:
    # See https://www.srihash.org for info on how to generate the hashes
    css:              "https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/css/bootstrap.min.css"
    css_hash:         "sha384-Zenh87qX5JnK2Jl0vWa8Ck2rdkQ2Bzep5IDxbcnCeuOxjzrPF/et3URy9Bv1WTRi"
    css_rtl:          "https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/css/bootstrap.rtl.min.css"
    css_rtl_hash:     "sha384-7mQhpDl5nRA5nY9lr8F1st2NbIly/8WqhjTp+0oFxEA/QUuvlbF6M1KXezGBh3Nb"
    js:               "https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/js/bootstrap.min.js"
    js_hash:          "sha384-IDwe1+LCz02ROU9k972gdyvl+AESN10+x7tBKgc9I5HFtuNz0wWnPclzo6p9vxnk"
    js_bundle:        "https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/js/bootstrap.bundle.min.js"
    js_bundle_hash:   "sha384-OERcA2EqjJCMA+/3y+gxIOqMEjwtxJY7qPCqsdltbNJuaOe923+mo//f6V8Qbsw3"
    popper:           "https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.6/dist/umd/popper.min.js"
    popper_hash:      "sha384-oBqDVmMz9ATKxIep9tiCxS/Z9fNfEXiDAYTujMAeBAsjFuCZSmKbSSUnQlmh/jp3"

  anchors:
    min: 2
    max: 5
