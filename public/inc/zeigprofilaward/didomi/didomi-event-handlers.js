window.didomiOnReady = window.didomiOnReady || [];

window.didomiEventListeners = window.didomiEventListeners || [];
window.didomiEventListeners.push({
    event: 'consent.changed',
    listener: (context) => {
        location.reload();
    }
});

const handleDidomiVendorContainers = (didomi) => {

    const categories = didomi.getCategories();
    const dataVendorsInDom = document.querySelectorAll('script[data-vendor][data-alert="true"]');
    /*data-cookie-alert="true"*/
    const vendors = didomi.getUserStatus().vendors.global;

    dataVendorsInDom.forEach(element => {

        if (!vendors.enabled.includes(element.dataset.vendor)) {

            const vendor = didomi.getVendorById(element.dataset.vendor);

            let parentCategoryNames = ['notwendigen Cookies'];

            if (vendor && vendor.purposeIds) {

                parentCategoryNames = [];

                for (let purposeId of vendor.purposeIds) {

                    const temp = categories.filter(category => {
                        return category.children.some(child => {
                            return child.purposeId === purposeId;
                        })
                    }).map(category => {
                        return category.name.de;
                    })
                    parentCategoryNames = [...parentCategoryNames, ...temp];

                }
            }

            const color = element.dataset.alertColor ?? 'info';

            const classes = element.dataset.alertClasses
                .split(' ')
                .map(className => className.trim())
                .filter(x => x);

            const alert = document.createElement('div');
            alert.classList.add("alert", "alert-" + color, "my-3");
            alert.classList.add(...classes);

            const text = document.createElement('p');
            text.innerHTML = `Um diesen Inhalt anzusehen, müssen Sie  die <b>${parentCategoryNames.join(', ')}</b>  akzeptieren.`;

            const btn = document.createElement('button');
            btn.classList.add('btn', 'btn-' + color);
            btn.textContent = "Cookie-Einstellungen";

            btn.onclick = () => {
                didomi.preferences.show();
            }

            alert.appendChild(text);
            alert.appendChild(btn);

            element.insertAdjacentElement("afterend", alert);
        }
    })
}

const createDidomiTags = (Didomi) => {
    const element = document.createElement('didomi');
    element.id = "didomicategories";
    const catString = Didomi.getUserStatus().purposes.global.enabled.join(",");
    element.setAttribute('categories', catString);
    document.body.prepend(element);
}


window.didomiOnReady.push((Didomi) => {
    handleDidomiVendorContainers(Didomi);
    createDidomiTags(Didomi);
});
