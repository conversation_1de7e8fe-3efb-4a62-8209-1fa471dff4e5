
(function(d, s, id) {
    var js, fjs = d.getElementsByTagName(s)[0];
    if (d.getElementById(id)) return;
    js = d.createElement(s);
    js.id = id;
    js.async = true;
    js.crossorigin = true;
    js.src = "//data-0c2107a914.profil.at/iomm/latest/bootstrap/loader.js";
    fjs.parentNode.insertBefore(js, fjs);
}(document, 'script', 'oewa2-script'));



var v_cp = 'Service/Unternehmenskommunikation/Sonstiges/award.profil.at';
 

var isMobile = window.innerWidth < 960;
var st = 'at_w_atprofil';
var sv = isMobile ? 'mo' : 'in';

var OEWA = {
    cn: "at",  // country
    cp: v_cp, // kategorienpfad
    ps: "lin",
    sc: "yes",
    st: st, //Path
    sv: sv  //Mobile?
}
function fireOEWA2(inta) {
    if (location.href.match(/[?&]oewa=false/)) {
        return;
    }
    if (typeof IOMm !== 'undefined') {
        if (window.oewa2Int) window.clearInterval(window.oewa2Int);
        //console.log("OEWA2 fired: " + inta);
        IOMm('configure', {
            st: OEWA.st,
            dn: 'data-0c2107a914.profil.at', //dn – domainServiceName
            cn: 'at', // cn – Länderkennung
            mh: 5,
        }); // Configure IOMm
        IOMm('pageview', {
            cp: OEWA.cp
        }); // Count pageview
    } else {
        if (!window.oewa2Int) {
            window.oewa2Int = window.setInterval(function() {
                fireOEWA2("RETRY");
            }, 100);
        }
    }

}

fireOEWA2();

