$danger: #CD000C;
$light: #f7f7f7;
$dark: #444444;
$secondary: #C0C0C0;

@import "../../bootstrap-5.2.2/scss/functions";
@import "../../bootstrap-5.2.2/scss/variables";
@import "../../bootstrap-5.2.2/scss/bootstrap";

@import "variables";


*.active, *:active, *.focus, *:focus {
    box-shadow: none !important;
}


html {
    font-size: $default-fontsize;
    font-weight: $default-fontweight;
    margin: 0;
    padding: 0;

    @media (min-width: $md) {
        font-size: $default-fontsize + 2;
    }
}


body {
    background-color: $white;
    color: $black;
    font-family: $default-fontfamily;
    line-height: $default-lineheight;
    overflow-y: scroll;
}

@for $i from 1 through $headlines {
    h#{$i}, .h#{$i} {
        font-weight: $default-fontweight + 300 !important;
        font-size: $headline-fontsize !important;
        font-family: $headline-fontfamily !important;
        color: $black !important;
        margin-bottom: $default-margin - 0.5 !important;
        word-break: break-word;

        @media (min-width: $md) {
            font-size: $headline-fontsize + 0.375 !important;
            margin-bottom: $default-margin - 0.25 !important;
        }

    }
    $headline-fontsize: $headline-fontsize - 0.4;
}

a {
    color: $danger;
    text-decoration: none;

    &:hover {
        text-decoration: none !important;
        color: $danger;
    }
}

.btn {
    border-top-right-radius: 0 !important;
}

/******************************************************
*********************** LAYOUT ************************
*******************************************************/
.container-fluid {
    max-width: 1400px !important;
}

.header-wrapper {

    padding: 1rem 0;

    & img {
        width: 120px;

        @media (min-width: $md) {
            width: 180px;
        }
    }
}

.intro-wrapper {

    & .intro-image {
        background-color: $black;
    }

    & .intro-subtitle {
        font-size: 1.3rem;
        font-weight: $default-fontweight + 300;
        font-family: $default-fontfamily2;
        line-height: $default-lineheight;
        margin: 0.5rem 0;
    }

    & .intro-text {
        margin-bottom: 2rem;
    }
}

.category-wrapper {
    background-color: $black;
    padding: 2rem 0;


    @for $i from 1 through $headlines {
        & h#{$i}, & .h#{$i} {
            color: $white !important;
        }
    }

    & .sixr-wrapper {
        & .sixr {

            & .sixr-image {
                background-color: $white;
                text-align: center;
                margin-bottom: 0.5rem;
            }

            & .sixr-title {
                font-size: 1.3rem;
                font-weight: $default-fontweight + 300;
                color: $white;
            }
        }
    }

}

.modal-category {

    @for $i from 1 through $headlines {
        & h#{$i}, & .h#{$i} {
            margin-bottom: 0 !important;
        }
    }

    & .category-subtitle {
        font-size: 1.5rem;
        margin-bottom: 1.5rem;
    }

    & .category-text {
        font-size: 1.1rem;
        margin-bottom: 3rem;
    }
}

.program-wrapper {
    padding: 5rem 0 4rem 0;

    & .card-program {
        border: 2px solid $black;
        border-radius: 0;

        & .card-body {
            padding: 3.5rem 2rem 2rem 2rem;

            & .card-header {
                background-color: $white;
                border: none;
                border-radius: 0;
                padding: 0.5rem 1rem;
                margin-top: -72px;
                text-wrap: nowrap;

                @media (min-width: $md) {
                    margin-top: -87px;
                    padding: 0.5rem 1.5rem;
                }
            }

            & .card-text {
                margin-top: 1rem;
            }
        }
    }
}

.partner-wrapper {
    padding: 1rem 0 5rem 0;

    & .partner-card {
        border-radius: 0;
    }
}

.footer-wrapper {
    background-color: $white !important;
    padding: 2rem 0;
    border-top: 1px solid $black;

    & img {
        width: 120px;
    }

    & .footer-links {

        & a {
            font-size: 1rem;
            padding: 0 1rem 1rem 1rem;
            color: $black;
            text-wrap: nowrap;
            display: inline-block;
        }

    }
}


.category-card {
    border: 2px solid white !important;
    border-radius: 1rem !important;

}

.voting-col{
    padding-bottom:12rem;

    @media (min-width: $md) {
        padding-bottom:8rem;
    }
}

.voting-card {
    background-color: rgba(255, 255, 255, 1) !important;
    border: 2px solid white;

    & .vote-radio {
        transform: scale(3.0);
        cursor: pointer;
    }

    & .card-header {
        background-color: transparent !important;
        border-bottom: none !important;
        padding: 1rem 1.5rem;
    }

    & .card-body {
        cursor: pointer;
    }
}

.voting-title {
    color: white;
    font-weight: bold;
    font-size: 1rem;
    background-color: black;
}



.sixr-image {
    position: relative;
    overflow: hidden;
    cursor: pointer;
}

.sixr-image .overlay-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: contain;
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
    pointer-events: none;
    z-index: 2;
}



.sixr-image:hover .overlay-image {
    opacity: 1;
}


.custom-file-input-wrapper {
    position: relative;
}

.custom-file-input {
    opacity: 0;
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
    cursor: pointer;
    z-index: 2;
}

.custom-file-display {
    position: relative;
    z-index: 1;
    pointer-events: none;
}

.form-control-file-mock {
    background-color: #fff;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    padding: 0.375rem 2.5rem 0.375rem 0.75rem; /* Platz für Icon */
    height: calc(1.5em + 0.75rem + 2px);
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #6c757d;
}

.file-upload-icon {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
    z-index: 1;
    pointer-events: none;
}


.sixr-image {
    position: relative;
}

/* Standard versteckt */
.overlay-image {
    opacity: 0;
    transition: opacity 0.2s ease;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Bei Hover anzeigen */
.sixr-image:hover .overlay-image {
    opacity: 1;
}

/* Wenn die Klasse .overlay-active gesetzt ist, ebenfalls anzeigen */
.overlay-image.overlay-active {
    opacity: 1;
}


.overlay-image {
    opacity: 0;
    transition: opacity 0.3s ease; /* Übergang hinzufügt */
    position: absolute;
    top: 0; left: 0;
    width: 100%; height: 100%;
    object-fit: cover;
}
.overlay-image.overlay-active {
    opacity: 1;
}


.image-container {
    height: 250px; /* oder was optisch gut aussieht */
    padding: 0;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.voting-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
}

#download {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-top: 20px;
}

.download-item {
    flex: 1 1 calc(33.333% - 20px); /* 3 gleich breite Spalten */
    box-sizing: border-box;
    border: 1px solid #ccc;
    padding: 10px;
    background: #f9f9f9;
}
.download-item embed {
    width: 100%;
    height: 300px;
    border: 1px solid #ccc;
}
