/******************************************************
******************** ALLGEMEIN ************************
*******************************************************/
* {
    margin: 0;
    padding: 0;
}

html {
    margin: 0;
    padding: 0;
    font-size: 16px;
}

body {
    background-color: #FFFFFF;
    font-weight: 400;
    font-family: 'PT Sans', sans-serif;
    color: #000000;
    line-height: 2rem;
    font-size: 1rem;
}

.container-fluid {
    max-width: 1200px;
}

h1, .h1 {
    font-weight: 700;
    font-family: 'PT Sans', sans-serif;
    color: #000000 !important;
    font-size: 1.8rem;
    line-height: 2.5rem;
}

a, a:hover {
    color: #000000;
    text-decoration: none !important;
}

p {
    font-size: 1.20rem;
}

.btn-dark {
    background-color: #444444 !important;
}

.btn-send {
    border-radius: 2rem;
    background-color: #444444 !important;
    padding: 1rem;
    text-transform: uppercase;
}

.range .thumb:after {
    background: #444444 !important;
}

.font-ptsans {
    font-weight: 500 !important;
    font-family: 'PT Sans', sans-serif !important;
}

.text-black {
    color: #000000 !important;
}

.bg-blue {
    background-color: #EEF8FB !important;
}

.bg-beige {
    background-color: #F1E3D7 !important;
}

.bg-tangerine {
    background-color: #D9684F !important;
}

.color-tropical {
    color: #56C3B1 !important;
}

.color-sky {
    color: #56C3B1 !important;
}

.color-whiskey {
    color: #D0A078 !important;
}

.color-tangerine {
    color: #D9684F !important;
}

.alert-secondary {
    background-color: #F1E3D7 !important;
    color: #000000 !important;
}

.form-control {
    line-height: inherit !important;
    border-color: #888888 !important;
    box-shadow: none !important;
}

#captchaReload {
    font-size: 1.2rem;
    margin-left: 0.5rem;
}

.header-logo {
    height: 60px;
    margin-top: 13px;
    margin-bottom: 13px;
}

.subheader-logo {
    height: 18px;
}

.footer-wrapper {
    padding-top: 50px;
    color: #000000 !important;
}

.footer-wrapper .nav-link {
    text-decoration: none;
    font-weight: 500;
    font-family: 'PT Sans', sans-serif;
    color: #000000 !important;
}

#optanon-cookie-policy * {
    color: #000000 !important;
}

#optanon-cookie-policy thead * {
    color: #000000 !important;
}

#optanon-cookie-policy table a {
    color: #000000 !important;
}

.optanon-toggle-display {
    cursor: pointer !important;
}


.autocomplete-custom-item-content {
    display: flex;
    flex-direction: column;
}

.autocomplete-custom-item-title {
    font-weight: 500;
}

.autocomplete-custom-item-subtitle {
    font-size: 0.8rem;
}

.autocomplete-input ~ .form-notch .form-notch-leading, .autocomplete-input ~ .form-notch .form-notch-middle, .autocomplete-input ~ .form-notch .form-notch-trailing,
.autocomplete-input.focused ~ .form-notch .form-notch-leading, .autocomplete-input.focused ~ .form-notch .form-notch-middle, .autocomplete-input.focused ~ .form-notch .form-notch-trailing {
    border-color: #BBBBBB !important;
}

input[type=range]::-webkit-slider-thumb {
    background-color: #D9684F !important;
}


/************** SELECT2 ****************/
.select2 {
    display: block !important;
    max-width: 100%;
}

.select2-container .select2-selection--single {
    height: calc(2.25rem + 12px);
    padding-top: 10px;
}

.select2-container--default .select2-results > .select2-results__options {
    max-height: 300px;
}

.select2-container .select2-selection--single .select2-selection__rendered {
    padding-left: 1rem;
    padding-right: 2.5rem;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
    top: 11px;
    right: 11px;
}

.select2-container--default .select2-selection--single .select2-selection__arrow b {
    border-width: 7px 6px 0 6px;
}

.select2-container--default.select2-container--open .select2-selection--single .select2-selection__arrow b {
    border-width: 0 6px 7px 6px;
}

.select2-container--default .select2-selection--single .select2-selection__clear {
    font-size: 1.5rem !important;
    color: #888888 !important;
}

.select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: #F5F5F5;
    color: #000000;
}

.bg-img-wirtshaus, .bg-img-gerichte {
    background-repeat: no-repeat;
    background-size: cover;
    position: relative;
    width: 100%;
    height: 100%;
}

.bg-img-wirtshaus {
    background-image: url("../../img/freizeitvoting/wirtshaus.JPG");
    background-position: top right;
}

.bg-img-gerichte {
    background-image: url("../../img/freizeitvoting/gerichte.JPG");
    background-position: center center;
}

.bg-img-polygon {
    position: absolute;
    content: "";
    width: 6.5rem;
    height: 100%;
    -webkit-clip-path: polygon(100% 0, 0% 100%, 100% 100%);
    clip-path: polygon(100% 0, 0% 100%, 100% 100%);
    right: -1px;
    top: 1px;
    z-index: 1;
}
