$(document).ready(function(){


    $.ajaxSetup({ headers : { 'X-CSRF-TOKEN' : $('meta[name="_token"]').attr('content') } });

    $('[id^=form_]').on('submit', function(event){
        event.preventDefault();
        var form = $(this);
        var category_id = form.closest('form').data('category');
        var project_id = form.find('input[name=vote]:checked').val();

        //console.log(project_id);
        var solution = form.find('#solution').val();

        if(category_id && project_id){
            $.ajax({
                       url : baseurl + '/voten',
                       method : 'POST',
                       data : {

                           'category_id' : Number(category_id),
                           'project_id' : Number(project_id),
                           'solution' : solution
                       },
                       success : function(response){
                           if(response.success == true){
                               $('#vote_' + response.category).children().fadeOut(50, function(){
                                   $(this).remove();
                               });
                               var newContent = $('<div class="alert alert-success mb-0 text-center"><PERSON><PERSON> für deine <PERSON>imme!</div>').hide();
                               $('#vote_' + response.category).append(newContent);
                               newContent.fadeIn(200);
                               form.find("#solution").remove();
                           }
                       },
                   });
        }


    });

    $('[id^=juryform_]').on('submit', function(event){
        event.preventDefault();
        var form = $(this);
        var category_id = form.closest('form').data('category');
        var project_id = form.find('input[name=vote]:checked').val();
        var hash = form.find('input[name=hash]').val();


        //console.log(project_id);
        var solution = form.find('#solution').val();

        if(category_id && project_id && hash){
            $.ajax({
                       url : baseurl + '/jury_voten',
                       method : 'POST',
                       data : {

                           'category_id' : Number(category_id),
                           'project_id' : Number(project_id),
                           'hash' : hash,
                           'solution' : solution
                       },
                       success : function(response){
                           if(response.success == true){
                               $('#vote_' + response.category).children().fadeOut(50, function(){
                                   $(this).remove();
                               });
                               var newContent = $('<div class="alert alert-success mb-0 text-center">Danke für deine Stimme!</div>').hide();
                               $('#vote_' + response.category).append(newContent);
                               newContent.fadeIn(200);
                               form.find("#solution").remove();
                           }
                       },
                   });
        }


    });

    $('.voteButton').prop('disabled', true);

    $('input[type="radio"]').change(function(){
        let form = $(this).closest('form');

        if(form.find('input[type="radio"]:checked').length > 0){
            form.find('.voteButton').prop('disabled', false);
        }else{
            form.find('.voteButton').prop('disabled', true);
        }
    });

    $('.scroll-link').on('click', function(e){
        // Ziel auswählen
        var target = $(this).data('target');

        // Scrollen zum Ziel
        $('html, body').animate({
                                    scrollTop : $(target).offset().top
                                }, 50);
    });

    $('[id^=modalbutton_]').on('click', function(element){



        var firma = $(this).data('firma');
        var id = $(this).data('id');
        var vorname = $(this).data('vorname');
        var nachname = $(this).data('nachname');
        var statement = $(this).data('statement');
        var claim = $(this).data('claim');
        var goal = $(this).data('goal');
        var file = $(this).data('file');
        var logo = $(this).data('logo');
        var company = $(this).data('company');
        var project = $(this).data('project');
        var skizze = $(this).data('skizze');
        var phase = $(this).data('phase');

        var path = baseurl + '/storage/app/public/zeigprofilaward/submits';

        $('#firmenname').html("");
        $('#name').html("");
        $('#statement').html("");
        $('#goal').html("");
        $('#claim').html("");
        $('#logo').html("");
        $('#project_picture').html("");
        $('#company_picture').html("");


        $('#firmenname').html('<h1><strong>' + firma + '</strong></h1>');
        $('#name').html(nachname + ' ' + vorname);
        $('#statement').html(statement);
        $('#goal').html(goal);
        $('#claim').html(claim);



        var files = [
            { name: 'Logo', file: logo },
            { name: 'Maßnahmen und Umsetzung', file: company },
            { name: 'Unternehmenspräsentation', file: project },
            { name: 'Referenzen', file: skizze }
        ];

        console.log(files);
        $('#download').html(''); // div leeren

        files.forEach(function(item) {
            if (item.file && item.file.endsWith('.pdf')) {
                var fileUrl = path + '/' + id + '/' + item.file;


                var html = `
            <div style="margin-bottom: 20px;">
                <strong>${item.name}</strong><br>
                <embed src="${fileUrl}" type="application/pdf" width="100%" height="300px" style="border:1px solid #ccc;"/>
                <br>
                <a href="${fileUrl}" download class="btn btn-sm btn-primary mt-2">Herunterladen</a>
            </div>
        `;

                $('#download').append(html);
            }
        });

        if(file != ""){
            if(phase == 3){
                if(logo != ""){
                    $('#logo').html("<img width='100%' style='border: solid black 1pt; padding:3%' alt='Logo' src='" + path + "/" + id + "/" + logo + "'>");
                }

            }else{
                if(logo != ""){
                    $('#logo').html("<img width='100%' style='border: solid black 1pt; padding:3%' alt='Logo' src='" + path + "/" + id + "/" + logo + "'>");
                }

            }

        }

        $('#projektmodal').modal('show');
    });


    $('button[id^="toggleRow_"]').on('click', function() {
        // ID des Buttons auslesen
        var categoryId = $(this).attr('id').split('_')[1];

        // Die dazugehörige Row mit derselben ID finden
        var detailsRow = $('#detailsRow_' + categoryId);

        // Klassen umschalten: d-none auf d-block setzen oder umgekehrt
        if (detailsRow.hasClass('d-none')) {
            detailsRow.removeClass('d-none').addClass('d-flex');
            $(this.innerHTML="Projekte ausblenden");
        } else {
            detailsRow.removeClass('d-flex').addClass('d-none');
            $(this.innerHTML="Projekte einblenden");
        }
    });


});


/*
                if(project != ''){
                    $('#project_picture').html("<img width='100%' alt='Logo' src='" + path + "/" + id + "/" + project + "'>");
                }else{
                    if(company != ''){
                        $('#project_picture').html("<img width='100%' alt='Logo' src='" + path + "/" + id + "/" + company + "'>");
                    }
                }
                if(skizze != ''){
                    $('#download').html("<a type=\"button\" class=\"float-end mt-1\" href=\"" + baseurl + "/download/" + id + "\"> <i class=\"fa-solid fa-folder-arrow-down text-danger fa-2x\"></i> </a>");
                }*/

/* if(project != ''){
                    $('#project_picture').html("<img width='100%' alt='Logo' src='" + path + "/" + id + "/" + project + "'>");
                }
                if(company != ''){
                    $('#company_picture').html("<img width='100%' alt='Logo' src='" + path + "/" + id + "/" + company + "'>");
                }
                if(skizze != ''){
                    $('#download').html("<a type=\"button\" class=\"float-end mt-1\" href=\"" + baseurl + "/download/" + id + "\"> <i class=\"fa-solid fa-folder-arrow-down text-danger fa-2x\"></i> </a>");
                }*/
