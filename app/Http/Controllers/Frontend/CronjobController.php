<?php

namespace App\Http\Controllers\Frontend;

use App\Facades\Email;
use App\Http\Controllers\Controller;
use App\Models\Errorlog;
use App\Models\Mediathek\MediathekEpaper as Epaper;
use App\Models\Mediathek\MediathekShortlabel;
use App\MPClasses\RecruitingAppService;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use PharIo\Version\Exception;


class CronjobController extends Controller
{
    private RecruitingAppService $recruitingAppService;

    public function __construct(RecruitingAppService $recruitingAppService)
    {
        $this->recruitingAppService = $recruitingAppService;
    }
    public function umantisJobsUpdate()
    {

        $this->recruitingAppService->update();

        return response("Done");
    }



    public function createReplaceMediathekCovers()
    {

        if (env('APP_ENV') === 'production') dd('This job can run only on DEV');

        $replaced = [];
        $fileNotFound = [];


        set_time_limit(21900);
        /*
         * Cronjob between these dates
         */
        $startDate = today()->subDays(5);
        $endDate = now()->addDays(5);
        /*
         * local C:\web\www7\mediaprint\storage\app\public\epaper\imported
         */
        foreach (Storage::directories("public/epaper/imported") as $dateDirPath) { /*["public/epaper/imported/20231124"]*/
            /*
             * remove path, keep dir name
             * public/epaper/imported/20231124 -> 20231124
             */
            $dateDir = basename($dateDirPath);

            /*
             * Only DIRs with date-name-format
             */
            if (!is_numeric($dateDir)) continue;
            /*
             * transform DIR in a date
             */
            $currDate = Carbon::parse($dateDir);
            /*
             * only between dates
             */
            if (!($currDate->isBetween($startDate, $endDate))) continue;

            /*
             * getting through the content of the Date dir folder  es. krone/ kurier
             */
            foreach (Storage::directories($dateDirPath) as $objDirPath) { /*["public/epaper/imported/20231124/krone"]*/
                /*
                 * public/epaper/imported/20231124/krone -> krone
                 */
                $mandant = $this->getMediathekObject(basename($objDirPath));

                if (!$mandant) {
                    $msg = "Für folgendes Keyword wurde keinen Mandant gefunden:<br><b>Keyword:</b> " . basename($objDirPath) . "<hr> Mediaprint.at <br> class function ->/ " . __CLASS__ . __FUNCTION__;
                    Email::newRawMail(env("MAIL_EMPFAENGER_IT"), "intern", "Mediathek - keinen Mandant gefunden - " . basename($objDirPath), $msg);
                }

                $currObjectName = $mandant['name'];

                foreach (Storage::directories($objDirPath) as $ausgabeDir) {

                    $currAusgabeShortLabel = basename($ausgabeDir);
                    $importedEpaperPath = "imported/" . $dateDir . "/" . $currObjectName . "/" . $currAusgabeShortLabel;
                    $coverImageName = $currAusgabeShortLabel . "_" . $dateDir . "_001.jpg";


                    $sourceImage = "storage/app/public/epaper/" . $importedEpaperPath . "/" . $coverImageName;
                    $previewImage = "storage/app/public/epaper/" . $importedEpaperPath . "/mediathek.jpg";


                    if (is_file($sourceImage)) {
                        $replaced[] = $sourceImage;
                        exec(env("PATH_TO_IMAGEMAGICK") . " convert -resize 400 -quality 20 " . $sourceImage . " " . $previewImage);

                    } else {
                        $fileNotFound[] = $sourceImage;

                    }

                }
            }

        }


        if (env('APP_ENV') == 'local') return response()->json(['replaced covers' => $replaced, 'not replaceable covers' => $fileNotFound]);

        return response()->json(['replaced covers' => count($replaced), 'not replaceable covers' => count($fileNotFound)]);


    }


    private function getMediathekObject($search): ?array
    {

        $objects = collect([
            [
                "id" => 0,
                "name" => "bazar"
            ],
            [
                "id" => 1,
                "name" => "krone"
            ],
            [
                "id" => 2,
                "name" => "kurier"
            ],
            [
                "id" => 3,
                "name" => "profil"
            ],
            [
                "id" => 4,
                "name" => "gesleb"
            ],
        ]);

        return $objects->first(
            function ($item) use ($search) {
                return $item['name'] === $search || ($item['id'] === $search);
            }
        );
    }


    //Mediathek
    public function importer($param = "daily")
    {
        /*
         * Array of new labels
         * if new one were found an email will be sent to env("MAIL_SHORTLABEL_MISSING")
         */
        $newAddedShortLabel = [];

        set_time_limit(21900);
        /*
         * local C:\web\www7\mediaprint\storage\app\public\epaper\imported
         * all relevant directories have a date as name like 20231201
         */
        $dateDirectories = Storage::directories("public/epaper/imported");
        /*
         * Cronjob between these dates
         */
        $startDate = today()->subDays(5);
        $endDate = now()->addDays(5);
        if ($param == "all") $startDate = Carbon::parse("20120101");
        if ($param == "month") $startDate = today()->subMonth();

        /*
         * Exact day mode
         * making sure it is  a date
         */
        if (is_numeric($param) && Str::length($param) === 8) {
            try {
                $startDate = Carbon::parse($param);
                $endDate = $startDate;
            } catch (Exception $e) {
                abort(400);
            }
        }


        foreach ($dateDirectories as $dateDirPath) {
            /*
             * remove path, keep dir name
             * public/epaper/imported/20231124 -> 20231124
             */
            $dateDir = basename($dateDirPath);
            /*
             * Only DIRs with date-name-format
             */
            if (!is_numeric($dateDir)) continue;
            /*
             * transform DIR in a date
             */
            $currDate = Carbon::parse($dateDir);
            /*
             * only between dates
             */
            if (!($currDate->isBetween($startDate, $endDate))) continue;
            /*
             * getting through the content of the Date dir folder  es. krone/ kurier
             */
            foreach (Storage::directories($dateDirPath) as $objDirPath) {
                /*
                 * public/epaper/imported/20231124/krone -> krone
                 */
                $objectDir = basename($objDirPath);

                $mandant = $this->getMediathekObject($objectDir);

                if (!$mandant) {
                    $msg = "Für folgendes Keyword wurde keinen Mandant gefunden:<br><b>Keyword:</b> " . $objectDir . "<hr> Mediaprint.at <br> class function ->/ " . __CLASS__ . __FUNCTION__;
                    Email::newRawMail(env("MAIL_EMPFAENGER_IT"), "intern", "Mediathek - keinen Mandant gefunden - " . $objectDir, $msg);
                }

                $currObjectName = $mandant['name'];
                $currObject = $mandant['id'];

                /*
                 * folder content  es. bu, lw, st, epkrone
                 */
                $ausgabeDirectories = Storage::directories($objDirPath);


                foreach ($ausgabeDirectories as $ausgabeDir) { // foreach ausgabe ["public/epaper/imported/20231123/krone/bu", ".."]

                    //public/epaper/imported/20231124/krone/lw -> lw

                    $currAusgabeShortLabel = basename($ausgabeDir);

                    /*
                     * ShortLabel is needed for  Updating the Ebook description
                     */
                    $mediathekShortLabel = MediathekShortlabel::where("object", $currObject)->where("shortlabel", $currAusgabeShortLabel)->first();
                    /*
                     *  Create Short label if it doesn't exist yet
                     */
                    if (!$mediathekShortLabel) {
                        $mediathekShortLabel = MediathekShortlabel::create([
                            "object" => $currObject,
                            "shortlabel" => $currAusgabeShortLabel,
                            "description" => $currAusgabeShortLabel . " (?)"
                        ]);

                        $newAddedShortLabel[] = $currAusgabeShortLabel;
                    }
                    /*
                     * reading the name from the xml to make sure
                     */
                    $name_from_xml = null;
                    foreach (Storage::files($ausgabeDir) as $file) {
                        if (pathinfo($file, PATHINFO_EXTENSION) === "xml"):
                            $file = @file_get_contents('storage/app/' . $file);
                            if ($file) {
                                $xml = simplexml_load_string($file);
                                $name_from_xml = (string)$xml->name;
                                unset($xml);
                            }
                            break;
                        endif;
                    }


                    // mediaprint.at/storage/app/public/epaper/  -> imported/20231127/krone/lw <-
                    $importedEpaperPath = "imported/" . $dateDir . "/" . $currObjectName . "/" . $currAusgabeShortLabel;


                    $existingEPaper = Epaper::withTrashed()->where("path", $importedEpaperPath)->first();

                    $ePaperData =     [
                        "publication_date" => $currDate->format("Y-m-d"),
                        "online_at" => $currDate->format("Y-m-d H:i:s"),
                        "ausgabe" => $mediathekShortLabel->id,
                        "object" => $currObject,
                        "wasImported" => 1,
                        "description" => $mediathekShortLabel->description,
                        "shortlabel_id" => $mediathekShortLabel->id,
                        "hash" => md5($dateDir . $currObjectName . $currAusgabeShortLabel),
                        "fulltext" => $currDate->format("d.m.Y") . " " . $currObjectName . " " . $currAusgabeShortLabel . " " . $mediathekShortLabel->description,
                        "name" => $name_from_xml,
                        "path" => $importedEpaperPath,
                    ];

                    if(!$existingEPaper) {
                        Epaper::create($ePaperData);
                    }else{
                        $existingEPaper->update($ePaperData);
                    }



                    // "storage/app/public/epaper/imported/" . $dirName . "/" . $currObjectName . "/" . $currAusgabeShortlabel . "/" . $currAusgabeShortlabel . "_" . $dirName . "_001.jpg";

                    $coverImageName = $currAusgabeShortLabel . "_" . $dateDir . "_001.jpg";
                    $sourceImage = "storage/app/public/epaper/" . $importedEpaperPath . "/" . $coverImageName;

                    // "storage/app/public/epaper/imported/" . $dirName . "/" . $currObjectName . "/" . $currAusgabeShortlabel . "/mediathek.jpg";
                    $previewImage = "storage/app/public/epaper/" . $importedEpaperPath . "/mediathek.jpg";
                    if (is_file($sourceImage) && env('APP_ENV') != 'production') {
                        exec(env("PATH_TO_IMAGEMAGICK") . " convert -resize 400 -quality 20 " . $sourceImage . " " . $previewImage);
                        echo "<div> $previewImage erstellt</div>";
                    }
                    flush();
                }
            }

        }

        if (!empty($newAddedShortLabel)) {
            $msg = "Für folgendes Kürzel wurde keine Beschreibung gefunden:
				<br>
				<br><b>Kürzel:</b> " . join(", ", $newAddedShortLabel) . "
				<br>
				<hr>
				Dies ist eine automatisierte Info-Mail - Keine Rückmeldung möglich";
            Email::newRawMail(env("MAIL_SHORTLABEL_MISSING"), "intern", "[SMARTSALES MEDIATHEK] Fehlendes Kürzel bei Importer", $msg);
        }


        return response('importer ok!');
    }


    //Mediathek
    public function syncFilter($param = "week")
    {

        set_time_limit(21900);

        $notFound = [];


        if ($param == "week") {
            $from = now()->subWeek();
        } elseif ($param == "month") {
            $from = now()->subMonth();
        } else {
            $from = now()->subYears(2);
        }

        $ePapers = Epaper::where("wasImported", 1)->orderBy("publication_date", "desc")->whereDate("publication_date", ">", $from)->get();

        foreach ($ePapers as $epaper) {
            // return $this->mediathek_epaper_json();
            $isFiltered = false;
            // Retrieve the short label for the current epaper
            $shortLabel = $epaper->shortlabel->shortlabel;

            /**
             * KRONE
             * Vorteilswelt Krone
             */
            if (Str::contains($shortLabel, 'krbonus') && $epaper->object == 1) {
                $isFiltered = true;
                $epaper->bookmarks()->syncWithoutDetaching([1, 101]);
            }
            //Krone = 1, Tageszeitungen (Krone) = 62
            $tmpArr = ['ab', 'wr', 'lw', 'lwnord', 'lwsued', 'lwmitte', 'lwwest', 'ni', 'niwus', 'niwald', 'niwein', 'niwest', 'bu', 'bunord', 'busüd', 'st', 'stgr', 'stenns', 'stmule', 'stso', 'stsw', 'kt', 'ktuk', 'ktkl', 'oo', 'oolz', 'ootr', 'oohi', 'sb', 'ti', 'vb'];
            if (in_array($shortLabel, $tmpArr) && $epaper->object == 1) {
                $isFiltered = true;
                $epaper->bookmarks()->syncWithoutDetaching([1, 62]);
            }


            //Krone = 1, KroneTV = 2
            $tmpArr = ["krgestv", "krstatv", "krnoetv", "krktntv", "krwietv", "krtirjtv"];
            if (in_array($shortLabel, $tmpArr)) {
                $isFiltered = true;
                $epaper->bookmarks()->syncWithoutDetaching([1, 2]);
            }

            //Krone = 1, KroneBunt = 4
            $tmpArr = ["bunt", "buntm"];
            if (in_array($shortLabel, $tmpArr)) {
                $isFiltered = true;
                $epaper->bookmarks()->syncWithoutDetaching([1, 4]);
            }


            //Krone = 1, Magazine = 69
            $tmpArr = ["krjverlag"];
            if (in_array($shortLabel, $tmpArr)) {
                $isFiltered = true;
                $epaper->bookmarks()->syncWithoutDetaching([1, 69]);
            }

            //Krone = 1, Wohnkrone = 68
            if (substr($shortLabel, 0, 5) == "krwoh") {
                $isFiltered = true;
                $epaper->bookmarks()->syncWithoutDetaching([1, 68]);
            }


            //Krone = 1, JOURNALE = 5
            if ((substr($shortLabel, 0, 2) == "kr" && substr($shortLabel, 5, 1) == "j") || strpos($shortLabel, "pan")) {
                $isFiltered = true;
                $epaper->bookmarks()->syncWithoutDetaching([1, 5]);
            }

            //Krone = 1, Wirtschaft = 98
            if ($shortLabel == "wima") {
                $isFiltered = true;
                $epaper->bookmarks()->syncWithoutDetaching([1, 98]);
            }


            //Krone = 1, Rätsel/Sudoku = 60
            if (substr($shortLabel, 0, 7) == "raetsel") {
                $isFiltered = true;
                $epaper->bookmarks()->syncWithoutDetaching([1, 60]);
            }

            //Gesuenderleben = 93
            if ($shortLabel == "gesleb") {
                $isFiltered = true;
                $epaper->bookmarks()->syncWithoutDetaching([93]);
            }

            //Gesuenderleben = 93
            if (substr($shortLabel, 0, 6) == "profil") {
                $isFiltered = true;
                $epaper->bookmarks()->syncWithoutDetaching([100]);
            }

            /**
             * KURIER
             */


            /*
             * Vorteilswelt KURIER
             */
            if ($epaper->name === "clubnews" && $epaper->object == 2) {
                $isFiltered = true;
                $epaper->bookmarks()->syncWithoutDetaching([1, 102]);
            }


            //KURIER = 3, TZ = 63
            $tmpArr = ['kt', 'a', 'wm', 'wm2', 'n', 'n2', 'ni', 'nm', 'nw', 'nö', 'noe', 'b', 'b2', 'l', 'l2', 'l3', 'l4', 'ooe', 'ooe2'];
            if (in_array($shortLabel, $tmpArr) && $epaper->object == 2) {
                $isFiltered = true;
                $epaper->bookmarks()->syncWithoutDetaching([3, 63]);
            }

            //KURIER = 3, KurierTV = 65
            $tmpArr = ["ttv", "ttv2"];
            if (in_array($shortLabel, $tmpArr)) {
                $isFiltered = true;
                $epaper->bookmarks()->syncWithoutDetaching([3, 65]);
            }

            //KURIER = 3, Freizeit = 64
            $tmpArr = ["freizeit", "freizeitm", "xfreizeit"];
            if (in_array($shortLabel, $tmpArr)) {
                $isFiltered = true;
                $epaper->bookmarks()->syncWithoutDetaching([3, 64]);
            }

            //KURIER = 3, IMMO Kurier = 67
            if (substr($shortLabel, 0, 4) == "immo") {
                $isFiltered = true;
                $epaper->bookmarks()->syncWithoutDetaching([3, 67]);
            }

            //KURIER = 3, JOURNALE = 66
            if (substr($shortLabel, 0, 2) == "ku" && substr($shortLabel, 2, 1) == "j") {
                $isFiltered = true;
                $epaper->bookmarks()->syncWithoutDetaching([3, 66]);
            }

            //KURIER = 3, JOURNALE = 66
            if (strpos($shortLabel, "themenwoche") && $epaper->object == 2) {
                $isFiltered = true;
                $epaper->bookmarks()->syncWithoutDetaching([3, 66]);
            }

            //KURIER = 3, MAGAZINE = 70
            if (strpos($shortLabel, "agazin") && $epaper->object == 2) {
                $isFiltered = true;
                $epaper->bookmarks()->syncWithoutDetaching([3, 70]);
            }


            //Profil = 71
            if (strpos($shortLabel, "profil") && $epaper->object == 3) {
                $isFiltered = true;
                $epaper->bookmarks()->syncWithoutDetaching([71]);
            }

            /**
             * MP
             */

            //MP = 1+3, Journale = 5+66
            if (substr($shortLabel, 0, 2) == "mp" && substr($shortLabel, 5, 1) == "j") {
                $isFiltered = true;
                $epaper->bookmarks()->syncWithoutDetaching([1, 3, 5, 66]);
            }

            if (!$isFiltered) {
                $notFound[] = (($epaper->shortlabel->object == 1) ? "KR" : 'KU') . " - " . $shortLabel;
            }
        }

        echo "<h2>NOT FOUND LIST: </h2><li>" . join("<li>", $notFound);
        echo "0";
        echo '<br>done';


        if (env('APP_ENV') == 'local') {
            return '<div> mediathek_epaper_json() not called because APP_ENV === local</div>';
        }

        return response("____________________________ -> OK, done! <- ____________________________ ");

    }





	public function sendErrormail(){

		set_time_limit(300);

		try{

			$errorlogs = ErrorLog::where("sent", 0)
			                     ->where("send_out_at", "<", now())
			                     ->orderBy("created_at", "desc")
			                     ->get();

			$errorCount = $errorlogs->count();
			$lastKey = $errorlogs->keys()->last();

			if($errorlogs->isNotEmpty()){
				$body = "";
				foreach($errorlogs as $i => $errorlog){
					$body .= "{$errorlog->created_at->format("H:i:s, d.m.Y ")}<br>";
					$body .= "<pre>".json_encode(json_decode($errorlog->error), JSON_UNESCAPED_SLASHES|JSON_PRETTY_PRINT)."</pre>";
					if($errorCount > 0 && $i != $lastKey) $body .= "<br><hr><br>";
					$errorlog->update(["sent" => 1]);
				}

				//trim
				$body = Str::limit($body, 100000, '<br><br><b>Wurde gekürzt, da mehr als 100.000 Zeichen ...</b>');

				//mail
				if(env("APP_ENV") == "production"){
					$subject = "[MEDIAPRINT] Service {$errorCount} Fehler";
					Email::newRawMail(env('MAIL_EMPFAENGER_ERROR_REPORT'), "intern", $subject, $body);

				}
			}

			return response()->json(['status' => 'success', 'message' => $errorCount." Fehler gemeldet"]);
		}catch
		(Exception $e){
			return response()->json(['status' => 'error', 'message' => $e->getMessage()], 500);
		}
	}






}
