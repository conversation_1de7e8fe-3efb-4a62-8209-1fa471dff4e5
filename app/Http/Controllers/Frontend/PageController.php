<?php

    namespace App\Http\Controllers\Frontend;

    use App\Facades\Email;
    use App\Facades\MPHelper;
    use App\Http\Controllers\Controller;
    use App\Models\Agb;
    use App\Models\AlertMessage;
    use App\Models\BrandPortfolio;
    use App\Models\Brandtag;
    use App\Models\DataPrivacy;
    use App\Models\DigiprintOpenhouseAttendee;
    use App\Models\EventRegistration;
    use App\Models\Imprint;
    use App\Models\Mediathek\MediathekEpaper as Epaper;
    use App\MPClasses\GoodCaptcha;
    use Carbon\Carbon;
    use Illuminate\Contracts\Filesystem\FileNotFoundException;
    use Illuminate\Http\Request;
    use Illuminate\Support\Facades\Artisan;
    use Illuminate\Support\Facades\DB;
    use Illuminate\Support\Facades\Session;
    use Illuminate\Support\Facades\Storage;
    use Illuminate\Support\Str;
    use Vedmant\FeedReader\Facades\FeedReader;

    /*   use Illuminate\Http\Request;*/


    class PageController extends Controller{


        public function getIp(){
            return request()->ip();
        }


        public function covid_impfung(){
            return view('frontend.covid.index');
        }


        public function markenportfolio(){
            $data["brands"] = BrandPortfolio::whereNotNull("title")->with('getTags')->orderby("title")->get();
            $data["tags"] = Brandtag::all();
            return view('frontend.markenportfolio.index', $data);
        }


        public function news_area(){

            abort("404");

            $files = (is_file("storage/app/public/mediaprint_presse/mediaprint_pressetexte.csv")) ? file("storage/app/public/mediaprint_presse/mediaprint_pressetexte.csv") : "";
            $data["pressetexte"] = [];
            if($files){
                foreach($files as $key => $value){
                    $tmp_arr = str_replace("\n", "", explode(";", $value));
                    $data["pressetexte"][ $key ]["ID"] = $tmp_arr[0];
                    $data["pressetexte"][ $key ]["TITLE"] = $tmp_arr[1];
                    $data["pressetexte"][ $key ]["MEDIUM"] = $tmp_arr[2];
                }
            }
            return view("frontend.news-area", $data);
        }


        public function kontakt(){
            return view('frontend.kontakt');
        }


        public function kontaktPost(Request $request){

            if(! empty($request->email)) return back()->with('message', 'Vielen Dank für Ihre Kontaktanfrage!');

            if(! GoodCaptcha::captchaValidate($request->solution)) abort(400);

            $messages = [
                'firstname.required' => 'Vorname muss ausgefüllt werden',
                'firstname.string'   => 'Vorname muss korrekt ausgefüllt werden',
                'firstname.max'      => 'Vorname ist zu lang (max. 255 Zeichen)',
                'lastname.required'  => 'Nachname muss ausgefüllt werden',
                'lastname.string'    => 'Nachname muss korrekt ausgefüllt werden',
                'lastname.max'       => 'Nachname ist zu lang (max. 255 Zeichen)',
                'email2.required'    => 'E-Mail Adresse muss ausgefüllt werden',
                'email2.email'       => 'E-Mail Adresse muss korrekt ausgefüllt werden',
                'email2.max'         => 'E-Mail Adresse ist zu lang (max. 100 Zeichen)',
                'phone.required'     => 'Telefonnummer muss ausgefüllt werden',
                'phone.string'       => 'Telefonnummer muss korrekt ausgefüllt werden',
                'phone.max'          => 'Telefonnummer ist zu lang (max. 16 Zeichen)',
                'area.required'      => 'Bereich muss ausgewählt werden',
                'area.in'            => 'Bereich muss korrekt ausgewählt werden',
                'message.required'   => 'Anliegen muss ausgefüllt werden',
                'message.string'     => 'Anliegen muss korrekt ausgefüllt werden',
                'message.max'        => 'Anliegen ist zu lang (max. 1000 Zeichen)',
            ];

            $rules = [
                "firstname" => 'required|string|max:255',
                "lastname"  => 'required|string|max:255',
                "email2"    => 'required|email|max:100',
                "phone"     => 'required|string|max:16',
                "area"      => 'required|in:'.join(",", array_keys(config("frontend.kontakt"))),
                "message"   => 'required|string|max:1000'
            ];

            //dd($request, $rules, $messages);

            $this->validate($request, $rules, $messages);

            $area = config("frontend.kontakt.".$request->area);

            $recipient = (env('APP_ENV') == 'local') ? env("MAIL_EMPFAENGER_KONTAKT") : $area["recipient"];


            //MESSAGE reinigen und URLS 'nicht klickbar' machen
            $msg = htmlspecialchars($request->message, ENT_QUOTES, 'UTF-8');
            $msg = strip_tags($msg);
            $foundUrls = [];
            preg_match_all('/https?:\/\/\S+/', $msg, $foundUrls);

            foreach($foundUrls[0] as $k => $url){
                //gefundene URL reinigen
                $cleansed_url = str_replace(['https://', 'http://', '.'], ['(FILTERED URL) ', '(FILTERED URL) ', '-'], $url);
                //gereinigte URL in Message setzen
                $msg = str_replace($url, $cleansed_url, $msg);
            }


            $body = "<b>Vorname:</b> ".$request->firstname."<br>";
            $body .= "<b>Nachname:</b> ".$request->lastname."<br>";
            $body .= "<b>E-Mail Adresse:</b> ".$request->email2."<br>";
            $body .= "<b>Telefonnummer:</b> ".$request->phone."<br>";
            $body .= "<b>Bereich:</b> ".$area["title"]."<br>";
            $body .= "<hr><b>Nachricht:</b><br>".nl2br($msg)."<br>";


            Email::newRawMail($recipient, "intern", "[MEDIAPRINT.AT] Kontaktformular", $body);

            return back()->with('message', 'Vielen Dank für Ihre Kontaktanfrage!');
        }


        public function unternehmen(){
            $gf = [

                [
                    'name' => 'Dr. Michael Tillian',
                    'img'  => 'tillian.jpg'
                ],
                [
                    'name' => 'Mag. Gerhard Valeskini',
                    'img'  => 'valeskini.jpg'
                ]
            ];

            if(now()->format("YmdH") > "2024070107"){
                $gf[] = [
                    'name' => 'Mag. Richard Grasl',
                    'img'  => 'rgrasl.jpg'
                ];
            }else{
                $gf[] = [
                    'name' => 'Mag. Thomas Kralinger',
                    'img'  => 'kralinger.jpg'
                ];
            }

            return view("frontend.unternehmen", ['gf' => $gf]);
        }


        public function presseShow($medium, $id){
            if(is_numeric($id)) return redirect("https://kroneanzeigen.at/download/".$id);
            return "Kein Download gefunden";
        }


        /*****************************
         ***  Datenschutz
         **************************/
        public function datenschutz($dataprivacy_id = null){
            //Liste aller Richtlinien
            $data_privacy = DataPrivacy::whereDate('online_at', '<=', Carbon::now())
                                       ->whereDate('offline_at', '>=', Carbon::now())
                                       ->orderBy("sort")
                                       ->get();

            //Einzelnes File -> Download
            if($dataprivacy_id){
                $dataprivacy = DataPrivacy::find($dataprivacy_id);
                if($dataprivacy){
                    $dataprivacy->increment("views");
                    $dataprivacy->save();

                    $data["file"] = base_path()."/storage/app/".$dataprivacy->path;
                    $data["fileurl"] = url("/storage/app/".$dataprivacy->path);
                    $data["filename"] = $dataprivacy->filename.".pdf";
                    return view("frontend.datenschutz.viewer", $data);
                }
            }

            return view("frontend.datenschutz.index", ["data" => $data_privacy]);
        }


        /*****************************
         ***  AGB
         **************************/
        public function agb($agb_id = null){

            $data["title"] = "Allgemeine Geschäftsbedingungen | www.mediaprint.at";
            $data["description"] = "Hier finden Sie die aktuellen Geschäftsbedingungen der MediaPrint.";
            $data["page"] = "agb";

            //Liste aller Richtlinien
            $data["agb"] = Agb::whereDate('online_at', '<=', Carbon::now())
                              ->whereDate('offline_at', '>=', Carbon::now())
                              ->orderBy("sort")
                              ->get();

            //Einzelnes File -> Download
            if($agb_id){
                $agb = Agb::find($agb_id);
                if($agb){
                    $agb->increment("views");
                    $agb->save();
                    $data["file"] = base_path()."/storage/app/".$agb->path;
                    $data["fileurl"] = url("/storage/app/".$agb->path);
                    $data["filename"] = $agb->filename.".pdf";
                    return view("frontend.agb.viewer", $data);
                }
            }

            return view("frontend.agb.index", ["data" => $data['agb']]);
        }


        /*****************************
         ***  IMPRESSUM
         **************************/
        public function impressum(){
            $data["impressum"] = Imprint::getByMedium("mediaprint");
            return view("frontend.impressum", $data);
        }


        public function cacheclear(){
            Artisan::call("cache:clear");
            Artisan::call("view:clear");
            Artisan::call("config:clear");
            return 0;
        }


        /*****************************
         ***  EVENT-ANMELDUNGEN
         **************************/
        public function getEventAnmeldung($event, $hash = null){
            $event = trim(strtolower($event));

            if($event == "jubilaeum"){
                $data["title"] = "Anmeldung zu MediaPrint Event";
                $data["description"] = "Dieses Event unseres Unternehmens möchten wir gemeinsam mit Ihnen feiern.";
                $data["bustransfer"] = true;
                $data["editable"] = true;
                $registration_until = "2019-01-01 00:00:00";
            }

            if($event == "weihnachtsfeier2018"){
                $data["title"] = "Anmeldung zur MediaPrint Weihnachtsfeier 2018";
                $data["description"] = "";
                $data["bustransfer"] = true;
                $data["editable"] = true;
                $registration_until = "2019-01-01 00:00:00";
            }

            if($event == "weihnachtsfeier2019"){
                $data["title"] = "Anmeldung zur MediaPrint Weihnachtsfeier 2019";
                $data["description"] = "";
                $data["bustransfer"] = false;
                $data["editable"] = false;
                $registration_until = "2019-11-24 00:00:00";
            }

            if($event == "weihnachtsfeier2022"){
                $data["title"] = "Anmeldung";
                $data["description"] = "";
                $data["bustransfer"] = false;
                $data["editable"] = false;
                $registration_until = "2022-11-24 00:00:00";
            }

            //WENN ES KEINE TITEL GIBT, DANN GIBT ES AUCH DAS EVENT NICHT
            if(empty($data["title"])) die("Kein Event gefunden!");

            //dd(Carbon::parse($registration_until), today());

            //ANMELDUNG IST VORBEI
            if(Carbon::parse($registration_until) <= today()) die("Anmeldung für dieses Event nicht mehr möglich!");

            $data['person']['email'] = "";
            $data['person']['firstname'] = "";
            $data['person']['lastname'] = "";
            $data['person']['bustransfer'] = "";
            $data['registration'] = "";

            if($hash){

                if($registration = EventRegistration::where("event", $event)->where("identifier_hash", $hash)->first()){

                    $data['person']['email'] = $registration->email;

                    if(! empty($registration->firstname)){
                        $data['person']['firstname'] = $registration->firstname;
                        $data['person']['lastname'] = $registration->lastname;
                        $data['person']['bustransfer'] = $registration->bustransfer;
                        $data['registration'] = $registration;
                    }else{
                        //holt sich den namen anhand der email adresse
                        $name = substr($registration->email, 0, strpos($registration->email, '@'));
                        if(preg_match("/\./", $name)){
                            [$firstname, $lastname] = explode('.', $name);
                            $data['person']['firstname'] = mb_convert_case($firstname, MB_CASE_TITLE);
                            $data['person']['lastname'] = mb_convert_case($lastname, MB_CASE_TITLE);
                        }else{
                            $data['person']['firstname'] = mb_convert_case($name, MB_CASE_TITLE);
                        }
                    }
                }
            }

            $data["page"] = $event;

            return view('frontend.eventanmeldung.anmeldung', $data);
        }


        public function postEventAnmeldung(Request $request){

            $messages = [
                'event.required'     => 'Kein gültiges Event',
                'event.string'       => 'Kein gültiges Event',
                'event.max'          => 'Kein gültiges Event',
                'firstname.required' => 'Bitte geben Sie einen<b> Vorname</b> an.',
                'firstname.min'      => 'Der angegebene <b>Vorname</b> ist zu kurz. (min. 2 Zeichen)',
                'firstname.max'      => 'Der angegebene<b> Vorname</b> ist zu lang. (max. 255 Zeichen)',
                'lastname.required'  => 'Bitte geben Sie einen <b>Nachnamen</b> an.',
                'lastname.min'       => 'Der angegebene <b>Nachame</b> ist zu kurz. (min. 2 Zeichen)',
                'lastname.max'       => 'Der angegebene <b>Nachame</b> ist zu lang. (max. 255 Zeichen)',
                'email.email'        => 'Bitte geben sie ein gültiges <b>E-Mail-Format</b> ein.',
                'email.max'          => 'Die angegebene <b>E-Mail</b> ist zu lang. (max. 100 Zeichen)',
                'bustransfer.string' => 'Die ausgewählte <b>Bustransferstelle</b> ist nicht zugelassen.'
            ];

            $this->validate($request, [
                'event'       => 'required|string|max:255',
                'firstname'   => 'required|min:2|max:255',
                'lastname'    => 'required|min:2|max:255',
                'email'       => 'nullable|email|max:100',
                'bustransfer' => 'nullable|string'
            ],              $messages);

            $email = trim(strtolower($request->email));
            if(! $email) $email = Str::slug(trim(strtolower($request->firstname)), ".").".".Str::slug(trim(strtolower($request->lastname)), ".")."@nomail.at";

            EventRegistration::updateOrCreate(
                [
                    'event' => $request->event,
                    'email' => $email
                ],
                [
                    'firstname'     => $request->firstname,
                    'lastname'      => $request->lastname,
                    'bustransfer'   => $request->bustransfer,
                    'registered_at' => now()
                ]
            );

            return redirect('anmeldung/'.$request->event)->with('message', 'Vielen Dank für Ihre Anmeldung!');
        }


        public function getEventAnmeldungStatistik(Request $request, $event){

            if($request->ip() != '*************' && env('APP_ENV') == 'production') return redirect('/');

            $param = new Request();
            $param["event"] = trim(strtolower($event));

            $messages = [
                'event.required' => 'Kein gültiges Event',
                'event.string'   => 'Kein gültiges Event',
                'event.max'      => 'Kein gültiges Event'
            ];
            $this->validate($param, [
                'event' => 'required|string|max:250'
            ],              $messages);

            if($event == "weihnachtsfeier2018"){
                $data["title"] = "Anmeldungen zur MediaPrint Weihnachtsfeier 2018";
                $data["description"] = "";
                $access_until = "2019-01-01 00:00:00";
            }

            if($event == "weihnachtsfeier2019"){
                $data["title"] = "Anmeldungen zur MediaPrint Weihnachtsfeier 2019";
                $data["description"] = "";
                $access_until = "2019-12-10 00:00:00";
            }

            if($event == "weihnachtsfeier2022"){
                $data["title"] = "Anmeldungen zur Weihnachtsfeier 2022";
                $data["description"] = "";
                $access_until = "2022-12-08 00:00:00";
            }

            //WENN ES KEINE TITEL GIBT, DANN GIBT ES AUCH DAS EVENT NICHT
            if(empty($data["title"])) die("Kein Event gefunden!");

            //ACCESS IST VORBEI
            if($access_until < today()) die("Statistik für dieses Event nicht mehr möglich!");

            $data["page"] = $param["event"];

            $data['raw'] = DB::select('SELECT COUNT(*) AS anzahl, bustransfer FROM event_registrations where event="'.$data["page"].'" and registered_at IS NOT NULL GROUP BY bustransfer');
            $data['gesamt'] = DB::select('SELECT COUNT(*) AS anzahl from event_registrations where event="'.$data["page"].'" and registered_at IS NOT NULL');

            $data["anmeldungen"] = EventRegistration::where("event", $data["page"])->whereNotNull("registered_at")->orderBy("lastname")->get();

            //dd($data["anmeldungen"]);

            return view('frontend.eventanmeldung.statistik', $data);
        }


        public function druckSendAnmeldung(Request $request){
            $messages = [
                'anm_organisation.required'               => 'Bitte geben Sie eine Schule oder Organisation an',
                'anm_organisation.string'                 => 'Das Feld Schule oder Organisation enthält ungültige Zeichen',
                'anm_organisation.max'                    => 'Das Feld Schule oder Organisation ist zu lang (max. 250 Zeichen)',
                'anm_name.required'                       => 'Bitte geben Sie einen Namen an',
                'anm_name.string'                         => 'Das Feld Name enthält ungültige Zeichen',
                'anm_name.max'                            => 'Das Feld Name ist zu lang (max. 250 Zeichen)',
                'anm_email.required'                      => 'Bitte geben Sie einen Namen an',
                'anm_email.email'                         => 'Das Feld E-Mail enthält ungültige Zeichen',
                'anm_email.max'                           => 'Das Feld E-Mail ist zu lang (max. 250 Zeichen)',
                'anm_telefon.required'                    => 'Bitte geben Sie eine Telefonnummer an',
                'anm_telefon.string'                      => 'Das Feld Telefonnummer enthält ungültige Zeichen',
                'anm_telefon.max'                         => 'Das Feld Telefonnummer ist zu lang (max. 250 Zeichen)',
                'anm_termin1.required'                    => 'Bitte geben Sie ihren Wunschermin an',
                'anm_termin1.date_format'                 => 'Bitte geben Sie ihren Wunschermin im Format dd.mm.YYYY an',
                'anm_termin2.date_format'                 => 'Bitte geben Sie ihren Ersatztermin im Format dd.mm.YYYY an',
                'anm_personenanzahl.required'             => 'Bitte geben Sie eine Personenanzahl an',
                'anm_personenanzahl.integer'              => 'Das Feld Personenanzahl enthält ungültige Zeichen',
                'anm_ansprechperson.string'               => 'Das Feld Ansprechperson vor Ort enthält ungültige Zeichen',
                'anm_ansprechperson.max'                  => 'Das Feld Ansprechperson vor Ort ist zu lang (max. 250 Zeichen)',
                'anm_ansprechperson_telefonnummer.string' => 'Das Feld Ansprechperson Telefonnummer enthält ungültige Zeichen',
                'anm_ansprechperson_telefonnummer.max'    => 'Das Feld Ansprechperson Telefonnummer ist zu lang (max. 250 Zeichen)',
                'anm_anmerkung.string'                    => 'Das Feld Anmerkung enthält ungültige Zeichen',
                'anm_anmerkung.max'                       => 'Das Feld Anmerkung ist zu lang (max. 20000 Zeichen)',
            ];

            $this->validate($request, [
                'anm_organisation'                 => 'required|string|max:250',
                'anm_name'                         => 'required|string|max:250',
                'anm_email'                        => 'required|email|max:250',
                'anm_telefon'                      => 'required|string|max:250',
                'anm_termin1'                      => 'required|date_format:d.m.Y',
                'anm_termin2'                      => 'nullable|date_format:d.m.Y',
                'anm_personenanzahl'               => 'required|integer',
                'anm_ansprechperson'               => 'nullable|string|max:250',
                'anm_ansprechperson_telefonnummer' => 'nullable|string|max:250',
                'anm_anmerkung'                    => 'nullable|string|max:20000'
            ],              $messages);

            //WENN EIN BOT DAS EMAIL FELD AUSFÜLLT, DANN GLEICH ERFOLGSMELDUNG ZEIGEN UND KEINE MAIL SCHICKEN
            if($request->email) return ["status" => "success", "message" => "Ihre Anfrage wurde erfolgreich abgesendet."];

            $mailtext = "
					<h3>1. Kontaktdaten</h3>
					<b>Schule oder Organisation:</b> ".$request->anm_organisation."<br>
					<b>Name:</b> ".$request->anm_name."<br>
					<b>E-Mail:</b> ".$request->anm_email."<br>
					<b>Telefonnummer:</b> ".$request->anm_telefon."<br>
					<br>
					<br>
					<h3>2. Informationen über die Führung</h3>
					<b>Gewünschter Termin:</b> ".$request->anm_termin1."<br>
					<b>Ersatztermin:</b> ".$request->anm_termin2."<br>
					<br>
					<b>Personenanzahl:</b> ".$request->anm_personenanzahl."<br>
					<b>Ansprechperson vor Ort:</b> ".$request->anm_ansprechperson."<br>
					<b>Ansprechperson Telefonnummer:</b> ".$request->anm_ansprechperson_telefonnummer."<br>
					<br>
					<b>Anmerkung:</b><br>
					".nl2br($request->anm_anmerkung)."<br>
					<br>
					<hr>
				";


            Email::newRawMail(env("MAIL_EMPFAENGER_DRUCKEREI"), "intern", "Gruppenführungen MediaPrint Druckzentrum Inzersdorf", $mailtext);

            return ["status" => "success", "message" => "Ihre Anfrage wurde erfolgreich abgesendet."];
        }


        /*****************************
         ***  Widerruf
         **************************/
        public function widerruf(Request $request, $type = null){

            if(is_numeric(request("werber"))) Session::put("werber", request("werber"));

            $data["title"] = "Widerruf | www.mediaprint.at";
            $data["description"] = "Hier finden Sie die Widerrufsbelehrung.";
            $data["page"] = "widerruf";
            $data["alerts"] = AlertMessage::getAll();
            $data["titles"] = MPHelper::getTitel();
            $data["anreden"] = MPHelper::getAnredeById();
            $data["type"] = "mediaprint";
            $data["ueberschrift"] = $data["alerts"]["A-WIDER-01"]->title;
            $data["text"] = $data["alerts"]["A-WIDER-01"]->message;

            return view("frontend.widerruf.index", $data);
        }


        /*****************************
         ***  Widerrufsformular
         **************************/
        public function widerrufPost(Request $request){

            $messages = [
                'firstname.required'            => 'Bitte geben Sie einen Vornamen an.',
                'firstname.string'              => 'Der eingegebene Vorname ist im falschen Format.',
                'firstname.min'                 => 'Der eingegebene Vorname ist zu kurz. (min. 2 Zeichen)',
                'firstname.max'                 => 'Der eingegebene Vorname ist zu lang. (max. 60 Zeichen)',
                'lastname.required'             => 'Bitte geben Sie einen Nachnamen an.',
                'lastname.string'               => 'Der eingegebene Nachname ist im falschen Format.',
                'lastname.min'                  => 'Der eingegebene Nachname ist zu kurz. (min. 2 Zeichen)',
                'lastname.max'                  => 'Der eingegebene Nachname ist zu lang. (max. 60 Zeichen)',
                'order_date.required'           => 'Bitte wählen Sie ein "Bestellung vom"-Datum.',
                'order_date.date'               => 'Das ausgewählte "Bestellung vom"-Datum ist im falschen Format.',
                'order_date.before'             => 'Das ausgewählte "Bestellung vom"-Datum liegt nach dem "Erhalten am"-Datum.',
                'received_date.required'        => 'Bitte wählen Sie ein "Erhalten am"-Datum.',
                'received_date.date'            => 'Das ausgewählte "Erhalten am"-Datum ist im falschen Format.',
                'received_date.after'           => 'Das ausgewählte "Erhalten am"-Datum liegt vor dem "Bestellung vom"-Datum',
                'email.required'                => 'Bitte geben Sie eine E-Mail Adresse an.',
                'email.email'                   => 'Die eingegebene E-Mail Adresse ist im falschen Format.',
                'email.regex'                   => 'Die eingegebene E-Mail Adresse ist im falschen Format.',
                'email.max'                     => 'Die eingegebene E-Mail Adresse ist zu lang. (max. 100 Zeichen)',
                'customer_number.numeric'       => 'Die angegebene '.(($request->type == "abo") ? "Kundennummer" : ((env("APP_OBJ") == "krone") ? "Nummer der Krone-BonusCard" : "Nummer der KURIER-Vorteilsclub-Karte")).' ist im falschen Format.',
                'address.required'              => 'Bitte geben Sie eine Adresse an.',
                'address.string'                => 'Die eingegebene Adresse ist im falschen Format.',
                'address.max'                   => 'Die eingegebene Adresse ist zu lang. (max. 200 Zeichen)',
                'improvement_suggestion.string' => 'Das eingegebene Feedback ist im falschen Format.',
                'improvement_suggestion.max'    => 'Das eingegebene Feedback ist zu lang. (max. 2000 Zeichen)',
                'produkt.required'              => 'Bitte wählen Sie das betreffende Produkt aus.',
                'produkt.string'                => 'Das eingegebene Produkt ist im falschen Format.',
                'solution.required'             => 'Sicherheitscode nicht gültig.'

            ];
            $validation_arr = [
                'firstname'              => 'required|string|min:2|max:60',
                'lastname'               => 'required|string|min:2|max:60',
                'order_date'             => 'required|date',
                'received_date'          => 'nullable|date',
                'email'                  => 'required|email|max:100|regex:/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}/',
                'produkt'                => 'required|string|min:2|max:60',
                'customer_number'        => 'nullable|numeric',
                'address'                => 'required|string|max:200',
                'improvement_suggestion' => 'nullable|string|max:2000',
                'solution'               => 'required|string'
            ];
            if(! empty($request["received_date"])){
                $validation_arr['order_date'] = 'required|date|before:received_date';
            }
            if(! empty($request["order_date"]) && ! empty($request["received_date"])){
                $validation_arr['received_date'] = 'nullable|date|after:order_date';
            }

            $this->validate($request, $validation_arr, $messages);

            if(! GoodCaptcha::captchaValidate($request->solution)) abort(400);

            //<NAME_EMAIL>
            $recipient_intern = env("MAIL_EMPFAENGER_WIDERRUF");

            $subject_intern = strtoupper(env("APP_OBJ"))."Mediaprint Widerruf";

            $message_intern = "
				<b>Datum:</b> ".Carbon::now()->format("d.m.Y H:i:s")."<br>
				<b>Vorname:</b> $request->firstname<br>
				<b>Nachname:</b> $request->lastname<br>
				<b>Kundennummer:</b> $request->customer_number<br>
				<hr>
				<b>Bestellung vom:</b> $request->order_date<br>
				<b>Erhalten am:</b> $request->received_date<br>
				<b>Produkt:</b> $request->produkt<br>
				<b>Email:</b> $request->email<br>
				<b>IP Adresse:</b> ".$request->ip()."<br>
				<hr>
				<b>Adresse:</b><br>
				$request->address<br>
				<hr>
				<b>Begründung:</b><br>
				$request->improvement_suggestion
			";
            Email::newRawMail($recipient_intern, "intern", $subject_intern, $message_intern);


            //MAIL AN KUNDE
            $recipient = $request->email;
            if(env("APP_ENV") != "production") $recipient = env("MAIL_RECIPIENT_WEBTEAM");

            $mailtext = "
				Sehr geehrte Kundin,<br>
				sehr geehrter Kunde,<br>
				<br>
				vielen Dank für Ihr <NAME_EMAIL>.<br>
				<br>
				Ihre E-Mail wurde an die zuständige Abteilung weitergeleitet und wird umgehend bearbeitet.<br>
				Bitte antworten Sie nicht auf diese E-Mail, da es sich um ein automatisch generiertes Schreiben handelt.<br>
				<br>
				<br>
				Mit freundlichen Grüßen<br>
				<br>
				Ihr Kundenservice
			";
            Email::newRawMail($recipient, "intern", "Erstrückmeldung - Ihre <NAME_EMAIL>", $mailtext);

            return back()->with("message", MPHelper::formatAlert("Ihr Widerruf wurde weitergeleitet.", "success"));
        }


        /*****************************
         ***  ANMELDUNG DIGIPRINT-OPENHOUSE
         **************************/
        public function getDigiprintOpenhouseRegistration($token){

            session()->remove("message");
            $attendee = DigiprintOpenhouseAttendee::where("verificationtoken", $token)->first();
            $attendees = DigiprintOpenhouseAttendee::count();
            $data["attendee"] = $attendee;
            $data["title"] = "DigiPrint Openhouse | www.mediaprint.at";
            $data["description"] = "Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet.";
            $data["page"] = "openhouse";

            if(session()->has("success")){
                session()->flash("message", session("success"));
                return view("frontend.digiprintopenhouseregistration", $data);
            }

            $data["dates"] = [
                "2019-08-26" => "26.08.2019",
                "2019-08-27" => "27.08.2019"
            ];

            foreach($data["dates"] as $key => $value){
                if(DigiprintOpenhouseAttendee::where("attending_at", $key)->count() >= (round($attendees / 2))) unset($data["dates"][ $key ]);
            }

            //User nicht gefunden -> error
            if(empty($attendee)){
                $data["error"] = "Der Link ist ungültig";
                return view("frontend.digiprintopenhouseregistration", $data);
            }
            //User gefunden, aber hat ist bereits angemeldet -> info
            if(! empty($attendee->confirmed_at)){
                session()->flash("message", "Sie sind bereits für den folgenden Tag angemeldet: <b>".Carbon::parse($attendee->attending_at)->format('d.m.Y')."</b>");
                return view("frontend.digiprintopenhouseregistration", $data);
            }

            return view("frontend.digiprintopenhouseregistration", $data);
        }


        public function postDigiprintOpenhouseRegistration(Request $request, $token){

            $messages = [
                'veranstaltungstag.required' => 'Kein Veranstaltungstag ausgewählt.',
                'veranstaltungstag.date'     => 'Kein gültiger Veranstaltungstag ausgewählt.',
                'size.required'              => 'Keine T-Shirt Größe ausgewählt.',
                'size.in'                    => 'Keine gültige T-Shirt Größe ausgewählt',
                'file.required'              => 'Bitte laden Sie ein Bild für den T-Shirt-Druck hoch.',
                'file.file'                  => 'Bitte laden Sie ein Bild für den T-Shirt-Druck hoch.',
                'file.mimes'                 => 'Bitte laden Sie ein Bild im richtigen Format hoch. (erlaubte Formate: JPG, JPEG, PNG)',
            ];

            $this->validate($request, [
                'veranstaltungstag' => 'required|date',
                'size'              => 'required|in: xs,s,m,l,xl,xxl',
                'file'              => 'required|file|mimes:jpg,jpeg,png'
            ],              $messages);

            $attendee = DigiprintOpenhouseAttendee::where("verificationtoken", $token)->first();

            if(empty($attendee)) return redirect("digiprint-openhouse/".$token);

            $imgStored = Storage::putFileAs("public/digiprint-openhouse", $request->file, $token.".".$request->file->getClientOriginalExtension());

            if(! $imgStored){
                return back()->withErrors("Es ist ein Problem mit dem hochgeladenen Bild aufgetreten.");
            }

            $attendee->confirmed_at = Carbon::now();
            $attendee->attending_at = $request->veranstaltungstag;
            $attendee->img_file = $imgStored;
            $attendee->tshirt_size = $request->size;
            $attendee->save();

            return redirect("digiprint-openhouse/".$token)->with("success", "<b>Vielen Dank!</b><br><i class='far fa-check fa-2x'></i><br>Sie haben sich erfolgreich angemeldet.");
        }


        /*
         *
         * mediathek
         *
         *
         */


        private function prepareEbook($hash){
            $epaper = Epaper::where("hash", $hash)->first();


            if($epaper){

                $epaper->increment("views");

                $data["epaper"] = $epaper;

                // test
                if($data["epaper"]->description == 'gesleb (?)'):
                    $path = explode('/', $data["epaper"]->path);
                    $data["epaper"]->path = 'imported/'.$path[1].'/gesleb/gesleb';
                endif;

                $data["images"] = [];
                $files = collect(Storage::files("public/epaper/".$epaper->path));


                $data["height"] = 0;
                $data["width"] = 0;

                $data["isSchoenerleben"] = Str::contains($epaper->path, 'schoenerleben');


                $files = $files->filter(function($item) use (&$data){
                    if(! preg_match("/th_|vorschaubild|mediathek|smartsales|xml/", $item)){
                        if(preg_match("/\.jpg|\.jpeg/i", $item)) [$data["width"], $data["height"]] = getimagesize(storage_path("app/$item"));
                        return $item;
                    }else{
                        return null;
                    }
                });

                $data["images"] = $files->toArray();
                //return  $data;
                return $data;
            }
            return null;
        }


        public function katalog($hash){

            $epaper = $this->prepareEbook($hash);


            if($epaper){

                if(array_key_exists('isSchoenerleben', $epaper) && $epaper['isSchoenerleben'] === true){
                    return view("frontend.katalog.show", $epaper);
                }

                return view("frontend.katalog.index", $epaper);
            }else{
                die("Kein Katalog gefunden. Mögliche Gründe: Katalog ist noch nicht oder nicht mehr online oder der Link ist falsch!");
            }
        }


        public function katalogDownload($hash){
            $epaper = $this->prepareEbook($hash);


            if($epaper){
                //  if original_pdf -> download directly else transform pages.img to pdf
                if($epaper['epaper']->original_pdf){
                    $file = "public/epaper/".$epaper['epaper']->path."/originalpdf/".$epaper['epaper']->original_pdf;
                    $headers = [
                        'Content-Type: application/pdf',
                    ];
                    return Storage::download($file, 'doc.pdf', $headers);
                }else{
                    $cover = null;
                    if($epaper['images'] and is_array($epaper['images']) and $epaper['images'][ array_key_first($epaper['images']) ]) $cover = $epaper['images'][ array_key_first($epaper['images']) ];
                    $epaper['cover'] = $cover;
                    return view("frontend.katalog.download", $epaper);
                }
            }else{
                return null;
            }
        }


        public function MapsSample1(){
            return view("maps.sample1");
        }


        public function download_kidskrone_pdf($filename){
            if($filename && explode('.', $filename)[ count(explode('.', $filename)) - 1 ] === 'pdf'){
                try{
                    $file = Storage::disk('public')->path(env('KIDSKRONE_DOWNLOAD').'/'.$filename);
                    $headers = [
                        'Content-Type: application/pdf',
                    ];
                    return \Response::download($file, $filename, $headers);
                }catch(FileNotFoundException $e){
                }
            }
            return '';
        }


        public function applyaccess(Request $request){

            if(empty($request->url)){
                Session::flash("message", "Ihre Freischaltung wurde erfolgreich beantragt");
                return view("frontend.applyaccess");
            }

            $rules = [
                "url"        => "nullable|string|max:250",
                "referer"    => "nullable|string|max:250",
                "reason"     => "nullable|string|max:250",
                "reasoncode" => "nullable|string|max:250",
                "timebound"  => "nullable|integer",
                "action"     => "nullable|string|max:250",
                "kind"       => "nullable|string|max:250",
                "rule"       => "nullable|string|max:250",
                "cat"        => "nullable|string|max:250",
                "user"       => "nullable|string|max:250",
                "locid"      => "nullable|string|max:250",
                "lang"       => "nullable|string|max:250",
                "zsq"        => "nullable|string|max:250"
            ];

            //dd($request, $rules, $request->ip());

            $this->validate($request, $rules);

            $body = "Folgende Freischaltung wurde beantragt:<br><br>";
            $body .= "<b>USER:</b> ".strip_tags($request->user)."<br>";
            $body .= "<b>IP:</b> ".$request->ip()."<br><br>";
            $body .= "<b>URL:</b> ".strip_tags($request->url)."<br>";
            $body .= "<b>REFERER:</b> ".strip_tags($request->referer)."<br>";
            $body .= "<b>REASON:</b> ".strip_tags($request->reason)."<br>";
            $body .= "<b>REASONCODE:</b> ".strip_tags($request->reasoncode)."<br>";
            $body .= "<b>TIMEBOUND:</b> ".strip_tags($request->timebound)."<br>";
            $body .= "<b>ACTION:</b> ".strip_tags($request->action)."<br>";
            $body .= "<b>KIND:</b> ".strip_tags($request->kind)."<br>";
            $body .= "<b>RULE:</b> ".strip_tags($request->rule)."<br>";
            $body .= "<b>CAT:</b> ".strip_tags($request->cat)."<br>";
            $body .= "<b>LOCID:</b> ".strip_tags($request->locid)."<br>";
            $body .= "<b>LANG:</b> ".strip_tags($request->lang)."<br>";
            $body .= "<b>ZSQ:</b> ".strip_tags($request->zsq)."<br>";

            //dd($body);

            Email::newRawMail(env("MAIL_EMPFAENGER_APPLYACCESS"), "intern", "[MEDIAPRINT.AT] APPLY ACCESS", $body);

            return redirect("applyaccess")->with('message', 'Ihre Freischaltung wurde erfolgreich beantragt');
        }

    }
