<?php

    namespace App\Http\Controllers\Frontend;

    use App\Facades\MPHelper;
    use App\Http\Controllers\Controller;
    use App\Models\Email;
    use Illuminate\Http\Request;
    use Storage;


    class GalaMenschlichkeitController extends Controller{

        public function index(){

            //if(env("APP_ENV") == "production" && ! in_array(request()->ip(), ["*************", "*************", "*************"])) die("Seite im Aufbau");


            return view("galamenschlichkeit.index", []);
        }


        public function store(Request $request){

            //dd($request->all());

            //if(env("APP_ENV") == "production" && ! in_array(request()->ip(), ["*************", "*************", "*************"])) die("Seite im Aufbau");

            $messages = [
                'firstname.required'      => 'Bitte geben Sie Ihren Vornamen an.',
                'firstname.string'        => 'Bitte einen gültigen Vornamen angeben.',
                'lastname.required'       => 'Bitte geben Sie Ihren Nachnamen an.',
                'lastname.string'         => 'Bitte einen gültigen Nachnamen angeben.',
                'email.required'          => 'Bitte geben Sie Ihre E-Mail-Adresse an.',
                'email.email'             => 'Bitte eine gültige E-Mail-Adresse angeben.',
                'phone.required'          => 'Bitte geben Sie Ihre Telefonnummer an.',
                'phone.string'            => 'Bitte eine gültige Telefonnummer angeben.',
                'nominatedName.required'  => 'Bitte geben Sie den vollständigen Namen der nominierten Person an.',
                'nominatedName.string'    => 'Bitte einen gültigen Namen der nominierten Person angeben.',
                'story.required_if'       => 'Bitte geben Sie eine Geschichte zur Nominierung an.',
                'story.string'            => 'Bitte geben Sie eine Geschichte zur Nominierung an.',
                'storyUpload.required_if' => 'Bitte laden Sie eine Geschichte hoch.',
                'storyUpload.mimes'       => 'Bitte laden Sie Ihre Geschichte als .pdf oder .docx hoch.',
                'captcha.required'        => 'Bitte den Sicherheitcode eingeben.',
                'captcha.captcha'         => 'Bitte den Sicherheitcode eingeben.',
                'acceptData.accepted'     => 'Bitte akzeptieren Sie die Verwendung Ihrer Daten.'
            ];

            $validation_arr = [
                'firstname'     => 'required|string',
                'lastname'      => 'required|string',
                'email'         => 'required|email',
                'phone'         => 'required|string',
                'nominatedName' => 'required|string',
                'story'         => 'required_if:storyUpload,null|nullable|string|max:1500',
                'storyUpload'   => 'required_if:story,null|nullable|mimes:pdf,docx',
                'acceptData'    => 'accepted',
                'captcha'       => 'required|captcha'
            ];

            $this->validate($request, $validation_arr, $messages);

            $useragent = MPHelper::getUserAgent();

            $link = "";
            if($request->storyUpload){
                $filepath = Storage::disk('public')->put("galamenschlichkeit", $request->storyUpload);
                $link = "<a href='https://www.mediaprint.at/storage/app/public/".$filepath."'>Hier downloaden</a>";
            }

            $mail_arr = [
                "Vorname"                    => strip_tags(trim($request->firstname)),
                "Nachname"                   => strip_tags(trim($request->lastname)),
                "E-Mail-Adresse"             => strip_tags(trim($request->email)),
                "Telefonnummer"              => strip_tags(trim($request->phone)),
                "Nominierte Person"          => strip_tags(trim($request->nominatedName)),
                "Link zum Upload"            => (! empty($link)) ? $link : "Kein Upload vorhanden",
                "Geschichte der Nominierung" => nl2br($request->story),
                "Session ID"                 => session()->getId(),
                "IP"                         => request()->ip(),
                "Agent - Platform"           => (! empty($useragent["platform"])) ? $useragent["platform"] : '',
                "Agent - Browser"            => (! empty($useragent["browser"])) ? $useragent["browser"] : '',
                "Agent - Version"            => (! empty($useragent["version"])) ? $useragent["version"] : ''
            ];

            $text = "<b><u>Ihre Kontaktdaten</u></b>:<br>";
            foreach($mail_arr as $key => $value){
                if($key == "Nominierte Person") $text .= "<br><b><u>Ich nominiere:</u></b><br>";
                if($key == "Session ID") $text .= "<br>";

                if($key == "Geschichte der Nominierung"){
                    $text .= "<b>".$key.":</b><br>".$value."<br>";
                }else{
                    $text .= "<b>".$key.":</b> ".$value."<br>";
                }
            }

            if($request->storyUpload){
                $filepath = Storage::disk('public')->put("galamenschlichkeit", $request->storyUpload);
            }

            Email::newRawMail(env("MAIL_EMPFAENGER_GALAMENSCHLICHKEIT"), "intern", "[GALAMENSCHLICHKEIT.KURIER.AT] Nominierung", $text);

            return redirect("/")->with("message", "Ihre Nominierung wurde erfolgreich gespeichert!");
        }

    }
