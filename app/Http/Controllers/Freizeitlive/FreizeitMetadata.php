<?php

namespace App\Http\Controllers\Freizeitlive;
class FreizeitMetadata
{
    public string $title;
    public string $description;
    public string $image;
    public string $imageType;

    public const SITE_NAME = 'freizeitlive.kurier.at';
    public const LOCALE = 'de_AT';
    public const TWITTER_CARD = 'summary_large_image';
    public const TYPE = 'website';

    public function __construct(
        string $title = '',
        string $description = '',
        string $image = ''
    ) {
        $this->title = $title ?: 'Willkommen in der exklusiven Erlebniswelt der KURIER freizeit!';
        $this->description = $description ?: 'Freizeit.live bietet zwei Tage lang die Crème de la Crème der Prominenz und eine Elite an ExpertInnen aus Mode & Beauty, Trends, Kunst & Kultur und Kulinarik an einem Ort.';
        $this->image = $image ?: url('img/freizeitlive/og-image-freizeitlive.png');
        $this->imageType = $this->detectImageType($this->image);
    }

    protected function detectImageType(string $image): string
    {
        $extension = strtolower(pathinfo($image, PATHINFO_EXTENSION));

        switch ($extension) {
            case 'jpeg':
                return 'image/jpeg';
            case 'png':
                return 'image/png';
            case 'webp':
                return 'image/webp';
            case 'gif':
                return 'image/gif';
            default:
                return 'image/jpg';
        }
    }
}
