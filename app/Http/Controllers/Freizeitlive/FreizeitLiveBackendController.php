<?php

namespace App\Http\Controllers\Freizeitlive;

use App\Http\Controllers\Controller;
use App\Models\Freizeitlive\FreizeitliveNews;
use App\Models\Freizeitlive\FreizeitlivePartner;
use App\Models\Freizeitlive\FreizeitlivePost;
use App\Models\Freizeitlive\FreizeitlivePostType;
use App\Models\Freizeitlive\FreizeitliveSpeaker;
use App\Models\Freizeitlive\FreizeitliveProgram;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Symfony\Component\HttpFoundation\File\UploadedFile;

class FreizeitLiveBackendController extends Controller
{


    /*
     *  INDEX
     */
    public function select()
    {


        return view('backend.freizeitlive.select');
    }


    public function programIndex()
    {

        //  return FreizeitliveProgram::groupProgramsByRoom();


        return view('backend.freizeitlive.program.index', ["rooms" => FreizeitliveProgram::groupProgramsByRoom()]);
    }


    public function programDelete(FreizeitliveProgram $program)
    {
        $program->speakers()->detach();
        $program->delete();
        return back()->with('notieSuccess', 'Erfolgreich gelöscht');
    }


    public function programPost(FreizeitliveProgram $program, Request $request)
    {

        $rules = [
            'title' => 'string|required',
            'description' => 'string|nullable',
            'eventdate' => 'required|date',
            'starts_at' => 'string|required',
            'ends_at' => 'string|required',
            'speakers' => 'string|nullable',
            'room_id' => 'numeric|required',
            'text' => 'string|nullable',

        ];



        if ($request->get('image_1_delete')) {
            $program->deleteFile('image_1');
            $program->update(['image_1' => null]);
        }
        if ($request->get('image_2_delete')) {
            $program->deleteFile('image_2');
            $program->update(['image_2' => null]);
        }

        if ($request->get('image_3_delete')) {
            $program->deleteFile('image_3');
            $program->update(['image_3' => null]);
        }


        $img1 = $request->file('image_1');

        if ($img1 instanceof UploadedFile) {
            $program->saveImage($img1, "image_1");
        }

        $img2 = $request->file('image_2');

        if ($img2 instanceof UploadedFile) {
            $program->saveImage($img2, "image_2");
        }

        $img3 = $request->file('image_3');

        if ($img3 instanceof UploadedFile) {
            $program->saveImage($img3, "image_3");
        }


        $valid = $request->validate($rules);

        $valid["hidden"] = $request->has('program_hidden');

        $speakerIds = array_filter(explode(';', ($request->get('speakers') ?: "")));

        $sponsorIds = array_filter(explode(';', ($request->get('sponsors') ?: "")));

        $program->speakers()->sync($speakerIds);

        $program->sponsors()->sync($sponsorIds);

        try {
            $program->update($valid);
        } catch (\Exception $exception) {

            Log::debug("reached " . __METHOD__ . ':' . __LINE__ . " | \$program->udpate() failed", ["request" => $request->all(), $exception]);
            throw $exception;
        }


        return back()->with('notieSuccess', 'Erfolgreich gespeichert');
    }


    public function programPostCreate(Request $request)
    {

        $rules = [
            'title' => 'string|required',
            'description' => 'string|nullable',
            'eventdate' => 'required|date',
            'starts_at' => 'string|required',
            'ends_at' => 'string|required',
            'speakers' => 'string|nullable',
            'room_id' => 'numeric|required',
            'text' => 'string|nullable'
        ];

        $valid = $request->validate($rules);

        $program = FreizeitliveProgram::create($valid);

        return $this->programPost($program, $request);
    }


    public function programShow(FreizeitliveProgram $program)
    {
        return view('backend.freizeitlive.program.show', ["program" => $program, "rooms" => FreizeitliveProgram::groupProgramsByRoom()]);
    }


    /*
     *  POST TYPE
     */

    public function postTypeIndex()
    {


        return view('backend.freizeitlive.post-type.index', ['postTypes' => FreizeitlivePostType::whereNotNull('name')->get()]);
    }


    public function postTypeShow($id)
    {


        return view('backend.freizeitlive.post-type.show', ['currentPost' => FreizeitlivePostType::findOrFail($id)]);
    }


    public function postTypeCreate()
    {
        $new = FreizeitlivePostType::create([]);
        return redirect("backend/freizeitlive/post-type/$new->id");
    }


    public function postTypeDelete($id)
    {


        try {
            $model = FreizeitlivePostType::findOrFail($id);
            $model->delete();

            return redirect("backend/freizeitlive/post-type")
                ->with('notieSuccess', 'Erfolgreich gelöscht');
        } catch (ModelNotFoundException $exception) {
            return redirect("backend/freizeitlive/post-type")->with('error', 'Partner not found or already deleted.');
        }
    }


    public function postTypeUpdate($id, Request $request)
    {

        $partner = FreizeitlivePostType::findOrFail($id);

        $rules = [
            'name' => 'string|required',
            'color' => 'string|nullable',
            'color_2' => 'string|nullable',
        ];


        $this->validate($request, $rules);


        $partner->update([
            'color' => $request->get('color'),
            'color_2' => $request->get('color_2'),
            'name' => $request->get('name'),

        ]);

        return redirect('backend/freizeitlive/post-type/' . $partner->id)->with('notieSuccess', 'Erfolgreich gespeichert');
    }


    /*
     *  POST
     */

    public function postIndex()
    {

        FreizeitlivePost::whereNull('title')
            ->whereDate('created_at', '<', Carbon::now()->subDay())
            ->delete();

        return view('backend.freizeitlive.post.index', ['posts' => FreizeitlivePost::whereNotNull('title')->orderBy('sort')->get()]);
    }


    public function postCreate()
    {
        $new = FreizeitlivePost::create([]);
        return redirect("backend/freizeitlive/post/$new->id");
    }


    public function postShow($id)
    {


        $v = view('backend.freizeitlive.post.show', [
            'currentPost' => FreizeitlivePost::findOrFail($id),
            'postTypes' => FreizeitlivePostType::whereNotNull('name')->get()

        ]);
        return $v;
    }


    public function postUpdate($id, Request $request)
    {

        $currentPost = FreizeitlivePost::findOrFail($id);

        $rules = [
            'title' => 'string|required',
            'desc_short' => 'string|nullable',
            'desc_long' => 'string|nullable',
            'image_one_alt' => 'string|nullable',
            'image_one_cr' => 'string|nullable',
            'image_two_alt' => 'string|nullable',
            'image_two_cr' => 'string|nullable',
            'post_type' => 'numeric|required',
            'seo_url' => 'string|nullable'
        ];

        if (!$currentPost->image_one && !$request->hasFile('image_one')) $rules['image_one'] = 'required|image';

        $this->validate($request, $rules);


        if ($request->hasFile('image_one')) {
            $currentPost->saveImage($request->file('image_one'), 1);
        }

        if ($request->hasFile('image_two')) {
            $currentPost->saveImage($request->file('image_two'), 2);
        }


        $cleanedTitle = html_entity_decode(strip_tags($request->get('title')));

        $seo_url = ($request->get('seo_url')) ?: $cleanedTitle;


        $seo_title_check = Str::slug(Str::limit($seo_url, 250));

        $unique_seo_url = $seo_title_check;

        $x = 0;

        while (FreizeitlivePost::where('seo_url', $seo_title_check . (($x) ? "-" . $x : ''))
            ->where('id', '!=', $currentPost->id)
            ->count()) {
            ++$x;
            $unique_seo_url = $seo_title_check . "-" . $x;
        }

        $currentPost->update([
            'title' => $request->get('title'),
            'desc_short' => $request->get('desc_short'),
            'desc_long' => $request->get('desc_long'),
            'image_one_alt' => $request->get('image_one_alt'),
            'image_one_cr' => $request->get('image_one_cr'),
            'image_two_alt' => $request->get('image_two_alt'),
            'image_two_cr' => $request->get('image_two_cr'),
            'post_type' => $request->get('post_type'),
            'seo_url' => $unique_seo_url
        ]);


        if ($request->get('all_links_same_color')) {
            $color = $request->get('all_links_color');
            foreach ($currentPost->links()->get() as $link) {
                $link->update(['color' => $color]);
            }
        }


        return redirect('backend/freizeitlive/post/' . $currentPost->id)->with('notieSuccess', 'Erfolgreich gespeichert');
    }


    public function postActive(Request $request, $id, $active)
    {

        $post = FreizeitlivePost::findOrFail($id);

        $request->merge(['active' => $active]);

        $rules = [
            'active' => 'required|in:0,1'
        ];

        $this->validate($request, $rules);


        $post->update([
            'active' => ($request->get('active') == 1) ? 0 : 1,

        ]);

        $text = "Erfolgreich eingeblendet";
        if ($request->get('active') == 1) $text = "Erfolgreich ausgeblendet";

        return back()->with('notieSuccess', $text);
    }


    public function postDelete($id)
    {

        try {
            $model = FreizeitlivePost::findOrFail($id);
            $model->delete();

            return redirect("backend/freizeitlive/post")
                ->with('notieSuccess', 'Erfolgreich gelöscht');
        } catch (ModelNotFoundException $exception) {
            return redirect()->back()->with('error', 'Post not found or already deleted.');
        }
    }


    public function postAjaxSort(Request $request)
    {
        if (!$request->has('orderArray')) return response(400);

        foreach ($request->get('orderArray') as $object):
            $x = FreizeitlivePost::find($object['id']);
            if ($x) $x->update(['sort' => $object['sort']]);
        endforeach;

        return response()->json(['status' => 'sorted']);
    }


    public function postDeleteImage($id, string $imagenumber)
    {

        //dz. nur für Nebenbild (image_two) erlaubt
        if (!in_array($imagenumber, ["two"])) return redirect("backend/freizeitlive/post/" . $id)->withErrors("Hauptbild kann nicht gelöscht werden");

        $flp = FreizeitlivePost::findOrFail($id);
        if (empty($flp->image_{$imagenumber})) return redirect("backend/freizeitlive/post/" . $id)->withErrors("Zu-löschendes Bild nicht gefunden");
        $flp->update([
            "image_{$imagenumber}" => null,
            "image_{$imagenumber}_alt" => null,
            "image_{$imagenumber}_cr" => null,
        ]);

        Storage::exists('public/' . $flp->image_one);

        return redirect("backend/freizeitlive/post/{$id}")->with("notieSuccess", "Bild gelöscht");
    }


    /*
     *  PARTNER
     */
    public function partnerIndex()
    {

        FreizeitlivePartner::whereNull('logoPath')
            ->whereDate('created_at', '<', Carbon::now()->subDay())
            ->delete();


        return view('backend.freizeitlive.partner.index', ['partners' => FreizeitlivePartner::whereNotNull('logoPath')->orderBy('sort')->get()]);
    }


    public function partnerCreate()
    {
        $new = FreizeitlivePartner::create([]);
        return redirect("backend/freizeitlive/partner/$new->id");
    }


    public function partnerShow($id)
    {

        return view('backend.freizeitlive.partner.show', ['partner' => FreizeitlivePartner::findOrFail($id)]);
    }


    public function partnerUpdate($id, Request $request)
    {

        $partner = FreizeitlivePartner::findOrFail($id);

        $rules = [
            'name.*' => 'string|required',
            'logoAlt' => 'string|nullable',
            'link' => 'string|nullable',

        ];

        if (!$partner->logoPath && !$request->hasFile('logo')) $rules['logo'] = 'required|image';

        $this->validate($request, $rules);


        if ($request->hasFile('logo')) {
            $partner->saveLogo($request->file('logo'));
        }

        $link = $request->get('link');

        if ($link) {
            $link = (Str::startsWith($link, 'https://')) ? $link : 'https://' . $link;
        }


        $partner->update([
            'name' => $request->get('name'),
            'logoAlt' => $request->get('logoAlt'),
            'link' => $link,

        ]);

        return redirect('backend/freizeitlive/partner/' . $partner->id)->with('notieSuccess', 'Erfolgreich gespeichert');
    }


    public function partnerActive(Request $request, $id, $active)
    {

        $partner = FreizeitlivePartner::findOrFail($id);

        $request->merge(['active' => $active]);

        $rules = [
            'active' => 'required|in:0,1'
        ];

        $this->validate($request, $rules);


        $partner->update([
            'active' => ($request->get('active') == 1) ? 0 : 1,

        ]);

        $text = "Erfolgreich eingeblendet";
        if ($request->get('active') == 1) $text = "Erfolgreich ausgeblendet";

        return back()->with('notieSuccess', $text);
    }


    public function partnerDelete($id)
    {

        try {
            $model = FreizeitlivePartner::findOrFail($id);
            if ($model) $model->delete();

            return redirect("backend/freizeitlive/partner")
                ->with('notieSuccess', 'Erfolgreich gelöscht');
        } catch (ModelNotFoundException $exception) {
            return redirect()->back()->with('error', 'Partner not found or already deleted.');
        }
    }


    public function partnerAjaxSort(Request $request)
    {
        if (!$request->has('orderArray')) return response(400);

        foreach ($request->get('orderArray') as $object):
            $x = FreizeitlivePartner::find($object['id']);
            if ($x) $x->update(['sort' => $object['sort']]);
        endforeach;

        return response()->json(['status' => 'sorted']);
    }


    /*
     *  SPEAKER
     */
    public function speakerIndex()
    {

        FreizeitliveSpeaker::whereNull('name')
            ->whereDate('created_at', '<', Carbon::now()->subDay())
            ->delete();

        return view('backend.freizeitlive.speaker.index', ['speakers' => FreizeitliveSpeaker::whereNotNull('name')->orderBy('sort')->get()]);
    }


    public function speakerCreate()
    {
        $new = FreizeitliveSpeaker::create([]);
        return redirect("backend/freizeitlive/speaker/$new->id");
    }


    public function speakerShow($id)
    {
        return view('backend.freizeitlive.speaker.show', [
            'currentSpeaker' => FreizeitliveSpeaker::findOrFail($id),
        ]);
    }


    public function speakerUpdate($id, Request $request)
    {

        $currentSpeaker = FreizeitliveSpeaker::findOrFail($id);

        $rules = [
            'name' => 'string|required',
            'desc_short' => 'string|nullable',
            'desc_long' => 'string|nullable',
            'image_one_alt' => 'string|nullable',
            'image_one_cr' => 'string|nullable',
            'seo_url' => 'string|nullable'
        ];

        if (!$currentSpeaker->image_one && !$request->hasFile('image_one')) $rules['image_one'] = 'required|image';

        $this->validate($request, $rules);


        if ($request->hasFile('image_one')) {
            $currentSpeaker->saveImage($request->file('image_one'), 1);
        }


        $seo_url = ($request->get('seo_url')) ?: $request->get('name');

        $seo_title_check = Str::slug(Str::limit($seo_url, 250));

        $unique_seo_url = $seo_title_check;

        $x = 0;

        while (FreizeitliveSpeaker::where('seo_url', $seo_title_check . (($x) ? "-" . $x : ''))
            ->where('id', '!=', $currentSpeaker->id)
            ->count()) {
            ++$x;
            $unique_seo_url = $seo_title_check . "-" . $x;
        }

        $currentSpeaker->update([
            'name' => $request->get('name'),
            'desc_short' => $request->get('desc_short'),
            'desc_long' => $request->get('desc_long'),
            'image_one_alt' => $request->get('image_one_alt'),
            'image_one_cr' => $request->get('image_one_cr'),
            'seo_url' => $unique_seo_url
        ]);


        if ($request->get('all_links_same_color')) {
            $color = $request->get('all_links_color');
            foreach ($currentSpeaker->links()->get() as $link) {
                $link->update(['color' => $color]);
            }
        }


        return redirect('backend/freizeitlive/speaker/' . $currentSpeaker->id)->with('notieSuccess', 'Erfolgreich gespeichert');
    }


    public function speakerActive(Request $request, $id, $active)
    {

        $speaker = FreizeitliveSpeaker::findOrFail($id);

        $request->merge(['active' => $active]);

        $rules = [
            'active' => 'required|in:0,1'
        ];

        $this->validate($request, $rules);


        $speaker->update([
            'active' => ($request->get('active') == 1) ? 0 : 1,

        ]);

        $text = "Erfolgreich eingeblendet";
        if ($request->get('active') == 1) $text = "Erfolgreich ausgeblendet";

        return back()->with('notieSuccess', $text);
    }


    public function speakerDelete($id)
    {

        try {
            $model = FreizeitliveSpeaker::findOrFail($id);
            $model->delete();

            return redirect("backend/freizeitlive/speaker")->with('notieSuccess', 'Erfolgreich gelöscht');
        } catch (ModelNotFoundException $exception) {
            return redirect()->back()->with('error', 'Speaker not found or already deleted.');
        }
    }


    public function speakerAjaxSort(Request $request)
    {
        if (!$request->has('orderArray')) return response(400);

        foreach ($request->get('orderArray') as $object):
            $x = FreizeitliveSpeaker::find($object['id']);
            if ($x) $x->update(['sort' => $object['sort']]);
        endforeach;

        return response()->json(['status' => 'sorted']);
    }


    /*News*/
    public function newsAjaxSort(Request $request)
    {

        if (!$request->has('orderArray')) return response(400);

        foreach ($request->get('orderArray') as $index => $newsId):
            $x = FreizeitliveNews::find($newsId);
            if ($x) $x->update(['sort' => $index]);
        endforeach;

        return response()->json(['status' => 'sorted']);
    }


    public function news()
    {

        FreizeitliveNews::whereNull('title')
            ->whereDate('created_at', '<', now()->subDay())
            ->delete();


        return view('backend.freizeitlive.news.index', ['section_name' => "HIGHLIGHTS", 'newsArray' => FreizeitliveNews::whereNotNull('title')->orderBy('sort')->get()]);
    }


    public function newsCreate()
    {
        $new = FreizeitliveNews::create([]);
        return redirect(url()->current() . "/$new->id");
    }


    public function newsShow(FreizeitliveNews $news)
    {


        return view('backend.freizeitlive.news.show', [
            'news' => $news,
            'section_name' => "HIGHLIGHTS",

        ]);
    }


    public function newsUpdate(FreizeitliveNews $news, Request $request)
    {


        $validatedData = $request->validate([
            'title' => 'required|string|max:255',
            'pre_text' => 'nullable|string',
            'text' => 'string',
            'imageCopyright' => 'string|nullable',
            'imageAlt' => 'string|nullable',
            "visible" => "string|nullable",
        ]);

        $validatedData['visible'] = $request->has('visible') ? 1 : 0;


        $cleanedTitle = html_entity_decode(strip_tags($request->get('title')));

        $seo_url = ($request->get('seo_url')) ?: $cleanedTitle;


        $seo_title_check = Str::slug(Str::limit($seo_url, 250));

        $unique_seo_url = $seo_title_check;

        $x = 0;

        while (FreizeitliveNews::where('seo_url', $seo_title_check . (($x) ? "-" . $x : ''))
            ->where('id', '!=', $news->id)
            ->count()) {
            ++$x;
            $unique_seo_url = $seo_title_check . "-" . $x;
        }

        $validatedData['seo_url'] = $unique_seo_url;

        $news->update($validatedData);

        if ($file = $request->file('imageFile')) {

            $news->saveImage($file);
        }


        return redirect(url()->current())->with('notieSuccess', 'Erfolgreich gespeichert');
    }


    public function newsDelete(FreizeitliveNews $news)
    {

        $back = str_replace("/$news->id/delete", "", url()->current());

        $news->delete();

        return redirect($back)
            ->with('notieSuccess', 'Erfolgreich gelöscht');
    }


    public function contentsPost(Request $request)
    {

        foreach ($request->all() as $key => $value) {
            $content = SpeakoutContent::where("key", $key)->first();
            if ($content instanceof SpeakoutContent) {
                $content->update(["value" => $value]);
            }
        }

        return back()->with('notieSuccess', 'Erfolgreich gespeichert');
    }
}
