<?php

namespace App\Http\Controllers\Freizeitlive;

use App\Http\Controllers\Controller;
use App\Models\Freizeitlive\FreizeitlivePartner;
use App\Models\Freizeitlive\FreizeitlivePost;
use App\Models\Freizeitlive\FreizeitliveProgram;
use App\Models\Freizeitlive\FreizeitliveSpeaker;
use App\Models\Freizeitlive\FreizeitliveNews;
use Illuminate\Http\Request;

class FreizeitLiveController extends Controller
{


    public function index()
    {
        /*    $data["speakers"] = FreizeitliveSpeaker::where("active", 1)->whereNotNull('name')->inRandomOrder()->get();*/
        $data["speakers"] = FreizeitliveSpeaker::where("active", 1)->whereNotNull('name')->orderBy('sort')->get();

        $posts = FreizeitlivePost::where("active", 1)->whereNotNull('title')->inRandomOrder()->get();

        $data["erlebnisstationen"] = $posts->filter(fn($item) => $item->post_type != 5);
        $data["masterclasses"] = $posts->filter(fn($item) => $item->post_type == 5);

        $data["partners"] = FreizeitlivePartner::where("active", 1)->whereNotNull('logoPath')->orderBy('sort')->get();
        $data["news"] = FreizeitliveNews::whereNotNull('title')->where("visible",1)->orderBy('sort')->get();

        return view("freizeitlive.home.index", $data);
    }


// erlebnisstation
    public function showPost(Request $request, $id = null, $seoUrl = null)
    {

        //Übersicht aller Posts anzeigen, wenn kein einzelner Post angezeigt werden soll

        $breadcrumbs = [
            ["text" => "Erlebnisstationen & Masterclasses", "url" => url('post')]
        ];

        if (!$id) {



            $title = "Erlebnisstationen | freizeitlive.kurier.at";

            $title_2 = "Masterclasses | freizeitlive.kurier.at";

            $posts = FreizeitlivePost::where("active", 1)->whereNotNull('title')->orderBy("post_type")->orderBy('sort')->get();

            $items = $posts->filter(fn($item) => $item->post_type != 5);

            $items_2 = $posts->filter(fn($item) => $item->post_type == 5);

            return view('freizeitlive.overview.index', [
                'items' => $items,
                "items_2" => $items_2,
                "title_2" => $title_2,
                'title' => $title,
                'breadcrumbs' => $breadcrumbs
            ]);
        }


        $post = FreizeitlivePost::with('postType')->findOrFail($id);

        //Wenn Post inaktiv und kein ?vorschau=true
        if (empty($post) || (!$post->active && !isset($request->vorschau))) abort(404);

        //Redirect if old seo_url
        if ($seoUrl != $post->seo_url) return redirect("post/$post->id/$post->seo_url");

        $breadcrumbs[] = ["text" => $post->titleText, "url" => url()->current()];


        $metadata = new FreizeitMetadata(
            $post->titleText,
            $post->desc_short,
            $post->imageOneFullPath
        );

        return view("freizeitlive.stations.show", ['station' => $post, 'breadcrumbs' => $breadcrumbs, "metadata" => $metadata]);
    }


    public function showSpeaker(Request $request, $id = null, $seoUrl = null)
    {



        $breadcrumbs = [
            ["text" => "Persönlichkeiten", "url" => url('speaker')]
        ];

        if (!$id) {

            $title = "Persönlichkeiten | freizeitlive.kurier.at";

            $items = FreizeitliveSpeaker::where("active", 1)->whereNotNull('name')->orderBy('sort')->get();

            return view('freizeitlive.overview.index', ['items' => $items, 'title' => $title, 'breadcrumbs' => $breadcrumbs]);
        }

        $speaker = FreizeitliveSpeaker::findOrFail($id);


        //Wenn Speaker inaktiv und kein ?vorschau=true
        if (empty($speaker) || (!$speaker->active && !isset($request->vorschau))) abort(404);

        //Redirect if old seo_url
        if ($seoUrl != $speaker->seo_url) return redirect("speaker/$speaker->id/$speaker->seo_url");

        $breadcrumbs[] = ["text" => $speaker->name, "url" => url()->current()];




        return view("freizeitlive.speakers.show", [
            'speaker' => $speaker,
            'breadcrumbs' => $breadcrumbs
        ]);


    }


    public function showHighlight(Request $request, $id = null, $seoUrl = null)
    {

        $breadcrumbs = [
            ["text" => "Highlights", "url" => url('highlights')]
        ];

        if (!$id) {

            $title = "Highlights | freizeitlive.kurier.at";

            $items = FreizeitliveNews::whereNotNull('title')->where('visible', 1)->orderBy('created_at', 'DESC')->get();

            return view('freizeitlive.overview.index', [
                'items' => $items,
                'title' => $title,
                'breadcrumbs' => $breadcrumbs]);

        }

        $highlight = FreizeitliveNews::where('visible', 1)->findOrFail($id);
        if (empty($highlight)) abort(404);

        //Redirect if old seo_url
        if ($seoUrl != $highlight->seo_url) return redirect("highlights/" . $highlight->id . "/" . $highlight->seo_url);

        $breadcrumbs[] = ["text" => $highlight->titleText, "url" => url()->current()];


        $metadata = new FreizeitMetadata(
            $highlight->titleText,
            $highlight->pre_text,
            $highlight->imageFullPath
        );


        return view("freizeitlive.highlights.show", [
            'item' => $highlight,
            'breadcrumbs' => $breadcrumbs,
            "metadata" => $metadata,
        ]);


    }


    public function program(Request $request, $selectedId = null)
    {

        //if(env("APP_ENV") == "production" && ! isset($request->vorschau)) abort(404);

        return view("freizeitlive.program.index",
            [
                'programs' => FreizeitliveProgram::orderByDayAndTime()->where("hidden", false)->get(),
                'rooms' => FreizeitliveProgram::getRooms(),
                'selectedProgram' => FreizeitliveProgram::find($selectedId),
            ]);


    }


    public function impressionen()
    {
        $data["images2024"] = scandir("storage/app/public/freizeitlive/impressionen/2024");
        unset($data["images2024"][0], $data["images2024"][1]);
        //dd($data["images2024"]);
        return view("freizeitlive.impressionen", $data);
    }
}

