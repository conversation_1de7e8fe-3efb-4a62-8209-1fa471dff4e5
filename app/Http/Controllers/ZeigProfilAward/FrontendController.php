<?php

    namespace App\Http\Controllers\ZeigProfilAward;

    use App\Facades\MPHelper;
    use App\Http\Controllers\Controller;
    use App\Models\Freizeitlive\FreizeitlivePartner;
    use App\Models\Freizeitlive\FreizeitlivePost;
    use App\Models\Freizeitlive\FreizeitliveProgram;
    use App\Models\Freizeitlive\FreizeitliveSpeaker;
    use App\Models\ZeigProfilAward\ZeigProfilAwardCategory;
    use App\Models\ZeigProfilAward\ZeigProfilAwardHomepage;
    use App\Models\ZeigProfilAward\ZeigProfilAwardJury;
    use App\Models\ZeigProfilAward\ZeigProfilAwardJuryVoting;
    use App\Models\ZeigProfilAward\ZeigProfilAwardPartner;
    use App\Models\ZeigProfilAward\ZeigProfilAwardPublicVoting;
    use App\Models\ZeigProfilAward\ZeigProfilAwardSubmit;
    use App\MPClasses\GoodCaptcha;
    use Illuminate\Http\Request;
    use Illuminate\Support\Facades\Storage;
    use Psy\Util\Str;
    use Session;

    class FrontendController extends Controller{

        public function index($hash = null){


            $categories = ZeigProfilAwardCategory::whereNotNull('title')->orderBy('sort')->get();
            $partners = ZeigProfilAwardPartner::whereNotNull('logoPath')->isActive()->orderBy('sort')->get();
            $epu = ZeigProfilAwardSubmit::where('epu', 'J')->where('epu_voting_freigabe', 1)->get();


            $homepage = ZeigProfilAwardHomepage::first();
            $name = null;

            $jury = ZeigProfilAwardJury::all();
            $exists = $jury->contains('hash', $hash);
            $getJury = ZeigProfilAwardJury::where('hash', $hash)->first();

            $phase = 0;
            $hash = null;
            $name = null;

            if(! $homepage instanceof ZeigProfilAwardHomepage) abort(400);

            if(today()->gte('2025-09-15') && today()->lte('2025-09-20')){
                $phase = 2;
                $name = null;
                $hash = null;
            }elseif($exists && today()->lte('2025-09-23') && today()->gte('2025-09-23')){
                $phase = 3;
                $name = $getJury->vorname.' '.$getJury->nachname;
            }

            //if(config('app.env') == 'development') $phase = 2;
            //if(app('APP_ENV' == 'development')) $phase = 2;

            if(today()->gte('2025-06-21') && today()->lte('2025-09-13')){
                $phase = 1;
            }

            if(in_array(request()->ip(), config("zeigprofilaward.zeigprofil-ips.ips"))){
                $phase = 1;
                $name = 'ADMIN';
            }

            $phase = 2;

            return view("zeigprofilaward.index", [
                "categories" => $categories,
                "partners"   => $partners,
                "allImages"  => $homepage->getImageArray(),
                "homepage"   => $homepage,
                "phase"      => $phase,
                "hash"       => $hash,
                /*  "epu"        => $epu,*/
                "name"       => $name
            ]);
        }


        public function cookiepolicy(){

            $homepage = ZeigProfilAwardHomepage::first();
            if(! $homepage instanceof ZeigProfilAwardHomepage) abort(400);

            return view("zeigprofilaward.cookiepolicy", [
                "allImages" => $homepage->getImageArray()
            ]);
        }


        public function einreichenIndex($cat = false, $seo = false){
            $categories = ZeigProfilAwardCategory::whereNotNull('title')->orderBy('sort')->get();

            $homepage = ZeigProfilAwardHomepage::first();
            if(! $homepage instanceof ZeigProfilAwardHomepage) abort(400);

            return view("zeigprofilaward.forms.einreichen", [
                "categories"  => $categories,
                "allImages"   => $homepage->getImageArray(),
                "selectedCat" => $cat
            ]);
        }


        public function einreichenPost(Request $request){


            $messages = [
                'firstname.required'         => 'Vorname ist ein Pflichtfeld',
                'firstname.string'           => 'Vorname ist im falschen Format',
                'firstname.max'              => 'Vorname ist zu lang (max. 250 Zeichen)',
                'lastname.required'          => 'Nachname ist ein Pflichtfeld',
                'lastname.string'            => 'Nachname ist im falschen Format',
                'lastname.max'               => 'Nachname ist zu lang (max. 250 Zeichen)',
                'email.required'             => 'E-Mail Adresse ist ein Pflichtfeld',
                'email.email'                => 'E-Mail Adresse ist im falschen Format',
                'email.max'                  => 'E-Mail Adresse ist zu lang (max. 250 Zeichen)',
                'company.required'           => 'Firmenname ist ein Pflichtfeld',
                'company.string'             => 'Firmenname ist im falschen Format',
                'company.max'                => 'Firmenname ist zu lang (max. 250 Zeichen)',
                'category.required'          => 'Kategorie ist ein Pflichtfeld',
                'category.between'           => 'Kategorie ist ungültig',
                /*                'epu.required'               => 'EPU ist ein Pflichtfeld',*/
                /*                'epu.in'                     => 'EPU ist ungültig',*/
                /*                'epu.string'                 => 'EPU ist im falschen Format',*/
                'founding_date.required'     => 'Gründungsdatum ist ein Pflichtfeld',
                'founding_date.date'         => 'Gründungsdatum ist im falschen Format',
                'founding_date.before'       => 'Gründungsdatum muss vor Heute sein',
                'founding_place.required'    => 'Gründungsort ist ein Pflichtfeld',
                'founding_place.string'      => 'Gründungsort ist im falschen Format',
                'founding_place.max'         => 'Gründungsort ist zu lang (max. 250 Zeichen)',
                'mission_statement.required' => 'Unternehmensphilosophie ist ein Pflichtfeld',
                'mission_statement.string'   => 'Unternehmensphilosophie ist im falschen Format',
                'claim.required'             => 'Claim/Slogan ist ein Pflichtfeld',
                'claim.string'               => 'Claim/Slogan ist im falschen Format',
                'claim.max'                  => 'Claim/Slogan ist zu lang (max. 250 Zeichen)',
                'project_goal.required'      => 'Unternehmensidee/-ziel ist ein Pflichtfeld',
                'project_goal.string'        => 'Unternehmensidee/-ziel ist im falschen Format',
                'credentials.required'       => 'Referenzen ist ein Pflichtfeld',
                'credentials.string'         => 'Referenzen ist im falschen Format',
                'credentials.max'            => 'Referenzen ist zu lang (max. 250 Zeichen)',
                'company_logo.required'      => 'Unternehmenslogo fehlt',
                'company_logo.image'         => 'Unternehmenslogo ist im falschen Format',
                'company_logo.max'           => 'Unternehmenslogo ist zu groß (max. 10MB)',
                'company_logo.dimensions'    => 'Das Firmenlogo muss mindestens 500x500 Pixel groß sein.',
                'project_picture.required'   => 'Unternehmenspräsentation ist ein Pflichtfeld',
                'project_picture.mimes'      => 'Unternehmenspräsentation muss ein PDF sein',
                'project_picture.max'        => 'Projektbild ist zu groß (max. 2MB)',
                'company_picture.required'   => 'Maßnahmen und Umsetzung ist ein Pflichtfeld',
                'company_picture.mimes'      => 'Maßnahmen und Umsetzung muss ein PDF sein',
                'company_skizze.mimes'       => 'Referenzen müssen als PDF hochgeladen werden',
                'company_skizze.max'         => 'Referenzen sind zu groß (max. 10MB)',
            ];

            $validation_arr = [
                "firstname"         => 'nullable|string|max:250',
                "lastname"          => 'nullable|string|max:250',
                "email"             => 'required|email|max:250',
                "company"           => 'required|string|max:250',
                "category"          => 'required|between:1,5',
                /*"epu"               => 'required|string|in:J,N',*/
                "founding_date"     => 'required|date|before:'.today()->subDay()->format('Y-m-d'),
                "founding_place"    => 'required|string|max:250',
                "mission_statement" => 'required|string',
                "claim"             => 'nullable|string|max:250',
                "project_goal"      => 'required|string',
                "credentials"       => 'nullable|string|max:250',
                "company_logo"      => 'required|image|mimes:jpg,jpeg,png|max:10048|dimensions:min_width=500,min_height=500',
                "company_picture"   => 'required|file|mimes:pdf|max:10240',
                "company_skizze"    => 'nullable|file|mimes:pdf|max:10240',
                "project_picture"   => 'required|file|mimes:pdf|max:10240',
            ];


            //dd($request->all());

            $this->validate($request, $validation_arr, $messages);

            $submit = ZeigProfilAwardSubmit::create([
                                                        "firstname"         => $request->firstname,
                                                        "lastname"          => $request->lastname,
                                                        "email"             => $request->email,
                                                        "company"           => $request->company,
                                                        "category"          => $request->category,
                                                        /*"epu"               => $request->epu,*/
                                                        "founding_date"     => $request->founding_date,
                                                        "founding_place"    => $request->founding_place,
                                                        "mission_statement" => $request->mission_statement,
                                                        "claim"             => $request->claim,
                                                        "project_goal"      => $request->project_goal,
                                                        "credentials"       => $request->credentials
                                                    ]);


            $fileshash = $hash = md5($request->email.$submit->id);
            $path = "zeigprofilaward".DIRECTORY_SEPARATOR.((env("APP_ENV") != "production") ? 'dev'.DIRECTORY_SEPARATOR : '')."submits";

            if($request->company_logo) Storage::disk('public')->putFileAs($path.DIRECTORY_SEPARATOR.$submit->id, $request->company_logo, "companylogo_{$fileshash}.{$request->company_logo->getClientOriginalExtension()}");
            if($request->project_picture) Storage::disk('public')->putFileAs($path.DIRECTORY_SEPARATOR.$submit->id, $request->project_picture, "unternehmenspraesentation_{$fileshash}.{$request->project_picture->getClientOriginalExtension()}");
            if($request->company_picture) Storage::disk('public')->putFileAs($path.DIRECTORY_SEPARATOR.$submit->id, $request->company_picture, "umsetzung_{$fileshash}.{$request->company_picture->getClientOriginalExtension()}");
            if($request->company_skizze) Storage::disk('public')->putFileAs($path.DIRECTORY_SEPARATOR.$submit->id, $request->company_skizze, "referenzen_{$fileshash}.{$request->company_skizze->getClientOriginalExtension()}");

            $submit->update(["fileshash" => $fileshash]);

            return back()->with("message", "Projekt erfolgreich eingereicht!");
        }


        public function vote(Request $request){

            //dd($request->all());

            $messages = [
                'project_id.required'  => 'Kein Projekt gewählt',
                'category_id.required' => 'Keine Kategorie gewählt',
                'project_id.integer'   => 'Wert ist keine Zahl',
                'category_id.integer'  => 'Wert ist keine Zahl',

            ];

            $validation_arr = [
                'project_id'  => 'required|integer',
                'category_id' => 'required|integer',
            ];

            //dd($request->all());

            $this->validate($request, $validation_arr, $messages);

            if(! GoodCaptcha::captchaValidate($request->solution)) abort(400);

            $useragent = MPHelper::getUserAgent();

            ZeigProfilAwardPublicVoting::create([
                                                    "submit_id"      => $request->project_id,
                                                    "category_id"    => $request->category_id,
                                                    "session_hash"   => session()->getId(),
                                                    "ip"             => $request->ip(),
                                                    'agent_platform' => (! empty($useragent["platform"])) ? $useragent["platform"] : '',
                                                    'agent_browser'  => (! empty($useragent["browser"])) ? $useragent["browser"] : '',
                                                    'agent_version'  => (! empty($useragent["version"])) ? $useragent["version"] : ''
                                                ]);

            return response()->json([
                                        "success"  => true,
                                        "category" => $request->category_id
                                    ]);
        }


        public function vote_jury(Request $request){


            $messages = [
                'project_id.required'  => 'Kein Projekt gewählt',
                'category_id.required' => 'Keine Kategorie gewählt',
                'project_id.integer'   => 'Wert ist keine Zahl',
                'category_id.integer'  => 'Wert ist keine Zahl',

            ];

            $validation_arr = [
                'project_id'  => 'required|integer',
                'category_id' => 'required|integer',
            ];

            //dd($request->all());

            $this->validate($request, $validation_arr, $messages);

            if(! GoodCaptcha::captchaValidate($request->solution)) abort(400);

            $useragent = MPHelper::getUserAgent();

            ZeigProfilAwardJuryVoting::updateOrCreate([
                                                          'category_id' => $request->category_id,
                                                          'hash'        => $request->hash,
                                                      ], [
                                                          "submit_id"      => $request->project_id,
                                                          "category_id"    => $request->category_id,
                                                          "session_hash"   => session()->getId(),
                                                          "ip"             => $request->ip(),
                                                          'agent_platform' => (! empty($useragent["platform"])) ? $useragent["platform"] : '',
                                                          'agent_browser'  => (! empty($useragent["browser"])) ? $useragent["browser"] : '',
                                                          'agent_version'  => (! empty($useragent["version"])) ? $useragent["version"] : '',
                                                          'hash'           => $request->hash

                                                      ]);

            return response()->json([
                                        "success"  => true,
                                        "category" => $request->category_id
                                    ]);
        }


        public function download(ZeigProfilAwardSubmit $submit){

            $filename = $submit->id.'/company_skizze_'.$submit->fileshash.'.pdf';


            $path = public_path('storage/app/public/zeigprofilaward/submits/');


            if(file_exists($path.$filename)){
                return response()->download($path.$filename);
            }else{
                return redirect()->back();
            }
        }
    }

