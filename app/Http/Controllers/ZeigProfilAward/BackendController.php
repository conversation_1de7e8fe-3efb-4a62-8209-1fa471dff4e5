<?php

    namespace App\Http\Controllers\ZeigProfilAward;

    use App\Http\Controllers\Controller;
    use App\Models\ZeigProfilAward\ZeigProfilAwardCategory;
    use App\Models\ZeigProfilAward\ZeigProfilAwardHomepage;
    use App\Models\ZeigProfilAward\ZeigProfilAwardJury;
    use App\Models\ZeigProfilAward\ZeigProfilAwardJuryVoting;
    use App\Models\ZeigProfilAward\ZeigProfilAwardPartner;
    use App\Models\ZeigProfilAward\ZeigProfilAwardPublicVoting;
    use App\Models\ZeigProfilAward\ZeigProfilAwardSubmit;
    use Carbon\Carbon;
    use Illuminate\Database\Eloquent\ModelNotFoundException;
    use Illuminate\Http\Request;

    use Illuminate\Support\Facades\DB;
    use Illuminate\Support\Facades\Storage;
    use Illuminate\Support\Str;
    use Rap2hpoutre\FastExcel\FastExcel;

    class BackendController extends Controller{


        /*
         *  INDEX
         */
        public function index(){
            return view('backend.zeigprofilaward.index');
        }


        /*
         *  PARTNER
         */
        public function partnerIndex(){

            ZeigProfilAwardPartner::whereNull('logoPath')
                                  ->whereDate('created_at', '<', Carbon::now()->subDay())
                                  ->delete();


            return view('backend.zeigprofilaward.partner.index', ['partners' => ZeigProfilAwardPartner::whereNotNull('logoPath')->orderBy('sort')->get()]);
        }


        public function partnerCreate(){
            $new = ZeigProfilAwardPartner::create([]);
            return redirect("backend/zeigprofilaward/partner/$new->id");
        }


        public function partnerShow($id){

            return view('backend.zeigprofilaward.partner.show', ['partner' => ZeigProfilAwardPartner::findOrFail($id)]);
        }


        public function partnerUpdate($id, Request $request){

            $partner = ZeigProfilAwardPartner::findOrFail($id);

            $rules = [
                'name.*'  => 'string|required',
                'logoAlt' => 'string|nullable',
                'link'    => 'string|nullable',

            ];

            if(! $partner->logoPath && ! $request->hasFile('logo')) $rules['logo'] = 'required|image';

            $this->validate($request, $rules);


            if($request->hasFile('logo')){
                $partner->saveLogo($request->file('logo'));
            }

            $link = $request->get('link');

            if($link){
                $link = (Str::startsWith($link, 'https://')) ? $link : 'https://'.$link;
            }


            $partner->update([
                                 'name'    => $request->get('name'),
                                 'logoAlt' => $request->get('logoAlt'),
                                 'link'    => $link,

                             ]);

            return redirect('backend/zeigprofilaward/partner/'.$partner->id)->with('notieSuccess', 'Erfolgreich gespeichert');
        }


        public function partnerActive(Request $request, $id, $active){

            $partner = ZeigProfilAwardPartner::findOrFail($id);

            $request->merge(['active' => $active]);

            $rules = [
                'active' => 'required|in:0,1'
            ];

            $this->validate($request, $rules);


            $partner->update([
                                 'active' => ($request->get('active') == 1) ? 0 : 1,

                             ]);

            $text = "Erfolgreich eingeblendet";
            if($request->get('active') == 1) $text = "Erfolgreich ausgeblendet";

            return back()->with('notieSuccess', $text);
        }


        public function partnerDelete($id){

            try{
                $model = ZeigProfilAwardPartner::findOrFail($id);
                if($model) $model->delete();

                return redirect("backend/zeigprofilaward/partner")->with('notieSuccess', 'Erfolgreich gelöscht');
            }catch(ModelNotFoundException $exception){
                return redirect()->back()->with('error', 'Partner not found or already deleted.');
            }
        }


        public function partnerAjaxSort(Request $request){
            if(! $request->has('orderArray')) return response(400);

            foreach($request->get('orderArray') as $object):
                $x = ZeigProfilAwardPartner::find($object['id']);
                if($x) $x->update(['sort' => $object['sort']]);
            endforeach;

            return response()->json(['status' => 'sorted']);
        }


        // KATEGORIEN VERWALTEN

        public function categoryIndex(){
            $inputs = config('zeigprofilaward.category-form');
            $categories = ZeigProfilAwardCategory::orderBy("sort")->get();

            $allFormFields = $categories->map(function($categorie) use ($inputs){

                $data = [];
                foreach($inputs as $input){
                    $name = $input['inputName'];

                    if($name === "image") $input['currentPreview'] = $categorie->imageFullPath;
                    $input['value'] = $categorie[ $input['inputName'] ];
                    $input['inputName'] = "r[$categorie->id][$name]";

                    $data[] = $input;
                }

                return $data;
            })->toArray();

            return view('backend.zeigprofilaward.category.index', ['allFormFields' => $allFormFields]);
        }


        public function categoryPost(Request $request){

            $categories = $request->get("r");
            $fileArray = $request->files->get('r');

            //dd($categories, $fileArray);

            if(! is_array($categories)) return back()->withErrors('Error!');

            foreach($categories as $id => $currentCategory){
                $category = ZeigProfilAwardCategory::find($id);
                if($category instanceof ZeigProfilAwardCategory){
                    $category->update(
                        [
                            "title"    => (string) $currentCategory['title'],
                            "subtitle" => (string) $currentCategory['subtitle'],
                            "text"     => (string) $currentCategory['text'],
                        ]
                    );


                    if(! empty($fileArray[ $id ]['image']) && is_file($fileArray[ $id ]['image'])){
                        $category->saveImage($fileArray[ $id ]['image']);
                    }
                }
            }

            return back()->with('notieSuccess', 'Erfolgreich gespeichert');
        }


        public function publicVotingFreigabe(ZeigProfilAwardSubmit $submit, Request $request){

            $freigabe = ($request->publicVoting == "true") ? 1 : 0;

            $submit->update([
                                'public_voting_freigabe' => $freigabe
                            ]);

            return response()->json('OK');
        }


        public function juryVotingFreigabe(ZeigProfilAwardSubmit $submit, Request $request){

            $freigabe = ($request->juryVoting == "true") ? 1 : 0;

            $submit->update([
                                'jury_voting_freigabe' => $freigabe
                            ]);

            return response()->json('OK');
        }
        public function epuVotingFreigabe(ZeigProfilAwardSubmit $submit, Request $request){


            $freigabe = ($request->epuVoting == "true") ? 1 : 0;

            $submit->update([
                                'epu_voting_freigabe' => $freigabe
                            ]);

            return response()->json('OK');
        }


        public function submitAuswertungen(){

            if(! ZeigProfilAwardPublicVoting::all()->count()){
                return redirect()->back();
            }

            $this->cleanUp();
            $data['getCategories'] = ZeigProfilAwardCategory::all();
            $data['votes_total'] = ZeigProfilAwardPublicVoting::withTrashed()->get();
            $data['votes_clean'] = ZeigProfilAwardPublicVoting::where('deleted_at', null);


            $getCategories = ZeigProfilAwardCategory::with([
                                                               'submits' => function($query){
                                                                   $query->where('public_voting_freigabe', 1);
                                                               }, 'submits.votes'
                                                           ])->get();

            foreach($getCategories as $category){

                $votesCount = 0;

                foreach($category->submits as $submit){
                    $votesCount += $submit->votes->count();
                }

                $data['categoryvotes'][ $category->title ] = $votesCount;
            }

            $getCategoriesClean = ZeigProfilAwardCategory::with([
                                                                    'submits'          => function($query){
                                                                        $query->where('public_voting_freigabe', 1);
                                                                    }, 'submits.votes' => function($query){
                    $query->withTrashed(); // Berücksichtige soft-gelöschte votes
                }
                                                                ])->get();

            foreach($getCategoriesClean as $category){

                $votesCountClean = 0;

                foreach($category->submits as $submit){
                    $votesCountClean += $submit->votes->count();
                }

                $data['categoryvotesclean'][ $category->title ] = $votesCountClean;
            }


            return view('backend.zeigprofilaward.auswertungen.index', $data);
        }


        public function submitJuryAuswertungen(){


            if(! ZeigProfilAwardJuryVoting::all()->count()){
                return redirect()->back();
            }

            $data['getCategories'] = ZeigProfilAwardCategory::all();
            $data['votes_total'] = ZeigProfilAwardJuryVoting::withTrashed()->get();



            $getCategories = ZeigProfilAwardCategory::with([
                                                               'jurySubmits' => function($query){
                                                                   $query->where('jury_voting_freigabe', 1);
                                                               }, 'jurySubmits.votesJury'
                                                           ])->get();


            foreach($getCategories as $category){
                $votesCount = 0;
                foreach($category->submits as $submit){
                    $votesCount += $submit->votesJury->where('category_id', '!=',6)->count();
                }
                if($category->id == 6){
                    foreach(ZeigProfilAwardSubmit::where('epu_voting_freigabe', 1)->get() as $submit_extra){
                        $votesCount += $submit_extra->votesJury->where('category_id', 6)->count();
                    }
                }

                $data['categoryvotes'][ $category->title ] = $votesCount;
            }


            return view('backend.zeigprofilaward.auswertungen_jury.index', $data);
        }


        public function cleanUp(){

            //Datensätze wo eine Kategorie NULL ist rauslöschen
            /*ZeigProfilAwardPublicVoting::whereNull("category_id")->forceDelete();*/

            $session_arr = [];
            $ergebnisse = DB::select("SELECT id, session_hash  FROM zeigprofilaward_public_voting WHERE deleted_at is null", [0]);


            foreach($ergebnisse as $ergebnis){
                if(key_exists($ergebnis->session_hash, $session_arr)){
                    if($session_arr[ $ergebnis->session_hash ] < 5){
                        $session_arr[ $ergebnis->session_hash ]++;
                    }else{
                        DB::update("update zeigprofilaward_public_voting set deleted_at = ? where id = ?", [now(), $ergebnis->id]);
                    }
                }else{
                    $session_arr[ $ergebnis->session_hash ] = 1;
                }
            }


            return response()->json(["done" => true], 200);
        }


        public function submitDataUpdate(ZeigProfilAwardSubmit $submit, Request $request){

            $messages = [
                'firstname.required'         => 'Vorname ist ein Pflichtfeld',
                'firstname.string'           => 'Vorname ist im falschen Format',
                'firstname.max'              => 'Vorname ist zu lang (max. 250 Zeichen)',
                'lastname.required'          => 'Nachname ist ein Pflichtfeld',
                'lastname.string'            => 'Nachname ist im falschen Format',
                'lastname.max'               => 'Nachname ist zu lang (max. 250 Zeichen)',
                'email.required'             => 'E-Mail Adresse ist ein Pflichtfeld',
                'email.email'                => 'E-Mail Adresse ist im falschen Format',
                'email.max'                  => 'E-Mail Adresse ist zu lang (max. 250 Zeichen)',
                'company.required'           => 'Firmenname ist ein Pflichtfeld',
                'company.string'             => 'Firmenname ist im falschen Format',
                'company.max'                => 'Firmenname ist zu lang (max. 250 Zeichen)',
                'category.required'          => 'Kategorie ist ein Pflichtfeld',
                'category.between'           => 'Kategorie ist ungültig',
/*                'epu.required'               => 'EPU ist ein Pflichtfeld',*/
/*                'epu.in'                     => 'EPU ist ungültig',*/
/*                'epu.string'                 => 'EPU ist im falschen Format',*/
                'founding_date.required'     => 'Gründungsdatum ist ein Pflichtfeld',
                'founding_date.date'         => 'Gründungsdatum ist im falschen Format',
                'founding_date.before'       => 'Gründungsdatum muss vor Heute sein',
                'founding_place.required'    => 'Gründungsort ist ein Pflichtfeld',
                'founding_place.string'      => 'Gründungsort ist im falschen Format',
                'founding_place.max'         => 'Gründungsort ist zu lang (max. 250 Zeichen)',
                'mission_statement.required' => 'Unternehmensphilosophie ist ein Pflichtfeld',
                'mission_statement.string'   => 'Unternehmensphilosophie ist im falschen Format',
                'claim.required'             => 'Claim/Slogan ist ein Pflichtfeld',
                'claim.string'               => 'Claim/Slogan ist im falschen Format',
                'claim.max'                  => 'Claim/Slogan ist zu lang (max. 250 Zeichen)',
                'project_goal.required'      => 'Unternehmensidee/-ziel ist ein Pflichtfeld',
                'project_goal.string'        => 'Unternehmensidee/-ziel ist im falschen Format',
                'credentials.required'       => 'Referenzen ist ein Pflichtfeld',
                'credentials.string'         => 'Referenzen ist im falschen Format',
                'credentials.max'            => 'Referenzen ist zu lang (max. 250 Zeichen)',
                'company_logo.required'      => 'Unternehmenslogo fehlt',
                'company_logo.image'         => 'Unternehmenslogo ist im falschen Format',
                'company_logo.max'           => 'Unternehmenslogo ist zu groß (max. 2MB)',
                'project_picture.required'   => 'Unternehmenspräsentation ist ein Pflichtfeld',
                'project_picture.mimes'      => 'Unternehmenspräsentation muss ein PDF sein',
                'project_picture.max'        => 'PDF ist zu groß (max. 20MB)',
                'company_picture.required'   => 'Maßnahmen und Umsetzung ist ein Pflichtfeld',
                'company_picture.mimes'      => 'Maßnahmen und Umsetzung muss ein PDF sein',
                'company_skizze.image'       => 'Referenzen müssen als PDF hochgeladen werden',
                'company_skizze.max'         => 'Referenzen sind zu groß (max. 10MB)',
            ];

            $validation_arr = [
                "firstname"         => 'nullable|string|max:250',
                "lastname"          => 'nullable|string|max:250',
                "email"             => 'required|email|max:250',
                "company"           => 'required|string|max:250',
                "category"          => 'required|between:1,5',
/*                "epu"               => 'required|string|in:J,N',*/
                "founding_date"     => 'required|date|before:'.today()->subDay()->format('Y-m-d'),
                "founding_place"    => 'required|string|max:250',
                "mission_statement" => 'nullable|string|max:250',
                "claim"             => 'nullable|string|max:250',
                "project_goal"      => 'required|string',
                "credentials"       => 'nullable|string|max:250',
                "company_logo"      => 'nullable|image|max:2048',
                "company_picture"   => 'nullable|file|mimes:pdf|max:20000',
                "company_skizze"    => 'nullable|file|mimes:pdf|max:20000',
                "project_picture"   => 'nullable|file|mimes:pdf|max:20000',
            ];

            //dd($request->all());

            $this->validate($request, $validation_arr, $messages);


            $submit->update([
                                "firstname"         => $request->firstname,
                                "lastname"          => $request->lastname,
                                "email"             => $request->email,
                                "company"           => $request->company,
                                "founding_date"     => $request->founding_date,
                                "founding_place"    => $request->founding_place,
                                "mission_statement" => $request->mission_statement,
                                "claim"             => $request->claim,
                                "epu"               => $request->epu,
                                "category"          => $request->category,
                                "project_goal"      => $request->project_goal,
                                "credentials"       => $request->credentials,
                            ]);


            $fileshash = $submit->fileshash;
            $path = "zeigprofilaward".DIRECTORY_SEPARATOR.((env("APP_ENV") != "production") ? 'dev'.DIRECTORY_SEPARATOR : '')."submits";

            if($request->company_logo){
                // Verzeichnis, in dem das Logo gespeichert wird
                $logoPath = $path.DIRECTORY_SEPARATOR.$submit->id;

                // Alle Dateien im Verzeichnis durchsuchen, die mit 'companylogo_' beginnen
                $files = Storage::disk('public')->files($logoPath);

                foreach($files as $file){
                    if(Str::startsWith(basename($file), 'companylogo_')){
                        // Lösche die Datei
                        Storage::disk('public')->delete($file);
                    }
                }

                Storage::disk('public')->putFileAs($path.DIRECTORY_SEPARATOR.$submit->id, $request->company_logo, "companylogo_{$fileshash}.{$request->company_logo->getClientOriginalExtension()}");
            }
            if($request->project_picture){
                // Verzeichnis, in dem das Logo gespeichert wird
                $logoPath = $path.DIRECTORY_SEPARATOR.$submit->id;

                // Alle Dateien im Verzeichnis durchsuchen, die mit 'companylogo_' beginnen
                $files = Storage::disk('public')->files($logoPath);

                foreach($files as $file){
                    if(Str::startsWith(basename($file), 'unternehmenspraesentation_')){
                        // Lösche die Datei
                        Storage::disk('public')->delete($file);
                    }
                }

                Storage::disk('public')->putFileAs($path.DIRECTORY_SEPARATOR.$submit->id, $request->project_picture, "unternehmenspraesentation_{$fileshash}.{$request->project_picture->getClientOriginalExtension()}");
            }
            if($request->company_picture){
                // Verzeichnis, in dem das Logo gespeichert wird
                $logoPath = $path.DIRECTORY_SEPARATOR.$submit->id;

                // Alle Dateien im Verzeichnis durchsuchen, die mit 'companylogo_' beginnen
                $files = Storage::disk('public')->files($logoPath);

                foreach($files as $file){
                    if(Str::startsWith(basename($file), 'umsetzung_')){
                        // Lösche die Datei
                        Storage::disk('public')->delete($file);
                    }
                }

                Storage::disk('public')->putFileAs($path.DIRECTORY_SEPARATOR.$submit->id, $request->company_picture, "umsetzung_{$fileshash}.{$request->company_picture->getClientOriginalExtension()}");
            }
            if($request->company_skizze){
                // Verzeichnis, in dem das Logo gespeichert wird
                $logoPath = $path.DIRECTORY_SEPARATOR.$submit->id;

                // Alle Dateien im Verzeichnis durchsuchen, die mit 'companylogo_' beginnen
                $files = Storage::disk('public')->files($logoPath);

                foreach($files as $file){
                    if(Str::startsWith(basename($file), 'referenzen_')){
                        // Lösche die Datei
                        Storage::disk('public')->delete($file);
                    }
                }

                Storage::disk('public')->putFileAs($path.DIRECTORY_SEPARATOR.$submit->id, $request->company_skizze, "referenzen_{$fileshash}.{$request->company_skizze->getClientOriginalExtension()}");
            }


            return redirect()->back();
        }


        public function homepageIndex(){

            $inputs = config('zeigprofilaward.homepage-form');

            $formFields = [];

            $homepage = ZeigProfilAwardHomepage::first();

            if(! $homepage instanceof ZeigProfilAwardHomepage){
                return 'Error, ZeigProfilAwardHomepage has no rows';
            }

            foreach($inputs as $current){
                if(! is_array($current)){
                    $formFields [] = $current;
                    continue;
                }

                $name = $current["inputName"];
                $type = $current["inputType"];

                if($type === "file") $current['currentPreview'] = $homepage->getFullPath($name);
                $current['value'] = $homepage[ $name ];

                $formFields [] = $current;
            }

            return view('backend.zeigprofilaward.homepage.index', ['formFields' => $formFields]);
        }


        public function homepagePost(Request $request){

            $inputs = config('zeigprofilaward.homepage-form');

            $rules = [];
            foreach($inputs as $field){
                if(! empty($field['validator'])){
                    $rules[ $field['inputName'] ] = $field['validator'];
                }
            }



            $request->validate($rules);

            $homepage = ZeigProfilAwardHomepage::first();

            if(! $homepage instanceof ZeigProfilAwardHomepage){
                return 'Error, ZeigProfilAwardHomepage has no rows';
            }

            foreach($inputs as $current){

                if(! is_array($current)) continue;

                $name = $current["inputName"];
                $fileType = ($current["inputType"] === "file") ? $current["fileType"] : null;



                if($fileType === "image"){
                    if($request->file($name)){
                        $homepage->saveFileImage($name, $request->file($name));
                    }
                }else{
                    $homepage->{$name} = $request->get($name);
                }
            }

            $homepage->save();

            return back()->with('notieSuccess', 'Erfolgreich gespeichert');
        }


        public function submitsIndex(){

            $data = [];
            $data["submits_coll"] = ZeigProfilAwardSubmit::all();
            $data["categories_arr"] = ZeigProfilAwardCategory::select("id", "title")->get()->keyby("id")->toArray();

            return view('backend.zeigprofilaward.submits.index', $data);
        }


        public function submitsShow(ZeigProfilAwardSubmit $submit){


            $data = [];
            $data["submit"] = $submit;
            $data["categories_arr"] = ZeigProfilAwardCategory::select("id", "title")->get()->keyby("id")->toArray();
            $data["fileshash"] = $submit->fileshash;
            $data["filespath"] = "zeigprofilaward".DIRECTORY_SEPARATOR.((env("APP_ENV") != "production") ? 'dev'.DIRECTORY_SEPARATOR : '')."submits".DIRECTORY_SEPARATOR.$submit->id;
            $data['categories'] = ZeigProfilAwardCategory::whereNotNull('title')->orderBy('sort')->get();
            $data["files"] = @scandir(storage_path("app/public/".$data["filespath"]));
            if(is_array($data["files"])) $data["files"] = array_filter($data["files"], fn($i) => ! in_array($i, [".", ".."]));

            return view("backend.zeigprofilaward.submits.show", $data);
        }


        public function export_excel(){
            $getAllSubmits = ZeigProfilAwardSubmit::all();
            $getAllSubmits = $getAllSubmits->map(function($submit){
                return [
                    "firstname" => $submit->firstname,
                    "lastname" => $submit->lastname,
                    "email" => $submit->email,
                ];
            });

            return (new FastExcel($getAllSubmits))->download("zeigprofilaward.xlsx");
        }
    }


