<?php

    namespace App\Http\Controllers\Freizeitvoting;

    use App\Http\Controllers\Controller;
    use App\Models\Email;
    use App\Models\Freizeit\FreizeitBesteGerichte;
    use App\Models\Freizeit\FreizeitBesteGerichteVoting;
    use App\Models\Freizeit\FreizeitGasthaus;
    use App\Models\Freizeit\FreizeitGasthausVoting;
    use App\Models\Freizeit\FreizeitJahreswahlGasthausVoting;
    use App\MPClasses\GoodCaptcha;
    use Illuminate\Http\Request;
    use MPHelper;


    class FreizeitVotingController extends Controller{

        public function importGasthaeuser(){
            if(env("APP_ENV") == "production" && ! in_array(request()->ip(), config("freizeitvoting.ips"))) die("Keine Berechtigung");

            if($daten = file("storage/app/public/freizeitvoting/import_gasthaeuser.csv", FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES)){

                //dd($daten);
                foreach($daten as $key => $value){
                    if($key > 0){
                        $zeile = explode(";", $value);
                        //dd($zeile);
                        if(! empty($zeile[0])){

                            //dd($zeile[0]);

                            FreizeitGasthaus::updateOrCreate([
                                                                 "identifier_id" => intval(trim($zeile[0]))
                                                             ], [
                                                                 "title"      => utf8_encode(trim($zeile[1])),
                                                                 "plz"        => trim($zeile[2]),
                                                                 "ort"        => utf8_encode(trim($zeile[3])),
                                                                 "bundesland" => utf8_encode(trim($zeile[4])),
                                                                 "active"     => trim($zeile[5]),
                                                             ]);
                        }
                    }
                }
                return 0;
            }
            return 1;
        }


        public function importGerichte(){
            if(env("APP_ENV") == "production" && ! in_array(request()->ip(), config("freizeitvoting.ips"))) die("Keine Berechtigung");

            if($daten = file("storage/app/public/freizeitvoting/import_gerichte.csv", FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES)){

                //dd($daten);
                foreach($daten as $key => $value){
                    if($key > 0){
                        $zeile = explode(";", trim($value));
                        //dd($zeile);
                        if(! empty($zeile[0])){
                            FreizeitBesteGerichte::updateOrCreate([
                                                                      "identifier_id" => intval(trim($zeile[0]))
                                                                  ], [
                                                                      "title"      => utf8_encode(trim($zeile[1])),
                                                                      "plz"        => trim($zeile[2]),
                                                                      "ort"        => utf8_encode(trim($zeile[3])),
                                                                      "bundesland" => utf8_encode(trim($zeile[4])),
                                                                      "active"     => trim($zeile[5]),
                                                                      "kategorie"  => trim($zeile[6])
                                                                  ]);
                        }
                    }
                }
                return 0;
            }
            return 1;
        }


        public function index(){

            $data["testing"] = false;
            $data["beendet"] = false;
            $data["nominierungActive"] = true;
            $data["votingActive"] = true;

            if(in_array(request()->ip(), config("freizeitvoting.ips")) && env("APP_ENV") == "local") $data["testing"] = true;

            //Nachnominierung läuft ab 29.07 bis inkl. 08.08
            if(now()->format("Ymd") < "20250729") $data["nominierungActive"] = false;
            if(now()->format("Ymd") > "20250808") $data["nominierungActive"] = false;

            //Voting läuft von 11.08 um 09.00 bis inkl. 30.09
            if(now()->format("YmdHi") < "202508110900") $data["votingActive"] = false;
            if(now()->format("Ymd") > "20250930") $data["beendet"] = true;

            return view("freizeitvoting.index", $data);
        }


        public function indexWirtshaeuser(){

            //Voting läuft von 11.08 um 09.00 bis inkl. 30.09
            if(now()->format("YmdHi") < "202508110900" && ! in_array(request()->ip(), config("freizeitvoting.ips"))) return redirect("/");
            if(now()->format("Ymd") > "20250930" && ! in_array(request()->ip(), config("freizeitvoting.ips"))) return redirect("/");

            $data["gasthaeuser"] = [];
            if($gasthaeuser = FreizeitGasthaus::where("active", 1)->orderby("bundesland")->orderby("plz")->get()){
                foreach($gasthaeuser as $gasthaus){
                    $data["gasthaeuser"][ $gasthaus->bundesland ][] = $gasthaus;
                }
            }

            return view("freizeitvoting.index_gasthaeuser", $data);
        }


        public function storeWirtshaeuser(Request $request){

            //Voting läuft von 11.08 um 09.00 bis inkl. 30.09
            if(now()->format("YmdHi") < "202508110900" && ! in_array(request()->ip(), config("freizeitvoting.ips"))) return redirect("/");
            if(now()->format("Ymd") > "20250930" && ! in_array(request()->ip(), config("freizeitvoting.ips"))) return redirect("/");

            $messages = [
                'freizeit_gasthaus_id.required' => 'Bitte wähle ein Gasthaus aus.',
                'freizeit_gasthaus_id.integer'  => 'Bitte wähle ein gültiges Gasthaus.',
                'kategorie1.required'           => 'Bitte bewerte die Kategorie "Essen".',
                'kategorie1.between'            => 'Bitte treffe eine korrekte Auswahl für die Kategorie "Essen".',
                'kategorie2.required'           => 'Bitte bewerte die Kategorie "Service".',
                'kategorie2.between'            => 'Bitte treffe eine korrekte Auswahl für die Kategorie "Service".',
                'kategorie3.required'           => 'Bitte bewerte die Kategorie "Getränke".',
                'kategorie3.between'            => 'Bitte treffe eine korrekte Auswahl für die Kategorie "Getränke".',
                'kategorie4.required'           => 'Bitte bewerte die Kategorie "Ambiente".',
                'kategorie4.between'            => 'Bitte treffe eine korrekte Auswahl für die Kategorie "Ambiente".',
                'solution.required'             => 'Sicherheitscode nicht gültig.'
            ];

            $validation_arr = [
                'freizeit_gasthaus_id' => 'required|integer',
                'kategorie1'           => 'required|between:0,50',
                'kategorie2'           => 'required|between:0,10',
                'kategorie3'           => 'required|between:0,15',
                'kategorie4'           => 'required|between:0,25',
                'solution'             => "required|string"
            ];

            $this->validate($request, $validation_arr, $messages);

            if(! GoodCaptcha::captchaValidate($request->solution)) abort(400);

            $useragent = MPHelper::getUserAgent();

            FreizeitGasthausVoting::updateOrCreate([
                                                       'jahr'                 => today()->format("Y"),
                                                       'freizeit_gasthaus_id' => $request->freizeit_gasthaus_id,
                                                       'session_id'           => session()->getId()
                                                   ],
                                                   [
                                                       'kategorie1'     => $request->kategorie1,
                                                       'kategorie2'     => $request->kategorie2,
                                                       'kategorie3'     => $request->kategorie3,
                                                       'kategorie4'     => $request->kategorie4,
                                                       'ip'             => request()->ip(),
                                                       'agent_platform' => (! empty($useragent["platform"])) ? $useragent["platform"] : '',
                                                       'agent_browser'  => (! empty($useragent["browser"])) ? $useragent["browser"] : '',
                                                       'agent_version'  => (! empty($useragent["version"])) ? $useragent["version"] : ''
                                                   ]
            );

            return redirect("gasthaeuser")->with("message", "Dein Voting wurde erfolgreich gespeichert!");
        }


        public function indexWirtshaeuserNominieren(){

            //Nachnominierung läuft ab 29.07 bis inkl. 08.08
            if(now()->format("Ymd") < "20250729" && ! in_array(request()->ip(), config("freizeitvoting.ips"))) return redirect("/");
            if(now()->format("Ymd") > "20250808" && ! in_array(request()->ip(), config("freizeitvoting.ips"))) return redirect("/");

            $data["gasthaeuser"] = [];
            if($gasthaeuser = FreizeitGasthaus::where("active", 1)->orderby("bundesland")->orderby("plz")->get()){
                foreach($gasthaeuser as $gasthaus){
                    $data["gasthaeuser"][ $gasthaus->bundesland ][] = $gasthaus;
                }
            }

            return view("freizeitvoting.index_gasthaeuser_nominierung", $data);
        }


        public function storeWirtshaeuserNominieren(Request $request){

            //Nachnominierung läuft ab 29.07 bis inkl. 08.08
            if(now()->format("Ymd") < "20250729" && ! in_array(request()->ip(), config("freizeitvoting.ips"))) return redirect("/");
            if(now()->format("Ymd") > "20250808" && ! in_array(request()->ip(), config("freizeitvoting.ips"))) return redirect("/");

            $messages = [
                'title.required'    => 'Bitte den Namen des Gasthauses angeben.',
                'title.string'      => 'Bitte einen gültigen Namen des Gasthauses angeben.',
                'plz.required'      => 'Bitte die PLZ des Gasthauses angeben.',
                'plz.string'        => 'Bitte eine gültige PLZ des Gasthauses angeben.',
                'ort.required'      => 'Bitte den Ort des Gasthauses angeben.',
                'ort.string'        => 'Bitte einen gültigen Ort des Gasthauses angeben.',
                'solution.required' => 'Sicherheitscode nicht gültig.'
            ];

            $validation_arr = [
                'title'    => 'required|string',
                'plz'      => 'required|string',
                'ort'      => 'required|string',
                'solution' => "required|string"
            ];

            $this->validate($request, $validation_arr, $messages);

            if(! GoodCaptcha::captchaValidate($request->solution)) abort(400);

            $useragent = MPHelper::getUserAgent();

            $mail_arr = [
                "Name des Gasthauses" => strip_tags(trim($request->title)),
                "PLZ"                 => strip_tags(trim($request->plz)),
                "ORT"                 => strip_tags(trim($request->ort)),
                "Session ID"          => session()->getId(),
                "IP"                  => request()->ip(),
                "Agent-Platform"      => (! empty($useragent["platform"])) ? $useragent["platform"] : '',
                "Agent-Browser"       => (! empty($useragent["browser"])) ? $useragent["browser"] : '',
                "Agent-Version"       => (! empty($useragent["version"])) ? $useragent["version"] : ''
            ];

            $text = "";
            foreach($mail_arr as $key => $value){
                $text .= "<b>".$key.":</b> ".$value."<br>";
            }

            $text .= "<br><br><b>Für csv:</b> ".str_replace(";", " ", strip_tags(trim($request->title))).";".str_replace(";", " ", strip_tags(trim($request->plz))).";".str_replace(";", " ", strip_tags(trim($request->ort)));

            Email::newRawMail(env("MAIL_EMPFAENGER_FREIZEIT"), "intern", "[VOTING.FREIZEIT.AT] Gasthaus nominieren", $text);

            return redirect("gasthaus-nominieren")->with("message", "Deine Nominierung wurde erfolgreich gespeichert!");
        }


        public function indexBesteGerichte(){

            die("Kein Voting verfügbar");

            //Voting startet am 28.06 um 09.00
            if(env("APP_ENV") == "production" && ! in_array(request()->ip(), config("freizeitvoting.ips")) && now()->format("YmdHi") < "202406280900") return redirect("/");

            //Voting endet am 22.08
            if(env("APP_ENV") == "production" && ! in_array(request()->ip(), config("freizeitvoting.ips")) && now()->format("Ymd") > "20240827") return redirect("/");

            $data["kategorien"] = [];
            if($bestegerichte = FreizeitBesteGerichte::where("active", 1)->orderby("id")->get()){
                foreach($bestegerichte as $tmp){
                    $data["kategorien"][ $tmp->kategorie ][] = [
                        "id"    => $tmp->identifier_id,
                        "title" => $tmp->title." (".$tmp->plz." ".$tmp->ort.")"
                    ];
                }
            }

            return view("freizeitvoting.index_bestegerichte", $data);
        }


        public function storeBesteGerichte(Request $request){

            die("Kein Voting verfügbar");

            //Voting startet am 24.06 um 09.00
            if(env("APP_ENV") == "production" && ! in_array(request()->ip(), config("freizeitvoting.ips")) && now()->format("YmdHi") < "202407240900") return redirect("/");

            //Voting endet am 22.08
            if(env("APP_ENV") == "production" && ! in_array(request()->ip(), config("freizeitvoting.ips")) && now()->format("Ymd") > "20240827") return redirect("/");

            $messages = [
                'kategorie1.integer' => 'Bitte treffe eine gültige Auswahl für das knusprigste Schnitze.',
                'kategorie2.integer' => 'Bitte treffe eine gültige Auswahl für den besten Tafelspitz.',
                'kategorie3.integer' => 'Bitte treffe eine gültige Auswahl für die saftigsten Käsespätzle.',
                'kategorie4.integer' => 'Bitte treffe eine gültige Auswahl für den fluffigsten Kaiserschmarrn.',
                'kategorie5.integer' => 'Bitte treffe eine gültige Auswahl für das beste Backhenderl.',
                'solution.required'  => 'Sicherheitscode nicht gültig.'
            ];

            $validation_arr = [
                'kategorie1' => 'nullable|integer',
                'kategorie2' => 'nullable|integer',
                'kategorie3' => 'nullable|integer',
                'kategorie4' => 'nullable|integer',
                'kategorie5' => 'nullable|integer',
                'solution'   => "required|string"
            ];

            $this->validate($request, $validation_arr, $messages);

            if(! GoodCaptcha::captchaValidate($request->solution)) abort(400);

            $useragent = MPHelper::getUserAgent();

            FreizeitBesteGerichteVoting::updateOrCreate([
                                                            'jahr'       => today()->format("Y"),
                                                            'session_id' => session()->getId()
                                                        ],
                                                        [
                                                            'kategorie1'     => $request->kategorie1,
                                                            'kategorie2'     => $request->kategorie2,
                                                            'kategorie3'     => $request->kategorie3,
                                                            'kategorie4'     => $request->kategorie4,
                                                            'kategorie5'     => $request->kategorie5,
                                                            'ip'             => request()->ip(),
                                                            'agent_platform' => (! empty($useragent["platform"])) ? $useragent["platform"] : '',
                                                            'agent_browser'  => (! empty($useragent["browser"])) ? $useragent["browser"] : '',
                                                            'agent_version'  => (! empty($useragent["version"])) ? $useragent["version"] : ''
                                                        ]
            );

            return redirect("beste-gerichte")->with("message", "Dein Voting wurde erfolgreich gespeichert!");
        }


        //JAHRESWAHL
        public function indexGasthausJahreswahl($freizeit_gasthaus_id){

            if(! is_numeric($freizeit_gasthaus_id)) die("Keine gültige URL");

            $data["freizeit_gasthaus_id"] = $freizeit_gasthaus_id;

            return view("freizeitvoting.jahreswahl.index", $data);
        }


        public function storeGasthausJahreswahl(Request $request, $freizeit_gasthaus_id){

            if(! is_numeric($freizeit_gasthaus_id)) die("Keine gültige URL");

            $messages = [
                'kategorie1.required' => 'Bitte bewerte die Kategorie "Essen".',
                'kategorie1.between'  => 'Bitte treffe eine korrekte Auswahl für die Kategorie "Essen".',
                'kategorie2.required' => 'Bitte bewerte die Kategorie "Service".',
                'kategorie2.between'  => 'Bitte treffe eine korrekte Auswahl für die Kategorie "Service".',
                'kategorie3.required' => 'Bitte bewerte die Kategorie "Getränke".',
                'kategorie3.between'  => 'Bitte treffe eine korrekte Auswahl für die Kategorie "Getränke".',
                'kategorie4.required' => 'Bitte bewerte die Kategorie "Ambiente".',
                'kategorie4.between'  => 'Bitte treffe eine korrekte Auswahl für die Kategorie "Ambiente".',
                'captcha.required'    => 'Bitte das Captcha ausfüllen.',
                'captcha.captcha'     => 'Bitte das Captcha ausfüllen.'
            ];

            $validation_arr = [
                'kategorie1' => 'required|between:0,50',
                'kategorie2' => 'required|between:0,10',
                'kategorie3' => 'required|between:0,15',
                'kategorie4' => 'required|between:0,25',
                'captcha'    => 'required|captcha'
            ];

            $this->validate($request, $validation_arr, $messages);

            $useragent = MPHelper::getUserAgent();

            FreizeitJahreswahlGasthausVoting::updateOrCreate([
                                                                 'jahr'                 => today()->format("Y"),
                                                                 'freizeit_gasthaus_id' => $freizeit_gasthaus_id,
                                                                 'session_id'           => session()->getId()
                                                             ],
                                                             [
                                                                 'kategorie1'     => $request->kategorie1,
                                                                 'kategorie2'     => $request->kategorie2,
                                                                 'kategorie3'     => $request->kategorie3,
                                                                 'kategorie4'     => $request->kategorie4,
                                                                 'ip'             => request()->ip(),
                                                                 'agent_platform' => (! empty($useragent["platform"])) ? $useragent["platform"] : '',
                                                                 'agent_browser'  => (! empty($useragent["browser"])) ? $useragent["browser"] : '',
                                                                 'agent_version'  => (! empty($useragent["version"])) ? $useragent["version"] : ''
                                                             ]
            );

            return redirect("jahreswahl/gasthaus/".$freizeit_gasthaus_id)->with("message", "Dein Voting wurde erfolgreich gespeichert!");
        }
    }

