<?php

namespace App\Http\Controllers\Speakout;

use App\Http\Controllers\Controller;
use App\Models\Speakout\SpeakoutContent;
use App\Models\Speakout\SpeakoutDocument;
use App\Models\Speakout\SpeakoutGalleryImage;
use App\Models\Speakout\SpeakoutGreenWorld;
use App\Models\Speakout\SpeakoutHomepage;
use App\Models\Speakout\SpeakoutNews;
use App\Models\Speakout\SpeakoutPartner;
use App\Models\Speakout\SpeakoutPress;
use App\Models\Speakout\SpeakoutProgram;
use App\Models\Speakout\SpeakoutSpeaker;
use Illuminate\Support\Facades\Storage;

class SpeakOutPageController extends Controller
{


    public function index(){


        $speakers = SpeakoutSpeaker::getWholeSpeakersList(true,   true);

 /*       if(env('APP_ENV') === "local"){
            $testSpeakerBiggerPhoto = SpeakoutSpeaker::where('name', "like", 'Anja')->first();

            $speakers[0] = $testSpeakerBiggerPhoto;
        }*/


        $partners = SpeakoutPartner::whereNotNull('logoPath')->where('type', 'partner')->orderBy('sort')->get();
        $sponsors = SpeakoutPartner::whereNotNull('logoPath')->where('type', 'sponsor')->orderBy('sort')->get();

        $speakoutHomepage = SpeakoutHomepage::first();

        if (!$speakoutHomepage instanceof SpeakoutHomepage) abort(400);

        return view("speakout.index", [
            'speakers' => $speakers,
            'newsArray' => SpeakoutNews::whereNotNull('title')->limit(5)->orderBy('sort')->get(),
            'greenWorldArray' => SpeakoutGreenWorld::whereNotNull('title')->limit(5)->orderBy('created_at', 'DESC')->get(),
            "partners" => $partners,
            "sponsors" => $sponsors,

            "allImages" => $speakoutHomepage->getImageArray(),
            "intro" => [
                "title" => $speakoutHomepage->intro_title,
                "title_mobile" => $speakoutHomepage->intro_title_mobile,
                "subtitle" => $speakoutHomepage->intro_subtitle,
            ],
            "info" => [
                "title" => $speakoutHomepage->info_title,
                "text" => $speakoutHomepage->info_text,
            ]
        ]);
    }


    public function speaker($speakerNameSlug = null)
    {


        $speakers = SpeakoutSpeaker::getWholeSpeakersList(false);

        $selectedSpeaker = $speakers->contains(function ($speaker) use ($speakerNameSlug) {
            return $speaker->name_slug === $speakerNameSlug;
        }) ? $speakerNameSlug
            : null;

        return view("speakout.speaker_overview", ['speakers' => $speakers, 'selectedSpeaker' => $selectedSpeaker]);

    }


    public function greenWorld(string $seo_url = "")
    {
        if ($seo_url) {
            return view("speakout.post.details", [
                'selectedPost' => SpeakoutGreenWorld::whereNotNull('title')->where('seo_url', $seo_url)->firstOrFail()
            ]);
        }

        $posts = SpeakoutGreenWorld::whereNotNull('title')->orderBy('created_at', 'DESC')->get();

        return view("speakout.post.overview", [
                "post_section_name" => "Grüne Welt",
                'allPosts' => $posts,
                "description" => SpeakoutContent::getByKey('green_world_desc')
            ]
        );

    }

    public function news(string $seo_url = "")
    {
        if ($seo_url) {
            return view("speakout.post.details", [
                'selectedPost' => SpeakoutNews::whereNotNull('title')->where('seo_url', $seo_url)->firstOrFail()
            ]);
        }

        $posts = SpeakoutNews::whereNotNull('title')->orderBy('sort')->get();

        return view("speakout.post.overview", [

            "post_section_name" => "Highlights",
            'allPosts' => $posts,
            "description" => SpeakoutContent::getByKey('highlights_desc')
        ]);

    }

    public function cookiepolicy()
    {
        return view("speakout.cookiepolicy", []);
    }

    public function showDocument($documentName)
    {
        $file = SpeakoutDocument::where('name', $documentName)->where('downloadable', true)->firstOrFail();

        // Specify the new filename you want the user to see
        $displayAsName = ($file->label ?: 'file-' . $file->id) . "." . pathinfo($file->path, PATHINFO_EXTENSION);

        // Determine the path to the file
        $filePath = storage_path("app/public/" . $file->path);

        // Prepare the response with the file content
        return response()->file($filePath, [
            'Content-Disposition' => 'inline; filename="' . $displayAsName . '"'
        ]);

    }


    public function program($selectedId = null)
    {

        //die("Das Programm wird bald veröffentlicht");


        return view("speakout.program",
            [
                'programs' => SpeakoutProgram::orderBy('starts_at', 'ASC')->get(),
                'rooms' => SpeakoutProgram::getRooms(),
                'selectedProgram' => SpeakoutProgram::find($selectedId),
                'shareImage' => ($tmp = SpeakoutHomepage::first()) ? $tmp->getImageArray()['hero_image_mobile'] : ''
            ]);
    }

    public function fotogalerie()
    {

        $images = SpeakoutGalleryImage::where("display", 1)->get()->reverse();

        return view("speakout.fotogalerie", ["images" => $images]);
    }
    public function insights()
    {
        return view("speakout.insights", []);
    }

    public function press()
    {
        return view("speakout.press", ["speakoutPressList" => SpeakoutPress::whereNotNull("name")->get()]);
    }

    public function pressDownload(SpeakoutPress $speakoutPress)
    {



        $fileFullPath =  Storage::disk('public')->path($speakoutPress->image);
        return response()->download($fileFullPath);
    }

}
