<?php

namespace App\Http\Controllers\Speakout;

use App\Http\Controllers\Controller;
use App\Models\Speakout\SpeakoutGalleryImage;
use Illuminate\Http\Request;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\ImageManager;
class GalleryBackendController extends Controller
{
    public function index()
    {

        $images = SpeakoutGalleryImage::all()->reverse();

        return view('backend.speakout.gallery.index', ["images" => $images]);

    }


    public function show($id)
    {

        if ($id == 'new') {
            $image = new SpeakoutGalleryImage();
        } else {
            $image = SpeakoutGalleryImage::findOrFail($id);
        }


        return view('backend.speakout.gallery.show', ['image' => $image]);

    }

    public function ajaxDisplayedImages(Request $request)
    {

        $request->validate([
            "displayedImages" => "required|array",
        ]);

        $displayed = [];
        $hidden = [];

        foreach ($request->input('displayedImages') as $id => $isDisplayed) {
            if ($isDisplayed) {
                $displayed[] = $id;
            } else {
                $hidden[] = $id;
            }
        }

        SpeakoutGalleryImage::whereIn('id', $displayed)->update(['display' => true]);
        SpeakoutGalleryImage::whereIn('id', $hidden)->update(['display' => false]);


        return response()->json(["success" => true]);

    }

    public function delete($id)
    {
        $image = SpeakoutGalleryImage::findOrFail($id);
        $image->delete();

        return redirect('backend/speakout/gallery')->with('notieSuccess', 'Das Foto wurde gelöscht');
    }



    public function store($id, Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            // 'image' => 'required|image|mimes:jpg,jpeg,png,gif|max:2048'
        ]);


        $file = $request->file('image');


        if ($id == 'new' ) {

            $image = SpeakoutGalleryImage::create([
                'title' => $request->title,
            ]);

            if ($file instanceof UploadedFile) {
                $image->saveImage($file);
            }else{
                return redirect()->back()->withErrors( 'Bild fehlt');
            }
        } else {
            $image = SpeakoutGalleryImage::where('id', $id)->firstOrFail();


            $image->update(['title' => $request->title]);

            if ($file instanceof UploadedFile) {
                $image->saveImage($file);
            }

        }


        return redirect('backend/speakout/gallery')->with('notieSuccess', 'Gespeichert');
    }

}
