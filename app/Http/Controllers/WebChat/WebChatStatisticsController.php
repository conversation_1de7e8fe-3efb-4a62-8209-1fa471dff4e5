<?php

namespace App\Http\Controllers\WebChat;


use App\Http\Controllers\Controller;
use App\Models\WebChat\WebChatConversation;
use App\Models\WebChat\WebChatCronjob;
use Carbon\Carbon;
use Illuminate\Support\Str;
use Rap2hpoutre\FastExcel\FastExcel;

class WebChatStatisticsController extends Controller
{

    private array $tabs;

    public function __construct()
    {
        $this->tabs = [
            [
                "title" => "Einzelnachweis",
                "showDate" => true,
                "updatedAt" => null
            ],
            [
                "title" => "Auswertung im Halbstunden-Takt",
                "showDate" => true,
                "updatedAt" => null
            ],
            [
                "title" => "Monatsansicht",
                "showDate" => false,
                "updatedAt" => WebChatCronjob::monthlyStatisticsUpdatedAt()
            ]
        ];
    }


    private function tableSwitcher($index, $options)
    {

        if (!is_numeric($index)) return null;

        $index = (int)$index;


        switch ($index) {
            case 0:
                $table = $this->getChatsWithDetails($options['dateRange']);
                break;
            case 1:
                $table = $this->getDailyChatsInTimeFrameTable($options['dateRange']);
                break;
            case 2:
                $table = WebChatCronjob::retrieveMonthlyStatistics();
                break;
            default:
                $table = null;
                break;
        }

        return $table;
    }


    private function getDateRange()
    {
        if (request()->has('from') && request()->has('to')) {
            try {
                $temp = [
                    "from" => Carbon::createFromFormat("Y-m-d", request()->get('from'))->startOfDay(),
                    "to" => Carbon::createFromFormat("Y-m-d", request()->get('to'))->endOfDay(),
                ];
            } catch (\Exception $exception) {
                abort(400);
            }

        } else {
            $temp = ['from' => now()->startOfDay(), 'to' => now()->endOfDay()];
        }


        $dayBefore = clone $temp['from'];
        $dayBefore = $dayBefore->subDay();

        $dayAfter = clone $temp['to'];
        $dayAfter = $dayAfter->addDay();

        return [
            "from" => $temp['from'],
            "to" => $temp['to'],
            "dayBefore" => $dayBefore,
            "dayAfter" => $dayAfter->isAfter(now()->endOfDay()) ? now()->endOfDay() : $dayAfter
        ];

    }

    public function download($index = 0)
    {
        $options['dateRange'] = $this->getDateRange();

        $table = $this->tableSwitcher($index, $options);

        if (!$table) abort(404);

        $title = Str::slug($this->tabs[$index]['title']) . '_' . $options['dateRange']['from']->format('d-m-Y') . '-' . $options['dateRange']['to']->format('d-m-Y');

        $readyTo = collect($table)->map(function ($el) {
            return $el['data'];
        });

        return (new FastExcel($readyTo))->download($title . '.xlsx');

    }

    public function index($index = 0)
    {


        if (!array_key_exists($index, $this->tabs)) {
            abort(404);
        }


        $options['dateRange'] = $this->getDateRange();


        $table = $this->tableSwitcher($index, $options);


        $data = [
            "tabs" => $this->tabs,
            "table" => $table ?? [],
            "selectedIndex" => $index,
            "dates" => $options['dateRange'],

        ];


        return view('backend.webchat-statistics.index', $data);
    }


    function formatTime($seconds)
    {
        $h = (string)(floor($seconds / 3600));
        $m = (string)(floor(($seconds % 3600) / 60));
        $s = (string)($seconds % 60);

        if (strlen($h) === 1) $h = "0$h";
        if (strlen($m) === 1) $m = "0$m";
        if (strlen($s) === 1) $s = "0$s";

        return "$h:$m:$s";
    }


    public function getMonthTable(): array
    {


        ini_set('max_execution_time', '300');


        $from = now()->subMonths(5)->startOfMonth();

        $groupedConversations = WebChatConversation::whereDate('created_at', '>=', $from)
            ->orderBy('created_at', 'DESC')
            ->get()
            ->groupBy(function ($date) {
                return $date->created_at->format('m.Y');
            });


        $res = [];

        foreach ($groupedConversations as $month => $conversations) {

            $groupedByMedium = $conversations->groupBy('medium');

            $dt = [];

            foreach (['krone', 'kurier', 'profil'] as $medium) {
                $dt[$medium] = (object)[
                    "workDuration" => !empty($groupedByMedium[$medium]) ?
                        $this->formatTime($groupedByMedium[$medium]->sum('duration'))
                        : $this->formatTime(0),

                    "workAvg" => !empty($groupedByMedium[$medium]) ?
                        $this->formatTime($groupedByMedium[$medium]->avg('duration'))
                        : $this->formatTime(0),
                    "amount" => !empty($groupedByMedium[$medium]) ?
                        $groupedByMedium[$medium]->count()
                        : 0,
                ];
            }

            $res[] = [
                "data" => [
                    "Datum" => $month,
                    "Gesamt" => $conversations->count(),


                    "Ges. Bearbeitungsdauer" => $this->formatTime($conversations->sum('duration')),
                    "Bearbeitungsdauer (durchschnitt)" => $this->formatTime($conversations->avg('duration')),

                    "Krone Bearbeitungsdauer" => $dt['krone']->workDuration,
                    "Krone Bearbeitungsdauer (durchschnitt)" => $dt['krone']->workAvg,

                    "Kurier Bearbeitungsdauer" => $dt['kurier']->workDuration,
                    "Kurier Bearbeitungsdauer (durchschnitt)" => $dt['kurier']->workAvg,

                    "Profil Bearbeitungsdauer" => $dt['profil']->workDuration,
                    "Profil Bearbeitungsdauer (durchschnitt)" => $dt['profil']->workAvg,

                    "Anzahl Krone" => $dt['krone']->amount,
                    "Anzahl Kurier" => $dt['kurier']->amount,
                    "Anzahl Profil" => $dt['profil']->amount,
                ]
            ];


        }

        return $res;
    }

    private function getDailyChatsInTimeFrameTable(array $dateRange, int $timeFrame = 30)
    {
        // Set default date to today if not provided


        // Get chat conversations for the provided date
        $chatConversationGroups = WebChatConversation::whereBetween('created_at', [$dateRange['from'], $dateRange['to']])
            ->orderBy('created_at')
            ->get()
            ->groupBy(function ($date) {
                return $date->created_at->format('Y-m-d');
            });


        // Initialize variables
        $res = [];


        foreach ($chatConversationGroups as $date => $chatConversations) {
            $date = Carbon::createFromFormat('Y-m-d', $date);

            $currentTime = $date->clone()->setTime(7, 0);
            $endTime = $date->clone()->setTime(17, 0);

            // Loop through time frames
            while ($currentTime->isBefore($endTime)) {
                // Define time frame
                $from = $currentTime->clone();
                $to = $currentTime->clone()->addMinutes($timeFrame);

                // Filter conversations within the current time frame
                $chats = $chatConversations->filter(function ($chat) use ($from, $to) {
                    return $chat->created_at->isBetween($from, $to);
                });


                foreach ($chats->pluck('medium')->unique()->toArray() as $medium) {


                    $currentMediumChats = $chats->filter(function ($chat) use ($medium) {
                        return $chat->medium === $medium;
                    });

                    $durationAverage = $currentMediumChats->isEmpty() ? 0 : $currentMediumChats->avg('duration');

                    $waitingTimeAverage = $currentMediumChats->isEmpty() ? 0 : $currentMediumChats->avg('waitedForAgent');

                    $waitedBeforeClosing = $currentMediumChats->isEmpty() ? 0 : $currentMediumChats->avg('waitedBeforeClosing');


                    $feedback = $currentMediumChats->filter(function ($x) {
                        /*
                         * Not null feedbacks
                         */
                        return is_numeric($x->feedback);
                    });


                    $feedbackAverage = (string)$feedback->isEmpty() ? 0 : $feedback->avg('feedback');

                    if (Str::contains($feedbackAverage, ".")) {
                        $feedbackAverage = number_format($feedbackAverage, 2);
                    }


                    $acceptedChats = $currentMediumChats->filter(function ($chat) {
                        return $chat->conversationAgents()->count();
                    })->count();

                    $res[] = [
                        "isFullHour" => str_ends_with($to->format('H:i'), ':00'),
                        "data" => [
                            "Datum" => $currentTime->format('d.m.Y'),
                            "Uhr" => $from->format('H:i') . " - " . $to->format('H:i'),
                            "Herkunft" => Str::title($medium),
                            "Eingehende" => $currentMediumChats->count(),
                            "Angenommene" => $acceptedChats,
                            "Unbearbeitete" => $currentMediumChats->count() - $acceptedChats,
                            "Chatdauer-Schnitt" => $this->formatTime($durationAverage),
                            "Wartedauer-Schnitt (Angenommene Chats)" => $this->formatTime($waitingTimeAverage),
                            "Wartezeit bis zum Auflegen" => $this->formatTime($waitedBeforeClosing),
                            "Anzahl Bewertungen" => $feedback->count(),
                            "Qualitative Bewertung-Schnitt" => $feedbackAverage
                        ]];
                }


                // Move to the next time frame
                $currentTime = $to;
            }
        }


        return $res;
    }

    private function getChatsWithDetails(array $dateRange)
    {


        $chatConversations = WebChatConversation::whereBetween('created_at', [$dateRange['from'], $dateRange['to']])
            ->orderBy('created_at')->get();


        $data = [];


        foreach ($chatConversations as $conversation) {
            if ($conversation instanceof WebChatConversation) {

                /*                if($conversation->id == 27318){

                                    $x = [
                                        "connected_at" => $conversation->created_at->format('d.m.Y H:i'),
                                        "msg" => $conversation->messages->map(function ($msg){

                                            $x = $msg->attributesToArray();

                                            if($msg->created_at instanceof Carbon) {
                                                $x['when'] = $msg->created_at->format('d.m.Y H:i');

                                            }else{
                                                $x['when'] = 'unknown';
                                            }
                                            return $x;
                                        })->toArray()
                                    ];

                                    dd( $x, $this->formatTime($conversation->waitedForAgent));
                                }*/


                $allAgents = $conversation->agents->pluck('user');

                $mainAgent = $allAgents->first();

                $others = $allAgents->filter(function ($ag) use ($mainAgent) {
                    return $ag != $mainAgent;
                })->join(', ');

                $data[] = [
                    "data" => [
                        "ID" => $conversation->id,
                        "Datum" => $conversation->created_at->format('d.m.Y'),
                        "Uhr" => $conversation->created_at->format('H:i'),
                        "Herkunft" => Str::title($conversation->medium),
                        "Angenommen" => $mainAgent ? 'Ja' : 'Nein',
                        "Agent" => $mainAgent ?: '',
                        "Bearbeitung durch mehrere Agenten" => $others ?: '',
                        "Chatdauer" => $this->formatTime($conversation->duration),
                        "Wartedauer" => $this->formatTime($conversation->waitedForAgent),
                    ]
                ];
            }
        }

        return $data;

    }
}


