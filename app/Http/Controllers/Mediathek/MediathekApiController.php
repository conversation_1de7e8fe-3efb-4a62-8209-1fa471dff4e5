<?php

namespace App\Http\Controllers\Mediathek;

use App\Facades\AdminUser;
use App\Facades\MPHelper;
use App\Facades\StringsHelperFacade as Strng;
use App\Http\Controllers\Api\MediathekController as MPController;
use App\Http\Controllers\Controller;
use App\Models\Email;
use App\Models\Mediathek\MediathekBookmark;
use App\Models\Mediathek\MediathekEpaper;
use App\Models\Mediathek\MediathekEpaper as Epaper;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Intervention\Image\Facades\Image;


class MediathekApiController extends Controller
{


    private array $specialEditions;

    public function __construct()
    {
        $this->specialEditions = config('mediathek.config.specialEditions', []);
    }


    public function mediathek_view()
    {
        /*
         * Sets cookie, MP-doesnt set auth-cookie by default
         */

        if (AdminUser::auth()) {

            $mediathek_cookie = Str::uuid() . '-' . now()->format('Y-m-d_H:i');

            AdminUser::get()->update(['mediathek_cookie' => $mediathek_cookie]);

            $expiration = time() + (60 * 60 * 24 * 3);

            $cookie = Cookie::make('Mediathek', $mediathek_cookie, $expiration);

            return redirect(url('mediathek'))->withCookie($cookie);

        } else {
            $cookie = Cookie::forget('Mediathek');
            return redirect('/login')->withCookie($cookie);
        }
    }

    public function getMediathekUser(User $user)
    {

        $isAdmin = $user->hasPermission('mt-admin');

        return (object)[
            'id' => $user->id,
            'email' => $user->email,
            'firstname' => $user->firstname,
            'lastname' => $user->lastname,
            'isAdmin' => $isAdmin, // todo remove after MT - vorab
            "permissions" => (object)[
                "admin" => $isAdmin,
                'kronePreview' => $isAdmin || $user->hasPermission('mt-krone-preview'),
                'kurierPreview' => $isAdmin || $user->hasPermission('mt-kurier-preview'),
                'specialEditions' => $isAdmin || $user->hasPermission('mt-special-editions'),
            ]
        ];
    }


    public function add_remove_favorites(Request $request)
    {

        $user = $request->get('AuthUser');
        $user->favorites()->toggle([$request->epaper_id]);

        return response()->json(['success' => true]);
    }


    public function set_bookmark(Request $request)
    {
        if ($request->delete) {
            $paper = $this->delete_bookmark($request);
            $result = 'successfully deleted';
        } else {
            $paper = $this->add_bookmark($request);
            $result = 'successfully added';
        }
        if (!$paper) response()->json(['res' => 'ePaper not found'], 400);

        return response()->json(['res' => $result, 'ePaper' => $paper]);
    }

    private function add_bookmark($request)
    {
        $epaper = Epaper::find($request->epaperId);
        if ($epaper) {
            $filter = MediathekBookmark::all()->toArray();
            $filter = MPHelper::buildTree($filter, [], 0);
            $found = MPHelper::searchArray($filter, "id", $request->bookmarkId);
            $parents = explode(",", $found[0]["parents"]);
            $pivotData = array_fill(0, count($parents), ["automated" => 0]);
            $syncData = array_combine($parents, $pivotData);
            $epaper->bookmarks()->syncWithoutDetaching($syncData);

            // it also return the upgraded Ebook with bookmarks
            $book = Epaper::with(['bookmarks:id,description,parent'])
                ->select('id', 'description', 'ausgabe', 'path', 'hash', 'publication_date')
                ->find($request->epaperId);
            return $book;
        }
        return null;
    }

    private function delete_bookmark(Request $request)
    {
        $epaper = Epaper::select('id', 'description', 'ausgabe', 'path', 'hash', 'publication_date')
            ->find($request->epaperId);
        if ($epaper) {
            $epaper->bookmarks()->where(['epaper_id' => $request->epaperId])->detach();
            $epaper->bookmarks = [];
            return $epaper;
        }
        return null;
    }


    public function send_email(Request $request)
    {
        $user = $request->get('AuthUser');

        $subject = $request->subject . " - von " . $user->firstname . ' ' . $user->lastname;
        $res = [];
        foreach ($request->emailReceivers as $recipient) {
            $res[] = Email::newRawMail($recipient, "intern", $subject, Strng::cleanSpaces($request->mailtext, '<br>'));
        }

        return response()->json(['success' => true, 'sent' => $res]);
    }


    public function create(Request $request)
    {
        $user = $request->get('AuthUser');
        if (!$user->hasPermission('mt-admin')) return response()->json(['error' => 'No Admin Rights'], 403);
        // $hash = Str::random(20);
        $hash = Str::uuid();

        $epaper = Epaper::create([
            "hash" => $hash,
            "wasImported" => 0,
            "updated_by" => $user->id
        ]);


        set_time_limit(600);

        $validator = Validator::make($request->all(), [
            'description' => 'required|string|max:250',
        ]);

        $file = $request->file('customFile');

        if ($validator->fails() || !$epaper || !$file) return response()->json(['is_form_valid' => false], 400);


        $path = "manual/" . $epaper->hash;

        if (!Storage::exists("public/epaper/$path/pages")) Storage::makeDirectory("public/epaper/" . $path . "/pages");


        //Wenn Files vorhanden, alle Löschen
        $files = Storage::allFiles("public/epaper/" . $path) ?? [];
        foreach ($files as $file) {
            Storage::delete($file);
        }


        //Geladenes PDF in einzelne Seiten aufspalten
        //einzelne Seiten/Pages dann in JPEG's umwandeln
        $storedPDF = $request->customFile->store("public/epaper/" . $path . "/originalpdf");
        exec(env("PATH_TO_PDFTK") . " " . storage_path("app/" . $storedPDF) . " burst output " . storage_path("app/public/epaper/" . $path) . "/pages/singlepage_%03d.pdf");

        if (Storage::exists("public/epaper/$path/pages/doc_data.txt")) Storage::delete("public/epaper/$path/pages/doc_data.txt"); #falls doc_data.txt generiert wird

        $files = Storage::files("public/epaper/" . $path . "/pages") ?? [];


        foreach ($files as $index => $file) {
            $filename = explode("/", $file)[count(explode("/", $file)) - 1]; #pdf_singlepage_1.pdf
            $imgname = "public/epaper/" . $path . "/" . substr_replace($filename, ".jpg", -4); #pdf_singlepage_1.jpeg
            exec(env("PATH_TO_IMAGEMAGICK") . " convert -density 200 -quality 90 " . storage_path("app/$file") . " -background white -flatten " . storage_path("app/$imgname")); #works //density runterschrauben für geringere Bildgröße (KB/kilobyte)
            if ($index === 0) {
                $previewPath = "storage/app/public/epaper/" . $path . "/mediathek.jpg";
                Image::make("storage/app/" . $imgname)->resize(400, null, function ($constraint) {
                    $constraint->aspectRatio();
                })->save($previewPath);
            }
            unset($filename, $imgname); #allocate memory
        }


        Storage::deleteDirectory("public/epaper/$path/pages");


        $update_arr = [
            "path" => $path,
            "object" => null,
            "description" => $request->description,
            "owner" => $user->firstname . ' ' . $user->lastname,
            "publication_date" => now(),
            "online_at" => Carbon::now()->subYears(1),
            "updated_by" => $user->id
        ];


        if ($storedPDF) $update_arr["original_pdf"] = str_replace("/", "", strrchr($storedPDF, "/"));

        $epaper->update($update_arr);

        $epaper->bookmarks = [];


        return response()->json(['success' => true, 'ebook' => $epaper]);

    }


    public function delete(Request $request)
    {
        $user = $request->get('AuthUser');
        if (!$user->hasPermission('mt-admin')) return response()->json(['error' => 'No Admin Rights'], 403);

        $epaper = MediathekEpaper::where("id", $request->id)->first();
        if (!$epaper) return response()->json(['success' => false, 400]);


        $epaper->update(["updated_by" => $user->id]);

        if ($epaper->path) {

            // "unlink(/srv/www/www.mediaprint.at/storage/app/public/epaper/imported/20250521/krone/kt/kt_20250521_003.jpg): Read-only file system",
/*            try{
                Storage::deleteDirectory("public/epaper/" . $epaper->path);
            }catch (\Exception $e){
                Log::error("Cannot delete public/epaper/$epaper->path because: ".$e->getMessage());
            }*/


            $epaper->delete();

        } else {
            return response()->json(['success' => false, 'err' => 'no e-paper path was found'], 400);
        }
        return response()->json(['success' => true, 'path' => $epaper->path, 'del' => true]);
    }


    public function getStartingData(Request $request)
    {
        $user = $request->get('AuthUser');

        if (!$user instanceof User) return null;


        $mtUser = $this->getMediathekUser($user);

        $start = microtime(true); // Start time

        $favorites = $user->mediathekFavorites();

        $media = $this->retrieveMedia($request);

        $previews = [];

        if ($mtUser->permissions->admin || $mtUser->permissions->kurierPreview) {
            /*
             * Kurier
             */
            $previews['kurier'] = $this->epaperMediaBaseQuery()->whereDate("publication_date", '>', now())->where('object', 2)->get();
        }

        if ($mtUser->permissions->admin || $mtUser->permissions->kronePreview) {
            /*
             * Krone
             */
            $previews['krone'] = $this->epaperMediaBaseQuery()->whereDate("publication_date", '>', now())->where('object', 1)->get();
        }


        $data = [
            'favorites' => $favorites,
            'previews' => $previews,
            'media' => $media,
            'user' => $mtUser,
            'filters' => MediathekBookmark::select('id', 'description', 'parent')->get(),
            'oldest_selectable_date' => "19.04.2014",
            'keywords' => Epaper::select('description')->whereNotNull('description')->distinct()->pluck('description')->toArray(),
        ];


        $end = microtime(true); // End time
        $data['time'] = $end - $start; // Calculate difference

        return response()->json($data);
    }


    private function epaperMediaBaseQuery()
    {
        return Epaper::with(['bookmarks:id,description,parent'])
            ->select('id', 'description', 'path', 'hash', 'publication_date', 'name');
    }

    private function retrieveMedia(Request $request)
    {

        $user = $request->get('AuthUser');

        if (!$user instanceof User) return null;

        $mtUser = $this->getMediathekUser($user);


        $category = $request->query('category');
        $date = $request->query('date');
        $search = $request->query('search');


        $media = $this->epaperMediaBaseQuery()->whereDate("publication_date", "<=", now());


        if (!$mtUser->permissions->admin && !$mtUser->permissions->specialEditions) {
            $exclusions = $this->specialEditions;
            $media->where(function ($query) use ($exclusions) {
                foreach ($exclusions as $exclusion) {
                    $query->where('path', 'NOT LIKE', "%/$exclusion");
                }
            });
        }


        if ($date) {
            $media->whereDate("publication_date", Carbon::createFromFormat('d.m.Y', $date));
        }

        if ($search) {
            $media->where('description', 'LIKE', $search . '%');
        }

        if ($category) {
            $media->whereHas('bookmarks', function ($query) use ($category) {
                $query->where('id', $category);
            });
        }

        return $media->orderByDesc("publication_date")->limit(160)->get();


    }

    public function getMedia(Request $request)
    {
        return response()->json([
            'media' => $this->retrieveMedia($request),
        ]);
    }
}
