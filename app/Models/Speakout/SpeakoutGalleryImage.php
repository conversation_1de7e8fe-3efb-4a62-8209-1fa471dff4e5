<?php

namespace App\Models\Speakout;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Intervention\Image\ImageManager as Image;
/**
 * @property string $path
 * @method static whereNotNull(string $string)
 * @method static find(int|string $id)
 * @method static where(string $string, $documentName)
 */
class SpeakoutGalleryImage extends Model
{
    protected $dates = ['created_at', 'updated_at'];
    protected $guarded = ['id'];

    protected $appends = ['fullPath'];

    protected function getFullPathAttribute(){
        if(!$this->path) return '';
        return url('storage/app/public/' . $this->path);
    }



    private function getInterventionImage(): Image
    {
        return app(Image::class);
    }


    public function saveImage(UploadedFile $uploadedFile): void
    {



        $dirPath = 'speakout/' .
            ((env('APP_ENV') === 'production') ? '' : 'dev/')
            . 'gallery-images'  ;



        if (!Storage::disk('public')->exists($dirPath)) Storage::disk('public')->makeDirectory($dirPath);

        $originalFilename = pathinfo($uploadedFile->getClientOriginalName(), PATHINFO_FILENAME);
        $extension = $uploadedFile->getClientOriginalExtension();
        $filename = Str::slug($originalFilename) . '-' . now()->timestamp . '.' . $extension;

        $filePath = $dirPath  . '/' . $filename;
        /*
         * If exist delete previous logo, before saving the new path
         */
        $this->deleteFile();

   /*     $saved = Storage::disk('public')->put($filePath, $uploadedFile->getContent());*/



        try {
            // resize the image so that the largest side fits within the limit; the smaller
            // side will be scaled to maintain the original aspect ratio
            $ready_file = $this->getInterventionImage()->make($uploadedFile->getContent())->resize(1400, 1400, function ($constraint) {
                $constraint->aspectRatio();
                // $constraint->upsize();
            });
        } catch (\Exception $e) {
            dd('ERROR: -> ', $e);
        }

        $saved = Storage::disk('public')->put($filePath, $ready_file->encode($uploadedFile->extension(), 95));

        if($saved) {

            $this->path = $filePath;
            $this->save();
        }

    }

    public function delete()
    {
        $this->deleteFile();
        parent::delete();
    }
    private function deleteFile(){
        if ($this->path && Storage::disk('public')->exists($this->path)) {
            Storage::disk('public')->delete($this->path);
        }

    }

}
