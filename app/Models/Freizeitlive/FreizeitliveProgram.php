<?php

namespace App\Models\Freizeitlive;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Intervention\Image\ImageManager as Image;

/**
 * @property mixed $starts_at
 * @property mixed $ends_at
 * @property mixed $room_id
 * @property bool $hidden
 * @method static create(array $valid)
 * @method static orderBy(string $string, string $string1)
 */
class FreizeitliveProgram extends Model
{


    protected $table = 'freizeitlive_programs';

    protected $guarded = ['id'];

    protected $appends = [
        "timeRangeString",
        "roomName",
        "roomSub",
        "image1full",
        "image2full",
        "image3full",
        "imagesArray"
    ];

    protected $casts = [
        'eventdate' => 'date',
        'starts_at' => 'datetime',
        'ends_at' => 'datetime',
        "hidden" => "boolean"
    ];


    public function getImagesArrayAttribute()
    {

        $x = [$this->getImage1fullAttribute(), $this->getImage2fullAttribute(), $this->getImage3fullAttribute()];
        return array_filter($x);

    }
    public function getImage1fullAttribute()
    {

        if (!$this->image_1) return "";
        return url('storage/app/public/' . $this->image_1);
    }

    public function getImage2fullAttribute()
    {

        if (!$this->image_2) return "";
        return url('storage/app/public/' . $this->image_2);
    }

    public function getImage3fullAttribute()
    {

        if (!$this->image_3) return "";
        return url('storage/app/public/' . $this->image_3);
    }


    public function getTimeRangeStringAttribute(): string
    {
        return $this->starts_at->format('H:i') . ' - ' . $this->ends_at->format('H:i');
    }


    public static function getRooms(): array
    {
        return config('freizeitlive.program.rooms');
    }


    public static function GetEventdates(): array
    {
        $a = [];
        foreach (config('freizeitlive.program.eventdates') as $date) $a[$date] = Carbon::parse($date);
        return $a;
    }


    private function getRoom()
    {
        return collect(config('freizeitlive.program.rooms'))->first(function ($room) {
            return $room['id'] === $this->room_id;
        });
    }


    public function getRoomNameAttribute()
    {
        $x = $this->getRoom();
        return ($x) ? $x['name'] : '';
    }


    public function getRoomSubAttribute()
    {
        $x = $this->getRoom();
        return ($x) ? $x['sub'] : '';
    }


    public function speakers(): BelongsToMany
    {
        return $this->belongsToMany(FreizeitliveSpeaker::class, 'freizeitlive_program_speaker', 'program_id', 'speaker_id');
    }


    public function sponsors(): BelongsToMany
    {
        return $this->belongsToMany(FreizeitlivePartner::class, 'freizeitlive_program_partner', 'program_id', 'partner_id');
    }


    public static function groupProgramsByRoom(): array
    {

        $all = FreizeitliveProgram::orderByDayAndTime()->get();

        $data = [];

        foreach (FreizeitliveProgram::getRooms() as $room) {

            $room["programs"] = $all->filter(function ($program) use ($room) {
                return $program->room_id === $room['id'];
            })->values()->all();

            $data[] = $room;
        }

        return $data;
    }


    public function getSharingLinks()
    {

        $articleUrl = url("programm/$this->id");


        return [
            [
                "text" => "Auf Facebook teilen",
                "icon" => 'fa-brands fa-facebook',
                "link" => "https://www.facebook.com/sharer/sharer.php?u=$articleUrl",
                "color" => "#3b5998"  // Facebook brand color
            ],
            [
                "text" => "Auf LinkedIn teilen",
                "icon" => 'fa-brands fa-linkedin',
                "link" => "https://www.linkedin.com/sharing/share-offsite/?url=$articleUrl",
                "color" => "#0077b5"  // LinkedIn brand color
            ],
            [
                "text" => "Auf WhatsApp teilen",
                "icon" => 'fa-brands fa-whatsapp',
                "link" => "https://api.whatsapp.com/send?text=$articleUrl",
                "color" => "#25D366"  // WhatsApp brand color
            ],
            /*   [
                   "text" => "Auf Instagram teilen",
                   "icon" => 'fa-brands fa-instagram',
                   "link" => "#",  // Instagram does not support direct sharing via URLs
                   "color" => "#E1306C"  // Instagram brand color
               ],*/
            [
                "text" => "Auf Twitter teilen",
                "icon" => 'fa-brands fa-x-twitter',
                "link" => "https://twitter.com/intent/tweet?url=$articleUrl",
                "color" => "#00000"  // Twitter brand color
            ]
        ];
    }


    public function saveImage(UploadedFile $uploadedFile, string $column): void
    {


        $dirPath = 'freizeitlive/program-img';

        if (env('APP_ENV') != 'production') $dirPath .= '-dev';

        if (!Storage::disk('public')->exists($dirPath)) Storage::disk('public')->makeDirectory($dirPath);

        $extension = $uploadedFile->getClientOriginalExtension();
        $filename = Str::uuid() . ".$extension";

        $filePath = $dirPath . '/' . $filename;
        /*
         * If exist delete previous logo, before saving the new path
         */
        $this->deleteFile($column);

        $ready_file = app(Image::class)->make($uploadedFile->getContent())->resize(1000, 1000, function ($constraint) {
            $constraint->aspectRatio();
        });


        $saved = Storage::disk('public')->put($filePath, $ready_file->encode($extension, 95));

        if ($saved) {
            $this->update([$column => $filePath]);
        }
    }

    public function deleteFile($column)
    {
        if ($this->{$column} && Storage::disk('public')->exists($this->{$column})) {
            Storage::disk('public')->delete($this->{$column});
        }
    }

    public function delete()
    {
        $this->deleteFile('image_1');
        $this->deleteFile('image_2');
        return parent::delete();
    }

    //SCOPES
    public function scopeOrderByDayAndTime($query)
    {

        return $query->orderBy("eventdate", "ASC")
            ->orderBy('starts_at', 'ASC')
            ->orderBy('ends_at', 'ASC');
    }

    /**
     * @param string[] $appends
     * @return FreizeitliveProgram
     */
    public function setAppends(array $appends): FreizeitliveProgram
    {
        $this->appends = $appends;
        return $this;
    }

}

