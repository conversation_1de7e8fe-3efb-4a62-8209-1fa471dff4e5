<?php

namespace App\Models\Freizeitlive;

use Exception;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Intervention\Image\ImageManager as Image;


/**
 * @property string $image_one
 * @property string $image_two
 * @property string $seo_url
 * @method static whereNotNull(string $string)
 * @method static where(string $string, string $string1)
 * @method static findOrFail(mixed $id)
 */
class FreizeitlivePost extends Model
{
    protected $table = 'freizeitlive_posts';
    protected $dates = ['created_at', 'updated_at'];
    protected $guarded = ['id'];

    protected $appends = ['imageOneFullPath', 'imageTwoFullPath', 'fullPath', 'titleText'];

    private array $imageSlots = [

        1 => 'image_one',
        2 => 'image_two'
    ];

    public function getTitleTextAttribute()
    {

        return html_entity_decode(strip_tags($this->title ?? ''));
    }

    protected function getImageOneFullPathAttribute()
    {
        if (!$this->image_one) return '';
        return url('storage/app/public/' . $this->image_one);
    }

    protected function getImageTwoFullPathAttribute()
    {
        if (!$this->image_two) return '';
        return url('storage/app/public/' . $this->image_two);
    }

    protected function getFullPathAttribute(){
        if(!$this->seo_url) return '';
        $base = "https://freizeitlive.kurier.at";
        if(env('APP_ENV') == 'local') $base = 'http://local-freizeitlive:81';
        return $base . "/post/".$this->id."/".$this->seo_url;
    }

    /**
     * @throws Exception
     */
    public function saveImage(UploadedFile $uploadedFile, int $slot): void
    {


        if (!array_key_exists($slot, $this->imageSlots)) {
            throw new Exception('Image slot doesnt exist!!!');
        }

        $imageSlot = $this->imageSlots[$slot];

        $dirPath = 'freizeitlive/posts';

        if (env('APP_ENV') != 'production') $dirPath .= '-dev';

        if (!Storage::disk('public')->exists($dirPath)) Storage::disk('public')->makeDirectory($dirPath);

        $extension = $uploadedFile->getClientOriginalExtension();
        $filename = Str::uuid() . 'Models.' . $extension;

        $filePath = $dirPath . '/' . $filename;
        /*
         * If exist delete previous logo, before saving the new path
         */
        $this->deleteFiles($imageSlot);

        $ready_file = app(Image::class)->make($uploadedFile->getContent())->resize(1000, 1000, function ($constraint) {
            $constraint->aspectRatio();
        });

        $saved = Storage::disk('public')->put($filePath, $ready_file->encode($extension, 95));

        if ($saved) {
            $this->update([$imageSlot => $filePath]);
        }

    }

    public function delete()
    {
        $this->deleteFiles($this->imageSlots);
        parent::delete();
    }


    private function deleteFiles($imageSlots)
    {
        if (!is_array($imageSlots)) $imageSlots = [$imageSlots];

        foreach ($imageSlots as $imageSlot) {
            if ($this->{$imageSlot} && Storage::disk('public')->exists($this->{$imageSlot})) {
                Storage::disk('public')->delete($this->{$imageSlot});
            }
        }

    }


    public function postType()
    {

        return $this->belongsTo(FreizeitlivePostType::class, 'post_type', 'id');
    }


    public function links(): MorphMany
    {
        return $this->morphMany(FreizeitliveLink::class, 'morphable');
    }

    public function uniqueLinksColor(): String
    {
        /*
         * Return color if all links have the same color, otherwise empty string
         */
        $links = $this->links()->get();

        $color = $links->pluck('color')->unique();

        if ($color->count() === 1) {
            return $color->first() ?? '';
        }

        return '';
    }

}
