<?php

    namespace App\Models\Freizeitlive;

    use Illuminate\Database\Eloquent\Model;
    use Illuminate\Http\UploadedFile;
    use Illuminate\Support\Arr;
    use Illuminate\Support\Facades\Storage;
    use Illuminate\Support\Str;
    use Intervention\Image\ImageManager as Image;


    /**
     * @method static find(int|string $key)
     * @method static where(string $string, string $string1)
     * @method static updateOrCreate(array $match, int[] $array)
     * @method static whereNotIn(string $string, int[] $array)
     * @method static whereIn(string $string, int[] $array)
     * @method static create(array $array)
     * @property mixed $token
     * @property mixed $image
     * @property bool $active
     * @property string $imagePath
     * @property mixed $name
     */
    class FreizeitliveNews extends Model{


        protected $table = 'freizeitlive_news';

        protected $guarded = ['id'];

        protected $appends = ['imageFullPath', 'titleText'];


        public function getTitleTextAttribute()
        {
            return  html_entity_decode(strip_tags($this->title));

        }
        public function getImageFullPathAttribute(){
            return $this->imagePath ? url('storage/app/public/'.$this->imagePath) : '';
        }


        private function getInterventionImage(): Image{
            return app(Image::class);
        }


        public function delete(){
            if($this->imagePath && Storage::disk('public')->exists($this->imagePath)){
                Storage::disk('public')->delete($this->imagePath);
            }

            return parent::delete();
        }


        public function update(array $attributes = [], array $options = []): bool{

            $parentUpdate = parent::update($attributes, $options);


            if(! Arr::has($attributes, 'name_slug') && Arr::has($attributes, 'name')){

                $cleaned = html_entity_decode(strip_tags($this->name));


                $this->update(['name_slug' => Str::slug($cleaned)]);
            }


            return $parentUpdate;
        }


        public function saveImage(UploadedFile $file): void{

            try{
                // resize the image so that the largest side fits within the limit; the smaller
                // side will be scaled to maintain the original aspect ratio
                $ready_file = $this->getInterventionImage()->make($file)->resize(1200, 1200, function($constraint){
                    $constraint->aspectRatio();
                    // $constraint->upsize();
                });
            }catch(\Exception $e){
                dd('ERROR: -> ', $e);
            }

            if($this->imagePath && Storage::disk('public')->exists($this->imagePath)){
                Storage::disk('public')->delete($this->imagePath);
            }

            $path = 'freizeitlive/'.((env('APP_ENV') === 'production') ? 'news/' : 'news-dev/').Str::uuid().'.jpg';

            if(Storage::disk('public')->put($path, $ready_file->encode('jpg', 95))){
                $this->imagePath = $path;
                $this->save();
            }
        }
    }
