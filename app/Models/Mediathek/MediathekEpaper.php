<?php

namespace App\Models\Mediathek;


use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @method static UpdateOrCreate(string[] $array, array $array1)
 * @method static select(string $string, string $string1, string $string2, string $string3, string $string4, string $string5)
 * @method static where(string $string, int $int)
 * @method static create(array $ePaperData)
 * @property mixed $path
 * @property mixed $hash
 */
class MediathekEpaper extends Model
{


    use SoftDeletes;

    protected $dates = ['online_at', 'offline_at', 'created_at', 'updated_at', "deleted_at", 'publication_date'];
    protected $guarded = ['id'];
    protected $appends = ['coverFullPath'];

    public function getCoverFullPathAttribute(): string
    {
        if (!$this->path) return '';
        return "https://www.mediaprint.at/storage/app/public/epaper/$this->path/mediathek.jpg";
    }

    public function getPublicationDateAttribute($value)
    {
        /**
         * @Important
         */
        return Carbon::parse($value)->setTimezone('Europe/Vienna')->format('d.m.Y');
    }




    /*https://www.mediaprint.at/katalog/*/
    public function shortlabel()
    {
        /*
         *  used by syncFilter($param = "week")
         */
        return $this->belongsTo(MediathekShortlabel::class, "ausgabe");
    }


    public function bookmarks(): BelongsToMany
    {
        /*
         * Filters for Mediathek
         */
        return $this->belongsToMany(MediathekBookmark::class, 'mediathek_bookmark_epaper', 'mediathek_epaper_id', 'mediathek_bookmark_id');
    }


}



