[{"name": "dargiola/vimeo-sdk", "version": "dev-master", "source": {"type": "git", "url": "https://gitlab.mediaprint.co.at/webgruppe/vimeo-sdk.git", "reference": "afd0d041fa7b26c156b55eebf26458d5998321e2"}, "require": {"php": "^7.4|^8.0"}, "default-branch": true, "type": "library", "autoload": {"psr-4": {"MpVimeoSDK\\": "src/"}}, "scripts": {"post-install-cmd": ["@php artisan vendor:publish --tag=vimeo-sdk --force"], "post-update-cmd": ["@php artisan vendor:publish --tag=vimeo-sdk --force"]}, "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A Vimeo SDK", "time": "2023-07-24T06:38:27+00:00"}, {"name": "mp-itweb/mp-oauth-2", "version": "dev-master", "source": {"type": "git", "url": "https://gitlab.mediaprint.co.at/webgruppe/mp-oauth-2.git", "reference": "d020c15d53c27fbf7138dfc6d0edbfd5bfb7cb97"}, "dist": {"type": "zip", "url": "https://gitlab.mediaprint.co.at/api/v4/projects/webgruppe%2Fmp-oauth-2/repository/archive.zip?sha=d020c15d53c27fbf7138dfc6d0edbfd5bfb7cb97", "reference": "d020c15d53c27fbf7138dfc6d0edbfd5bfb7cb97", "shasum": ""}, "default-branch": true, "type": "library", "autoload": {"psr-4": {"MpItWeb\\MPOAuth\\": "src/"}}, "scripts": {"post-install-cmd": ["@php artisan vendor:publish --tag=oauth-config", "echo 'The post-install script is running'"], "post-update-cmd": ["@php artisan vendor:publish --tag=oauth-config", "echo 'The post-update script is running'"]}, "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "support": {"source": "https://gitlab.mediaprint.co.at/webgruppe/mp-oauth-2/-/tree/master", "issues": "https://gitlab.mediaprint.co.at/webgruppe/mp-oauth-2/-/issues"}, "time": "2023-12-07T08:19:43+00:00"}]