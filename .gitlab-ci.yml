# For more information, see: https://docs.gitlab.com/ee/ci/yaml/index.html#stages

stages:          # List of stages for jobs, and their order of execution
  - build
  - deploy_dev
  - deploy_prod

variables:
  TGT_USR: mediaprint-git

image: registry.mediaprint.co.at/sbd/container/rsync:latest

.build:          # This job runs in the build stage, which runs first.
  tags: [it-web-docker]
  stage: build
  script:
    - openssl s_client -showcerts -verify 5 -connect gitlab.mediaprint.co.at:443 < /dev/null 2> /dev/null | sed -n '/-----BEGIN/,/-----END/p' | sed -n '/-----BEGIN/h;/-----BEGIN/!H;$!b;x;p' > /usr/local/share/ca-certificates/MPAD-Root.crt
    - update-ca-certificates
    - EXPECTED_CHECKSUM="$(php -r 'copy("https://composer.github.io/installer.sig", "php://stdout");')"
    - php -r "copy('https://getcomposer.org/installer', 'composer-setup.php');"
    - php -r "if (hash_file('sha384', 'composer-setup.php') === '$EXPECTED_CHECKSUM') { echo 'Installer verified'; } else { echo 'Installer corrupt'; unlink('composer-setup.php'); } echo PHP_EOL;"
    - mkdir -p /usr/local/bin/composer
    - php composer-setup.php --install-dir /usr/local/bin/composer
    - php -r "unlink('composer-setup.php');"
    - php /usr/local/bin/composer/composer.phar self-update $COMPOSER_VERSION
    - curl -sSLf -o /usr/local/bin/install-php-extensions https://github.com/mlocati/docker-php-extension-installer/releases/latest/download/install-php-extensions && chmod +x /usr/local/bin/install-php-extensions && install-php-extensions ldap gd zip
    - apt-get update && apt-get install unzip git --assume-yes
    - php /usr/local/bin/composer/composer.phar config http-basic.gitlab.mediaprint.co.at gitlab-ci-token $CI_JOB_TOKEN
    - mkdir -p storage/
    - mkdir -p storage/framework/
    - mkdir -p storage/framework/sessions
    - mkdir -p storage/framework/views
    - mkdir -p storage/framework/views_development
    - mkdir -p storage/framework/views_local
    - mkdir -p storage/framework/views_production
    - mkdir -p storage/framework/cache
    - mkdir -p storage/framework/testing
    - mkdir -p storage/logs/
    - mkdir -p bootstrap/cache
    - php /usr/local/bin/composer/composer.phar update
    - php /usr/local/bin/composer/composer.phar dump-autoload
    - php /usr/local/bin/composer/composer.phar show
    - rm -rf .idea/
    - rm -rf .git/
    - rm -f .git*
    - rm -rf storage/
    - rm -rf public/web.config
    - find . -path "*/lost+found" -prune -o -name ".git" -type d -prune -exec rm -rf {} +
  artifacts:
    expire_in: 1 day
    paths:
      - "*"
  cache:
    key: ${CI_COMMIT_REF_SLUG}-composer
  rules:
    - if: $CI_PIPELINE_SOURCE == "push"

build-composer-php7:
  extends: .build
  image: php:7.4.33-cli
  variables:
    COMPOSER_VERSION: "2.7.2"

# build-composer-php8:
#   extends: .build
#   image: php:8.0.30-cli
#   variables:
#     COMPOSER_VERSION: "2.7.2"

.deploy:          # This job runs in the deploy stage.
  tags: [it-web-docker]
  when: manual
  script:
    - mkdir -p /root/.ssh
    - echo "$SSH_KNOWN_HOSTS" >> /root/.ssh/known_hosts
    - cat /root/.ssh/known_hosts
    - echo "$SSHKEY_DEPLOY" > /root/.ssh/id_gitlab ; chmod 400 /root/.ssh/id_gitlab
    - echo "rsync -rl --delete --exclude lost+found --exclude='storage' --exclude='storage/**' --exclude='rsync_scripts' --exclude public/web-chat-app --exclude public/mediathek --exclude='public/.well-known' --exclude='public/transport' --exclude='.git' --exclude='data.ms' -e 'ssh -i /root/.ssh/id_gitlab' ${SRC_DIR} ${TGT_USR}@${TGT_SRV}:${TGT_DIR}"
    - rsync -rl --delete --exclude lost+found --exclude='storage' --exclude='storage/**' --exclude='rsync_scripts' --exclude public/web-chat-app --exclude public/mediathek --exclude='public/.well-known' --exclude='public/transport' --exclude='.git' --exclude='data.ms' -e 'ssh -i /root/.ssh/id_gitlab' ${SRC_DIR} ${TGT_USR}@${TGT_SRV}:${TGT_DIR}
  variables:
    SRC_DIR: "./"
    TGT_DIR: "/srv/www/www.mediaprint.at/"

deploy_dev:
  extends: .deploy
  stage: deploy_dev
  needs: [build-composer-php7]
  parallel:
    matrix:
      - TGT_SRV:
        - lxintel423.mediaprint.co.at
  variables:
    TGT_USR: deploy-git
    SSHKEY_DEPLOY: "${SSHKEY_DEV}"
  environment:
    name: test
    #url: https://mediaprint.dev.mediaprint.co.at

# deploy_dev8:
#   extends: .deploy
#   stage: deploy_dev
#   needs: [build-composer-php8]
#   parallel:
#     matrix:
#       - TGT_SRV:
#         - itwebd-ap01.mediaprint.co.at
#   variables:
#     TGT_USR: deploy-git
#     SSHKEY_DEPLOY: "${SSHKEY_DEV}"
#   environment:
#     name: test
#     #url: https://mediaprint.dev.mediaprint.co.at

deploy-prod:
  extends: .deploy
  stage: deploy_prod
  needs: [build-composer-php7]
  parallel:
    matrix:
      - TGT_SRV:
        - lxintel425.mediaprint.co.at
  variables:
    SSHKEY_DEPLOY: "${SSHKEY_PROD_DMZ}"
  rules:
    - if: '$CI_COMMIT_BRANCH == "master" || $CI_COMMIT_BRANCH == "main"'
      when: manual
      allow_failure: false
  environment:
    name: prod
    #url: https://www.mediaprint.at
