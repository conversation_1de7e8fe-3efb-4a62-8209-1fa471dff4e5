#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 1048576 bytes. Error detail: AllocateHeap
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (allocation.cpp:44), pid=17544, tid=15896
#
# JRE version:  (21.0.6+9) (build )
# Java VM: OpenJDK 64-Bit Server VM (21.0.6+9-b895.109, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: git4idea.http.GitAskPassApp Username for 'https://gitlab.mediaprint.co.at': 

Host: Intel(R) Core(TM) Ultra 7 165U, 14 cores, 31G,  Windows 11 , 64 bit Build 22621 (10.0.22621.4974)
Time: Wed Apr 30 09:44:24 2025 Mitteleurop�ische Sommerzeit elapsed time: 0.027358 seconds (0d 0h 0m 0s)

---------------  T H R E A D  ---------------

Current thread (0x000001e64e3f16a0):  JavaThread "Unknown thread" [_thread_in_vm, id=15896, stack(0x000000e4a4300000,0x000000e4a4400000) (1024K)]

Stack: [0x000000e4a4300000,0x000000e4a4400000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6e5cb9]
V  [jvm.dll+0x8c4113]
V  [jvm.dll+0x8c666e]
V  [jvm.dll+0x8c6d53]
V  [jvm.dll+0x288f76]
V  [jvm.dll+0xc0e57]
V  [jvm.dll+0x333ac5]
V  [jvm.dll+0x88b52d]
V  [jvm.dll+0x3ca6c8]
V  [jvm.dll+0x8745b8]
V  [jvm.dll+0x45f0de]
V  [jvm.dll+0x460dc1]
C  [jli.dll+0x52ab]
C  [ucrtbase.dll+0x29333]
C  [KERNEL32.DLL+0x1259d]
C  [ntdll.dll+0x5af38]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00007ffaca6ba148, length=0, elements={
}

Java Threads: ( => current thread )
Total: 0

Other Threads:

=>0x000001e64e3f16a0 (exited) JavaThread "Unknown thread"    [_thread_in_vm, id=15896, stack(0x000000e4a4300000,0x000000e4a4400000) (1024K)]
Total: 1

Threads with active compile tasks:
Total: 0

VM state: not at safepoint (not fully initialized)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x0000000000000000, size: 0 MB, Compressed Oops mode: 32-bit

CDS archive(s) mapped at: [0x0000000000000000-0x0000000000000000-0x0000000000000000), size 0, SharedBaseAddress: 0x0000000800000000, ArchiveRelocationMode: 1.
Narrow klass base: 0x0000000000000000, Narrow klass shift: 0, Narrow klass range: 0x0

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 32 size 80 Howl #buckets 8 coarsen threshold 7372 Howl Bitmap #cards 1024 size 144 coarsen threshold 921 Card regions per heap region 1 cards per card region 8192

GC Heap History (0 events):
No events

Dll operation events (1 events):
Event: 0.020 Loaded shared library C:\Users\<USER>\AppData\Local\JetBrains\PhpStorm 2023.2.3\jbr\bin\java.dll

Deoptimization events (0 events):
No events

Classes loaded (0 events):
No events

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

ZGC Phase Switch (0 events):
No events

VM Operations (0 events):
No events

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (0 events):
No events


Dynamic libraries:
0x00007ff6682b0000 - 0x00007ff6682ba000 	C:\Users\<USER>\AppData\Local\JetBrains\PhpStorm 2023.2.3\jbr\bin\java.exe
0x00007ffb54870000 - 0x00007ffb54a87000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffb52f10000 - 0x00007ffb52fd4000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffb519b0000 - 0x00007ffb51d81000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffb522e0000 - 0x00007ffb523f1000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffb3a090000 - 0x00007ffb3a0a8000 	C:\Users\<USER>\AppData\Local\JetBrains\PhpStorm 2023.2.3\jbr\bin\jli.dll
0x00007ffb3a0c0000 - 0x00007ffb3a0db000 	C:\Users\<USER>\AppData\Local\JetBrains\PhpStorm 2023.2.3\jbr\bin\VCRUNTIME140.dll
0x00007ffb528d0000 - 0x00007ffb52a81000 	C:\WINDOWS\System32\USER32.dll
0x00007ffb52230000 - 0x00007ffb52256000 	C:\WINDOWS\System32\win32u.dll
0x00007ffb541e0000 - 0x00007ffb54209000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffb51f00000 - 0x00007ffb5201b000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffb52020000 - 0x00007ffb520ba000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffb36bd0000 - 0x00007ffb36e62000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.4830_none_270fe7d773858e80\COMCTL32.dll
0x00007ffb526a0000 - 0x00007ffb52747000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffb54620000 - 0x00007ffb54651000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffb4a8f0000 - 0x00007ffb4a8fc000 	C:\Users\<USER>\AppData\Local\JetBrains\PhpStorm 2023.2.3\jbr\bin\vcruntime140_1.dll
0x00007ffaf71f0000 - 0x00007ffaf727d000 	C:\Users\<USER>\AppData\Local\JetBrains\PhpStorm 2023.2.3\jbr\bin\msvcp140.dll
0x00007ffac9a60000 - 0x00007ffaca821000 	C:\Users\<USER>\AppData\Local\JetBrains\PhpStorm 2023.2.3\jbr\bin\server\jvm.dll
0x00007ffb53210000 - 0x00007ffb532c1000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffb52c90000 - 0x00007ffb52d37000 	C:\WINDOWS\System32\sechost.dll
0x00007ffb520c0000 - 0x00007ffb520e8000 	C:\WINDOWS\System32\bcrypt.dll
0x00007ffb54030000 - 0x00007ffb54144000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffb545a0000 - 0x00007ffb54611000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffb50740000 - 0x00007ffb5078d000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffb3f5f0000 - 0x00007ffb3f624000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffb3a110000 - 0x00007ffb3a11a000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffb50720000 - 0x00007ffb50733000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffb50970000 - 0x00007ffb50988000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffb3eb40000 - 0x00007ffb3eb4a000 	C:\Users\<USER>\AppData\Local\JetBrains\PhpStorm 2023.2.3\jbr\bin\jimage.dll
0x00007ffb3e1c0000 - 0x00007ffb3e3f2000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffb54210000 - 0x00007ffb545a0000 	C:\WINDOWS\System32\combase.dll
0x00007ffb527e0000 - 0x00007ffb528b7000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffb35010000 - 0x00007ffb35042000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffb52260000 - 0x00007ffb522db000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffb39f80000 - 0x00007ffb39fa0000 	C:\Users\<USER>\AppData\Local\JetBrains\PhpStorm 2023.2.3\jbr\bin\java.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Users\<USER>\AppData\Local\JetBrains\PhpStorm 2023.2.3\jbr\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.4830_none_270fe7d773858e80;C:\Users\<USER>\AppData\Local\JetBrains\PhpStorm 2023.2.3\jbr\bin\server

VM Arguments:
java_command: git4idea.http.GitAskPassApp Username for 'https://gitlab.mediaprint.co.at': 
java_class_path (initial): C:/Users/<USER>/AppData/Local/JetBrains/PhpStorm 2023.2.3/plugins/vcs-git/lib/git4idea-rt.jar;C:/Users/<USER>/AppData/Local/JetBrains/PhpStorm 2023.2.3/lib/externalProcess-rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 11                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 4194304                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 528482304                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 8447328256                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 4194304                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 8447328256                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
PATH=C:/Users/<USER>/AppData/Local/Programs/Git/mingw64/libexec/git-core;C:/Users/<USER>/AppData/Local/Programs/Git/mingw64/libexec/git-core;C:\Users\<USER>\AppData\Local\Programs\Git\mingw64\bin;C:\Users\<USER>\AppData\Local\Programs\Git\usr\bin;C:\Users\<USER>\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\dotnet\;C:\Users\<USER>\AppData\Local\Programs\Git\bin;C:\xampp74\php;C:\ProgramData\ComposerSetup\bin;C:\Program Files\nodejs\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\Git\cmd
USERNAME=SSCHICK
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
TERM=xterm-256color
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 170 Stepping 4, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 0, weak refs: 0

JNI global refs memory usage: 0, weak refs: 0

Process memory usage:
Resident Set Size: 11032K (0% of 32990300K total physical memory with 9373612K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.4974)
OS uptime: 8 days 1:28 hours
Hyper-V role detected

CPU: total 14 (initial active 14) (7 cores per cpu, 2 threads per core) family 6 model 170 stepping 4 microcode 0x23, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, clwb, hv, serialize, rdtscp, rdpid, fsrm, f16c, cet_ibt, cet_ss
Processor Information for processor 0
  Max Mhz: 1700, Current Mhz: 1700, Mhz Limit: 1700
Processor Information for processor 1
  Max Mhz: 1700, Current Mhz: 1700, Mhz Limit: 1700
Processor Information for processor 2
  Max Mhz: 1200, Current Mhz: 1200, Mhz Limit: 1200
Processor Information for processor 3
  Max Mhz: 1200, Current Mhz: 1200, Mhz Limit: 1200
Processor Information for processor 4
  Max Mhz: 1200, Current Mhz: 1200, Mhz Limit: 1200
Processor Information for processor 5
  Max Mhz: 1200, Current Mhz: 1200, Mhz Limit: 1200
Processor Information for processor 6
  Max Mhz: 1200, Current Mhz: 1200, Mhz Limit: 1200
Processor Information for processor 7
  Max Mhz: 1200, Current Mhz: 1200, Mhz Limit: 1200
Processor Information for processor 8
  Max Mhz: 1200, Current Mhz: 1200, Mhz Limit: 1200
Processor Information for processor 9
  Max Mhz: 1200, Current Mhz: 1200, Mhz Limit: 1200
Processor Information for processor 10
  Max Mhz: 1700, Current Mhz: 1700, Mhz Limit: 1700
Processor Information for processor 11
  Max Mhz: 1700, Current Mhz: 1700, Mhz Limit: 1700
Processor Information for processor 12
  Max Mhz: 700, Current Mhz: 700, Mhz Limit: 700
Processor Information for processor 13
  Max Mhz: 700, Current Mhz: 700, Mhz Limit: 700

Memory: 4k page, system-wide physical 32217M (9153M free)
TotalPageFile size 130521M (AvailPageFile size 5M)
current process WorkingSet (physical memory assigned to process): 10M, peak: 10M
current process commit charge ("private bytes"): 12M, peak: 13M

vm_info: OpenJDK 64-Bit Server VM (21.0.6+9-b895.109) for windows-amd64 JRE (21.0.6+9-b895.109), built on 2025-03-26 by "builduser" with MS VC++ 16.10 / 16.11 (VS2019)

END.
