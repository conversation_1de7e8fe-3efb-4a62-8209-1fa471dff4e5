@extends('backend.master')

@section('content')
    <div class="row mb-5">

        <div class="col-12 mb-3">
            <div>
                <h2>Speakout Verwaltung - Documents</h2>
            </div>
        </div>

        @include('backend.speakout.components.navigation', ['newButton' => false])


        <form action="{{ url()->current() }}" id="" name="" method="post" enctype="multipart/form-data">
            {{ csrf_field() }}
            @method('POST')


            <div class="border p-3 mb-3">
                @foreach($docs as $doc)
                    <div class="mb-3">
                        <label for="label-{{$doc->id}}" class="form-label fw-bold">
                            {{$doc->label}}
                            @if($doc->fullPath)
                                ( <a href="{{$doc->fullPath}}" class="link-success fw-bold" target="_blank">Aktuelle
                                    Datei an<PERSON>hen</a> )
                            @endif
                        </label>
                        <input class="form-control" type="file" id="label-{{$doc->id}}" name="document[{{$doc->id}}]">
                    </div>
                @endforeach
            </div>


            <button type="submit" class="btn btn-brown">SPEICHERN</button>
        </form>

@endsection
