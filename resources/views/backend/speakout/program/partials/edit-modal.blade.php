@php
    use App\Models\Speakout\SpeakoutProgram;
    if(empty($program)){
        $program = new SpeakoutProgram();
    }
    $speakerIds = ($program->id) ? $program->speakers->map(function($x){return $x->id;})->join(';') :"";
    $sponsorIds = ($program->id) ? $program->sponsors->map(function($x){return $x->id;})->join(';') :"";
@endphp


<div>

    <form class="modal-dialog modal-dialog-centered  speaker--form "
          action="{{ url()->current() . (($program->id) ? "/$program->id" : "") }}" method="post"
          enctype="multipart/form-data">
        {{ csrf_field() }}
        @method('POST')
        <div class="modal-content">

            <div class="modal-body">


                <div class="mb-3">
                    <label for="" class="w-100">
                        <PERSON><PERSON>
                        <select class="form-select" name="room_id">
                            @foreach(SpeakoutProgram::getRooms() as $room)
                                <option @if($room['id'] == $program->room_id) selected
                                        @endif  value="{{$room['id']}}">{{$room['name']}}</option>
                            @endforeach
                        </select>
                    </label>

                </div>
                <div class="row mb-3">
                    <div class="col-sm-6">
                        <label for="input-{{$program->id}}-start" class="form-label">Startzeit</label>
                        <input type="time" class="form-control" id="input-{{$program->id}}-start" name="starts_at"
                               value="{{$program->starts_at ? $program->starts_at->format('H:i') : null}}">
                    </div>


                    <div class="col-sm-6">
                        <label for="input-{{$program->id}}-end" class="form-label">Endzeit </label>
                        <input type="time" class="form-control" id="input-{{$program->id}}-end" name="ends_at"
                               value="{{$program->ends_at ? $program->ends_at->format('H:i') : null}}">
                    </div>

                </div>


                <div class="mb-3">
                    <label for="input-{{$program->id}}-title" class="form-label">Titel</label>
                    <input name="title" type="text" class="form-control" id="input-{{$program->id}}-title"
                           value="{{$program->title}}">
                </div>


                <div class="mb-3">
                    <label for="input-{{$program->id}}-description" class="form-label">Beschreibung</label>
                    <textarea name="description"
                              class="form-control"
                              id="input-{{$program->id}}-description"
                              rows="3">{{$program->description}}</textarea>
                </div>

                <div class="mb-3">
                    <label for="input-{{$program->id}}-text" class="form-label">Text</label>


                    <textarea
                            ckeditor="true"
                            ckeditor_height="300"
                            ckeditor_removeButtons="{{env('CKEDITOR_ELEMENTS_MIDI')}}"
                            id="input-{{$program->id}}-text" class="form-control mb-3 " name="text">{{$program->text}}
							</textarea>
                </div>


                {{--Js--}}
                {{--                <input name="speakers" class="added-speakers-input" type="hidden" value="{{$speakerIds}}" >--}}
                @include('backend.speakout.program.partials.rel')

                {{--Js--}}
                {{--                <input name="partners" class="added-partners-input" type="hidden" value="{{$partnerIds}}" >--}}


            </div>
            <div class="modal-footer pt-3">
                <button type="submit" class="btn btn-primary" data-bs-ripple-init>Speichern</button>
            </div>
        </div>
    </form>
</div>

