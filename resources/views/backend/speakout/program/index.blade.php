@php
    use App\Models\Speakout\SpeakoutPartner;use App\Models\Speakout\SpeakoutProgram;use App\Models\Speakout\SpeakoutSpeaker;
    $allSpeakers =  SpeakoutSpeaker::where("hidden", 0)->orderBy("name", "asc")->get();
        $allSponsors = SpeakoutPartner::getPartnerAndSponsors();
@endphp


@extends('backend.master')

@section('container-fluid')

    <div class="row mb-5">
        <div class="col-12 mb-3">
            <h2>Speakout Verwaltung - Programm</h2>
        </div>

        <div class="col-12">

            <div class="row">
                @foreach($rooms as $room)

                    <div class="col-lg-6  ">
                        <div class="border border-3 border-primary p-4 mb-5  " style="background-color: #d4e9ff">
                            <div class="border-bottom border-primary pb-3 mb-3">
                                <h3>{{$room['name']}}</h3>
                                <i>{{$room['sub'] ? : "..."}}</i>
                            </div>


                            @foreach($room['programs'] as $program)

                                @php
                                    $editAccordionId = "edit-accordion-$program->id";

                                    $deleteModalId = "delete-modal-$program->id";
                                @endphp


                                @include('backend.speakout.program.partials.delete-modal', ["program" => $program, "modalId" => $deleteModalId])

                                @if($program instanceof SpeakoutProgram)
                                    <div class="border border-info   mb-3 bg-white">
                                        <div class="d-flex">
                                        <div class="border-end p-2 fs-5 d-flex align-items-center "
                                             style="white-space: nowrap; width: 20%">
                                            <div class="w-100 text-center">
                                                <i class="fa-regular fa-clock fa-xl"></i>
                                                <br>
                                                {{$program->timeRangeString}}
                                            </div>

                                        </div>
                                        <div class="p-3 w-100">
                                            <h3 class="text-primary">
                                                {{$program->title}}
                                            </h3>
                                            <p>
                                                {{$program->description}}
                                            </p>
                                            @if($program->text)
                                                <div class="border p-1 mb-3">
                                                    {!! $program->text !!}
                                                </div>
                                            @endif
                                            <h4 class="text-end">Speakers</h4>
                                            <div class="d-flex justify-content-end">
                                                @foreach($program->speakers as $s)
                                                    <div class="ms-2 border border-dark p-1">
                                                        <img src="{{$s->imageFullPath}}" alt="" width="35px">
                                                    </div>
                                                @endforeach
                                            </div>
                                            <hr>
                                            <h4 class="text-end">Partners & Sponsors</h4>
                                            <div class="d-flex justify-content-end">

                                                @foreach($program->sponsors as $sp)
                                                    <div class="ms-2 border border-dark p-1">
                                                        <img src="{{$sp->logoFullPath}}" alt="" width="35px">
                                                    </div>
                                                @endforeach
                                            </div>
                                            <hr>
                                            <div class="d-flex justify-content-between">
                                                <button class="btn btn-sm btn-outline-danger mb-3 "
                                                        data-bs-toggle="modal"
                                                        data-bs-target="#{{$deleteModalId}}"
                                                >Löschen
                                                </button>

                                            </div>

                                        </div>
                                    </div>
                                        <div class="bg-white">
                                            <div class="accordion  ">
                                                <div class="accordion-item">
                                                    <h2 class="accordion-header ">
                                                        <button class="accordion-button collapsed fw-bold" type="button" data-bs-toggle="collapse" data-bs-target="#{{$editAccordionId}}"  >
                                                            <span class="h3">Bearbeiten</span>
                                                        </button>
                                                    </h2>
                                                    <div id="{{$editAccordionId}}" class="accordion-collapse collapse">
                                                        <div class="accordion-body bg-brown2 " style="z-index: 9999">
                                                            @include('backend.speakout.program.partials.edit-modal', ["program" => $program, "accordionId" => $editAccordionId])
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                    </div>

                                @endif

                            @endforeach
                        </div>
                    </div>

                @endforeach


            </div>
            <div class="border border-primary p-2 text-center shadow col-lg-6 ">

                @php
                    $editAccordionId = "edit-accordion-new";

                    $deleteModalId = "delete-modal-new";
                @endphp

                @include('backend.speakout.program.partials.edit-modal', ["program" => null, "modalId" => "new-event"])
                <button class="btn  btn-primary"
                        data-bs-toggle="modal"
                        data-bs-target="#new-event"
                >New event +
                </button>
            </div>
        </div>
    </div>
    <script>


        function removeSpeaker(element) {
            const id = element.dataset.speakerId
            const addedSpeakersInput = element.closest('form').querySelector('.added-speakers-input');
            const all = (addedSpeakersInput.value ?? "").split(';').filter(x => x.toString() !== id.toString());
            addedSpeakersInput.value = all.join(';');
            document.getElementById("speaker--" + id)?.remove();
        }


        function addSpeakerEvents(speakerSelectors, speakersRow) {
            speakerSelectors.forEach(speaker => {
                const id = speaker.dataset.speakerId.toString();
                const name = speaker.dataset.speakerName;
                const image = speaker.dataset.speakerImage;

                const addedSpeakersInput = speaker.closest('form').querySelector('.added-speakers-input');

                speaker.onclick = (ev) => {

                    const all = (addedSpeakersInput.value ?? "").split(';').filter(x => x);

                    if (all.includes(id)) {
                        // already here
                        return;
                    }

                    all.push(id)

                    addedSpeakersInput.value = all.join(';');

                    const column = `  <div class="col-lg-3  col-6 mb-3" id="${"speaker--" + id}">

                                    <div class="card h-100">
                                         <div class="text-end">
                                            <button type="button" class="btn btn-sm"
                                            data-speaker-id="${id}"
                                            onclick="removeSpeaker(this)"
                                            >
                                                    <i class="fa-regular fa-xmark text-danger"></i>
                                            </button>
                                        </div>
                                        <img src="${image}"  alt="">
                                        <div class="card-body" >
                                             ${name}
                                        </div>
                                    </div>
                                </div>`;

                    speakersRow.innerHTML += column;
                }
            })
        }


        document.querySelectorAll('.speaker--form').forEach((form) => {
            const speakerSelectors = form.querySelectorAll('.speaker--selector');
            const speakersRow = form.querySelector('.speakers--row');
            addSpeakerEvents(speakerSelectors, speakersRow);
        })

        function removeSponsor(element) {
            const id = element.dataset.sponsorId;
            const addedSponsorsInput = element.closest('form').querySelector('.added-sponsors-input');
            const all = (addedSponsorsInput.value ?? "").split(';').filter(x => x.toString() !== id.toString());
            addedSponsorsInput.value = all.join(';');
            document.getElementById("sponsor--" + id)?.remove();
        }

        function addSponsorEvents(sponsorSelectors, sponsorsRow) {
            sponsorSelectors.forEach(sponsor => {
                const id = sponsor.dataset.sponsorId.toString();
                const name = sponsor.dataset.sponsorName;
                const logo = sponsor.dataset.sponsorLogo;

                const addedSponsorsInput = sponsor.closest('form').querySelector('.added-sponsors-input');

                sponsor.onclick = (ev) => {
                    const all = (addedSponsorsInput.value ?? "").split(';').filter(x => x);
                    if (all.includes(id)) {
                        // already here
                        return;
                    }
                    all.push(id);
                    addedSponsorsInput.value = all.join(';');

                    const column = `<div class="col-lg-3 col-6 mb-3" id="sponsor--${id}">
                                <div class="card h-100">
                                    <div class="text-end">
                                        <button type="button" class="btn btn-sm"
                                        data-sponsor-id="${id}"
                                        onclick="removeSponsor(this)">
                                            <i class="fa-regular fa-xmark text-danger"></i>
                                        </button>
                                    </div>


                                        <div class="p-3" style="max-width: 10rem">
                                            <img src="${logo}" alt="" class="img-fluid">
                                        </div>


                                </div>
                            </div>`;
                    sponsorsRow.innerHTML += column;
                }
            })
        }

        document.querySelectorAll('.speaker--form').forEach((form) => {
            const sponsorSelectors = form.querySelectorAll('.sponsor--selector');
            const sponsorsRow = form.querySelector('.sponsors--row');
            addSponsorEvents(sponsorSelectors, sponsorsRow);
        })

    </script>
@endsection
