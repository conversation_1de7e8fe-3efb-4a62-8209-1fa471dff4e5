@extends('backend.master')

@section("scripts")
    <script src="{{url('inc\sortableJS\sortableJS.js') }}"></script>

    <script>
        const endpoint = '{{url("backend/ajax/speakout/news/sort")}}';
        new Sortable(document.querySelector("#sortable-list"), {
           // handle: '.handle', // handle's class
            animation: 150,

            onSort: function (/**Event*/evt) {

                const newOrder = Array.from(document.querySelectorAll(`[data-news-id]`)).map(element=> (element.dataset.newsId))

                fetch(endpoint,
                    {
                        method: 'POST',
                        headers: {
                            'X-CSRF-TOKEN': "{{ csrf_token() }}",
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({orderArray: newOrder}),
                    }
                )
                    .then(response => response.json())
                    .then(json => console.log(json))
                    .catch((e) => {
                        // On Error reload the Page
                        console.log(e)
                        //location.reload()
                    });


            },
        });
    </script>
@endsection

@section('content')
    <div class=" mb-5">
        <div class="col-12 mb-3">
            <div>
                <h2>Speakout Verwaltung - {{$section_name}}</h2>
            </div>
        </div>
        @if(!empty($contentForm['inputs']))
            <div class="col-12 mb-3">
                @include('backend.speakout.components.contents-form', ["contentForm" => $contentForm])
            </div>
        @endif
        @include('backend.speakout.components.navigation')
        <div id="sortable-list" class="row">
            @foreach($newsArray as $news)
                <div class="col-lg-3 mb-3">
                    <div class="card   shadow h-100"  data-news-id="{{$news->id}}" style="cursor:grab">
                        <img src="{{$news->imageFullPath}}" class="card-image">
                        <div class="card-footer d-flex flex-column justify-content-between h-100">
                            <p>{{$news->title}}</p>

                            <a href="{{ url()->current() . "/$news->id"  }}"
                               class="btn btn-sm btn-brown w-100">Bearbeiten</a>

                        </div>
                    </div>
                </div>
            @endforeach
        </div>


@endsection

