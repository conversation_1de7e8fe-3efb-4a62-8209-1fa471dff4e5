@extends('backend.master')
@section('content')


    <!-- Modal -->
    <div class="modal fade" id="delete-modal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
         aria-hidden="true">
        <div class="modal-dialog  modal-dialog-centered">
            <form class="modal-content" action="{{url()->current() . "/delete"}}"
                  id="form" name=""
                  method="post">
                {{ csrf_field() }}
                @method('delete')
                <div class="modal-header">
                    <h5 class="modal-title" id="any-label">{{$news->title}}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    Wirklich löschen?
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Nein, abbrechen</button>
                    <button type="submit" class="btn btn-primary auto-disable"><i class="fad fa-trash"></i>
                        Ja, löschen
                    </button>
                </div>
            </form>
        </div>
    </div>

    <form action="{{ url()->current() }}"
          method="post"
          enctype="multipart/form-data">
        @method('POST')
        {{ csrf_field() }}

        <div class="row mb-5">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h2>Speakout Verwaltung - {{$section_name}}</h2>
                    {{--@if($news->fullPath)
                        <a href="{{$news->fullPath}}" target="_blank">Post ansehen <i
                                class="fa-solid fa-arrow-up-right-from-square link-primary"
                            ></i></a>
                    @endif--}}
                </div>
            </div>

            <div class="  col-12">



                <div class="mb-3 border p-3">
                    <div class="d-flex ">
                        @if($news->imageFullPath)
                            <div class="w-50 pe-3">
                                <img src="{{url($news->imageFullPath)}}" alt=""
                                     style="max-height: 15rem; max-width:100%">
                            </div>
                        @endif
                        <div class="" style="flex:1">
                            <div class="mb-3">
                                <label for="imageFile"> <b>Bild</b> {!! ($news->imageFullPath) ? 'austauschen' : ''!!}</label>
                                <input type="file" class="form-control" id="imageFile" name="imageFile"
                                       accept="image/*">
                            </div>
                            <div class="mb-3">
                                <label for="imageAlt">Bild ALT</label>
                                <input type="text" class="form-control" id="imageAlt" name="imageAlt"
                                       value="{{ old('imageAlt', $news->imageAlt) }}">
                            </div>
                            <div class="mb-3">
                                <label for="imageOne_CR">Copyright</label>
                                <input type="text" class="form-control" id="imageOne_CR" name="imageCopyright"
                                       value="{{ old('imageCopyright', $news->imageCopyright) }}">
                            </div>
                        </div>
                    </div>
                </div>




                <div class="mb-3">
                    <label for="title">Titel</label>
                    <input type="text" class="form-control w-100" id="title" name="title"
                           value="{{ old('title', $news->title) }}">
                </div>


                <div class="mb-3">
                    <label for="seo_url">Seo Url <small class="text-muted">(Wird automatisch aus dem Titel generiert,
                            wenn nichts eingegeben wurde)</small></label>
                    <input type="text" class="form-control w-100" id="seo_url" name="seo_url"
                           value="{{ old('seo_url', $news->seo_url) }}">
                </div>


                <div class="mb-3">
                    <label for="pre_text">Vorspann </label>
                    <textarea type="text" class="form-control w-100" id="pre_text"
                              style="height: 100px;"
                              name="pre_text">{{ old('pre_text', $news->pre_text) }}</textarea>
                </div>

                <div class="mb-3">
                    <label for="desc_long">News Text</label>

                    <textarea
                        ckeditor="true"
                        ckeditor_height="400"
                        ckeditor_removeButtons="{{env('CKEDITOR_ELEMENTS_MIDI')}}"
                        id="desc_long" class="form-control mb-3 " name="text">{{ old('text', $news->text) }}
							</textarea>


                </div>


                <div class="d-flex justify-content-between">
                    <div>
                        <a class="btn btn-secondary border" href="{{ str_replace("/$news->id", "",url()->current()) }}"> Zurück</a>


                        <!-- Button trigger modal -->
                        <button type="button" class="btn btn-danger ms-3" data-bs-toggle="modal"
                                data-bs-target="#delete-modal">
                            LÖSCHEN
                        </button>


                    </div>
                    <button type="submit" class="btn btn-brown">Speichern</button>
                </div>


            </div>

        </div>
    </form>



    <style>
        /* Hide the text input by default */
        #colorInput {
            visibility: hidden;
        }

        /* Show the text input when the checkbox is checked */
        #toggleTextInput:checked + label + #colorInput {
            visibility: visible;
        }
    </style>





    <script>

        const colorSelector = document.getElementById('colorSelector')

        const setLinkColorBtn = document.getElementById('setLinkColorBtn')

        let color;

        function update() {
            try {
                color = colorSelector.options[colorSelector.options.selectedIndex].dataset.color;

            } catch (e) {
                console.log(e)
            }

        }

        update();

        setLinkColorBtn.addEventListener('click', () => {

            document.getElementById('links-color-input').value = color;
            document.getElementById('links-color-input').dispatchEvent(new Event('input', {bubbles: true}));
        })

        colorSelector.addEventListener('change', () => update())
    </script>

@endsection
