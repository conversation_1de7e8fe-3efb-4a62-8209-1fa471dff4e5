@extends('backend.master')

@section('content')




    <!-- Modal -->
    <div class="modal fade" id="delete-modal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
         aria-hidden="true">
        <div class="modal-dialog  modal-dialog-centered">
            <form class="modal-content" action="{{url()->current() . "/delete"}}"
                  id="form" name=""
                  method="post">
                {{ csrf_field() }}
                @method('delete')
                <div class="modal-header">
                    <h5 class="modal-title" id="any-label">{{$speaker->name}}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    Wirklich löschen?
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Nein, abbrechen</button>
                    <button type="submit" class="btn btn-primary auto-disable"><i class="fad fa-trash"></i>
                        Ja, löschen
                    </button>
                </div>
            </form>
        </div>
    </div>
    <form action="{{ url()->current() }}" id="" name="" method="post" enctype="multipart/form-data" class="row my-4">
        {{ csrf_field() }}
        @method('POST')

        <div class="col-12">
            <h2 class="mb-3">Speakout Verwaltung - Speaker </h2>
        </div>


        <div class="col-lg-4">
            <div class="card">
                <img src="{{$speaker->imageFullPath}}" class="card-image mb-3" onerror="this.classList.add('d-none')">

                <div class="mb-3">
                <label for="formFile" class="form-label">Bild</label>
                <input class="form-control" type="file" id="formFile" name="fileImage">
                </div>
                <div  >
                    <label for="name" class="form-label w-100">Bild Copyright
                        <input type="text" class="form-control" name="imageCopyright"  value="{{$speaker->imageCopyright}}"></label>
                </div>


            </div>
        </div>
        <div class="col-lg-8">


            <div >

                <div class="form-check form-switch form-check-reverse mb-3">
                    <input class="form-check-input" type="checkbox" id="check-show_on_homepage" name="show_on_homepage" @if($speaker->show_on_homepage) checked @endif>
                    <label class="form-check-label" for="check-show_on_homepage">Speaker auf der Startseite anzeigen</label>
                </div>

                <div class="form-check form-switch form-check-reverse mb-3">
                    <input class="form-check-input" type="checkbox" id="check-hidden" name="hidden" @if($speaker->hidden) checked @endif>
                    <label class="form-check-label" for="check-hidden">Speaker ausblenden</label>
                </div>
                @if($speaker->hidden)
                    <div class="alert alert-warning">
                       Der Speaker ist ausgeblendet
                    </div>
                @endif

                <div class="mb-3">
                    <label for="name" class="form-label w-100">Reihenfolge <small>(In dieser Reihenfolge in der Slideshow anzeigen, für zufällige Reihenfolge leer lassen)</small>
                        <input type="number" step="1" min="0" class="form-control" name="order"  value="{{$speaker->order}}"></label>
                </div>

                <div class="mb-3">
                    <label for="name" class="form-label w-100">Name
                    <input type="text" class="form-control" name="name"  value="{{$speaker->name}}"></label>
                </div>

                <div class="mb-3">
                    <label class="form-label w-100">Card kurze Beschreibung
                        <textarea type="text" class="form-control"  name="shortDescription"
                                  >{{$speaker->shortDescription}}</textarea>
                    </label>
                </div>



                <label class="form-label w-100">Biography
                <textarea
                    ckeditor="true"
                    ckeditor_height="300"
                    ckeditor_removeButtons="{{env('CKEDITOR_ELEMENTS_MIDI')}}"
                    id="desc_long" class="form-control mb-3 " name="biography">{{ old('text', $speaker->biography) }}
							</textarea>
                </label>

            </div>


        </div>

        <div class="col-12">
            <h2>
                Social Media & Websites
            </h2>
            <table class="table table-bordered" >
                <tr>
                    <th>Type</th>
                    <th>Url</th>
                </tr>

                <tbody id="socialmedia-table-body">
                @foreach($speaker->socialMediaArray as $sm)
                    <tr>
                        <td>

                            <select class="form-select"  name="mediaType[]">
                                <option  value="">Löschen</option>
                                @foreach($mediaTypes as $type)
                                    <option @if($type === $sm->type) selected @endif value="{{$type}}">{{$type}}</option>
                                @endforeach
                            </select>

                        </td>
                        <td class="w-75">
                            <input class="form-control" name="mediaUrl[]" type="text" value="{{$sm->url}}">
                        </td>
                    </tr>
                @endforeach
                <tr id="new-social-media-fields">
                    <td>

                        <select class="form-select"  name="mediaType[]">
                            <option  value="">Keine Auswahl</option>
                            @foreach($mediaTypes as $type)
                                <option  value="{{$type}}">{{$type}}</option>
                            @endforeach
                        </select>

                    </td>
                    <td class="w-75">
                        <input class="form-control" name="mediaUrl[]" type="text" value="">
                    </td>
                </tr>
                </tbody>



            </table>
            <div class="pb-3 text-end">
                <button id="btn-add-social-media" type="button" class="btn btn-brown">Add +</button>
            </div>
        </div>

        <div class="col-12 d-flex justify-content-between">
            <div>
                <a class="btn btn-secondary border" href="{{url('backend/speakout/speaker')}}"> Zurück</a>


                <!-- Button trigger modal -->
                <button type="button" class="btn btn-danger ms-3" data-bs-toggle="modal"
                        data-bs-target="#delete-modal">
                    LÖSCHEN
                </button>


            </div>

            <button type="submit" class="btn btn-brown">SPEICHERN</button>
        </div>

    </form>

    <script>
        const originalField = document.getElementById('new-social-media-fields');
        if(originalField){
            const copiedSocialMediaFields = originalField.cloneNode(true);
            copiedSocialMediaFields.id = ""
            document.querySelector('#btn-add-social-media').addEventListener('click',() => {
                document.querySelector('#socialmedia-table-body').appendChild(copiedSocialMediaFields.cloneNode(true))
            })
        }else {
            console.log('Nop')
        }

    </script>
@endsection
