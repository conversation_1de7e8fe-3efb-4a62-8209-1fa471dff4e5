@extends('backend.master')

@section("scripts")



@endsection

@section('content')
    <div class=" mb-5">
        <div class="col-12 mb-3">
            <div>
                <h2>Speakout Verwaltung - Gallery</h2>
            </div>
        </div>

        <div class="col-12 mb-3">
            <div class="d-flex justify-content-between">
                <div>
                    <a class="btn btn-brown" href="{{url('backend/speakout/gallery')}}">Zurück</a>
                </div>
            </div>
        </div>


        <form action="{{ url()->current() }}" method="post" enctype="multipart/form-data" class="p-3 rounded shadow-sm">
            @csrf

            <h3>
                Fotodetails bearbeiten
            </h3>

            <hr>

            <div class="form-check form-switch mb-3 ">
                <input class="form-check-input" type="checkbox" role="switch" id="switchCheckChecked"
                       name="display"
                       @if($image->display)
                           checked
                           @endif
                >
                <label class="form-check-label fw-bold" for="switchCheckChecked">Foto in der Fotogalerie anzeigen
                </label>
            </div>

            <hr>

            {{-- Titolo --}}
            <div class="col-lg-6">
                <div class="mb-3">
                    <label for="title" class="form-label">Titel</label>
                    <input type="text" name="title" id="title" class="form-control @error('title') is-invalid @enderror"
                           value="{{ old('title', $image->title ?? '') }}"
                           required maxlength="255">
                    @error('title')
                    <div class="invalid-feedback">
                        {{ $message }}
                    </div>
                    @enderror
                </div>
            </div>


            <hr>

            <div class="row align-items-end">


                <div class="col-lg-6">
                    <div class=" ">
                        <label for="image" class="form-label">Bild</label>
                        <input type="file" name="image" id="image"
                               class="form-control @error('image') is-invalid @enderror"
                               accept=".jpg,.jpeg,.png,.gif"  >
                        @error('image')
                        <div class="invalid-feedback">
                            {{ $message }}
                        </div>
                        @enderror
                    </div>
                </div>

                <div class="col-lg-6 d-flex justify-content-end">

                    @if (!empty($image->fullPath))
                        <div class=" ">

                            <img src="{{ $image->fullPath }}" alt="Bild" class="img-thumbnail"
                                 style="max-width: 150px;">
                        </div>
                    @endif

                </div>

            </div>
            <hr>

            {{-- Submit --}}
            <div class="d-grid">
                <button type="submit" class="btn btn-brown">Speichern</button>
            </div>
        </form>



@endsection

