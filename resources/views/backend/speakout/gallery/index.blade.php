@extends('backend.master')

@section("scripts")



@endsection

@section('content')
    <div class=" mb-5">
        <div class="col-12 mb-3">
            <div>
                <h2>Speakout Verwaltung - Gallery</h2>
            </div>
        </div>


        <div class="col-12 mb-3">
            <div class="d-flex justify-content-between">
                <div>
                    <a class="btn btn-brown" href="{{url('backend/speakout')}}">Zurück</a>
                </div>
                <div>
                    <a class="btn btn-brown" href="{{url()->current(). '/new'}}">+ NEU</a>
                </div>
            </div>
        </div>


        <div class="row">

            @foreach($images as $image)

                <!-- Modal -->
                <div class="modal fade" id="image-delete-modal-{{$image->id}}" tabindex="-1"
                     aria-labelledby="image-delete-modal-{{$image->id}}Label" aria-hidden="true">
                    <form class="modal-dialog" method="POST" action="{{url()->current()}}/{{$image->id}}/delete">
                        @csrf
                        @method('DELETE')
                        <div class="modal-content">
                            <div class="modal-header">
                                <h1 class="modal-title fs-5" id="image-delete-modal-{{$image->id}}Label">Wirklich
                                    löschen ?</h1>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"
                                        aria-label="Close"></button>
                            </div>
                            <div class="modal-body text-center">


                                <img src="{{$image->fullPath}}" style="max-height: 20rem" alt="">
                                <div class="p-3">
                                    {{$image->title}}
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="submit" class="btn btn-brown">Ja, löschen</button>
                                <button type="button" class="btn btn-light" data-bs-dismiss="modal">Nein, abbrechen
                                </button>

                            </div>
                        </div>
                    </form>
                </div>

                <div class="col-lg-3 mb-3">
                    <div class="card h-100 p-0">
                        <div class="card-body p-0 h-100">
                            <img src="{{$image->fullPath}}" class="img-fluid" alt="">
                            <div class="p-3 ">
                                <div>
                                    {{$image->title}}
                                </div>


                            </div>


                        </div>

                        <div class="card-footer">


                            <div class="form-check form-switch mb-3 ">
                                <input class="form-check-input "
                                       data-displayed-image={{$image->id}}
                                           type="checkbox" role="switch"
                                       id="switchCheckChecked-{{$image->id}}"
                                       @if($image->display)
                                           checked
                                    @endif
                                >
                                <label class="form-check-label" for="switchCheckChecked-{{$image->id}}">
                                    in der Galerie anzeigen
                                </label>
                            </div>

                            <div class="d-flex justify-content-between">
                                <a href="{{url()->current()}}/{{$image->id}}" class="btn btn-danger"
                                   data-bs-toggle="modal"
                                   data-bs-target="#image-delete-modal-{{$image->id}}">Löschen</a>
                                <a href="{{url()->current()}}/{{$image->id}}" class="btn btn-brown">Bearbeiten</a>

                            </div>

                        </div>
                    </div>
                </div>
            @endforeach
        </div>

        <script>

            const endpoint = "/backend/ajax/speakout/gallery/displayed-images";

            const allSwitches = Array.from(document.querySelectorAll('[data-displayed-image]'))


            const sendRequest = async (data) => {
                const req = await fetch(endpoint, {
                    method: "POST",
                    body: JSON.stringify({displayedImages:data}),
                    headers: { "Content-Type": "application/json",
                        "X-CSRF-TOKEN": "{{csrf_token()}}"
                    },
                })

                const json = await req.json();

                return json;
            }


            const onChanges = () => {
                const data = {}

                allSwitches.forEach(e => {
                    data[e.dataset.displayedImage] = e.checked
                })

                sendRequest(data).then(res => {
                    console.log(res)
                })

            }

            allSwitches.forEach((element) => {
                element.addEventListener('change', () => {
                    onChanges();
                })
            })

        </script>

@endsection

