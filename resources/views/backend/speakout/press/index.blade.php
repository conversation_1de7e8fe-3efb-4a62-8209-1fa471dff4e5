@extends('backend.master')

@section('content')
    <div class="row mb-5">
        <div class="col-12 mb-3">

            <h2>Speakout Verwaltung - Presse</h2>

        </div>

        <div class="col-12">

            <div class="d-flex justify-content-between">
                <div>
                    <a class="btn btn-brown" href="{{url('backend/speakout')}}">Zurück</a>
                </div>

                <div>
                    <a class="btn btn-brown" href="{{url('backend/speakout/press/new')}}">  <i class="fa-solid fa-plus"></i> NEU</a>
                </div>

            </div>

            <hr>

        </div>

        @foreach($allSpeakoutPress as $speakoutPress)

            <div class="modal fade" id="delete-modal-{{$speakoutPress->id}}" tabindex="-1" >
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h1 class="modal-title fs-5" id="delete-modal-{{$speakoutPress->id}}Label">{{$speakoutPress->name}}</h1>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            Wirlklich löschen?
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-light" data-bs-dismiss="modal">Nein</button>
                            <form action="{{ url()->current() ."/$speakoutPress->id/delete" }}"  method="post" >
                                {{ csrf_field() }}
                                @method("DELETE")
                                <button type="submit" class="btn btn-brown">Ja, löschen</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        @endforeach
        <div class="col-12">
            <div class="row">
                @foreach($allSpeakoutPress as $speakoutPress)




                <div class="col-lg-3">


                        <div class="card  h-100"  >

                                @if($speakoutPress->imageFullPath)
                                    <img src="{{$speakoutPress->imageFullPath}}" alt="" class="card-img-top">
                                @endif


                            <div class="card-body d-flex flex-column justify-content-end "  >
                                <p>{{$speakoutPress->name}}</p>
                                <div class="d-flex justify-content-between">
                                    <button type="button" class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#delete-modal-{{$speakoutPress->id}}">
                                        Löschen
                                    </button>
                                    <a href="{{url()->current() . "/$speakoutPress->id"}}" class="btn btn-brown">Bearbeiten</a>
                                </div>
                            </div>

                        </div>


                </div>







                @endforeach
            </div>

        </div>

    </div>

@endsection
