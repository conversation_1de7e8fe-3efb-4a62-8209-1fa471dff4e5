@extends('backend.master')

@section('content')
    <div class="row mb-5">
        <div class="col-12 mb-3">

            <h2>Speakout Verwaltung - Presse</h2>

        </div>

        <div class="col-12">

            <div class="d-flex justify-content-between">
                <div>
                    <a class="btn btn-brown" href="{{url('backend/speakout/press')}}">Zurück</a>

                </div>


            </div>

            <hr>

        </div>

        <div class="col-12">

                <div class="row">
                    <div class="col-md-6 ">

                        <form action="{{ url()->current() }}" method="POST" enctype="multipart/form-data">
                            @csrf
                            <div class="mb-3">
                                <label for="name">Bezeichnung</label>
                                <input type="text" class="form-control" id="name" name="name" placeholder="Bezeichnung" required value="{{old("name", $speakoutPress->name)}}">
                            </div>

                            <div class="mb-3">
                                @if($speakoutPress->imageFullPath)
                                    <div class="border p-3 mb-3">
                                        <img src="{{$speakoutPress->imageFullPath}}" alt="" class="img-fluid">
                                    </div>
                                    <label for="formFile" class="form-label">Bild austauschen</label>
                                @else
                                    <label for="formFile" class="form-label">Bild</label>
                                @endif

                                <input class="form-control" type="file" id="formFile" name="image" accept="image/*">
                            </div>

                            <div class="text-end">
                                <button type="submit" class="btn btn-brown">Speichern</button>
                            </div>


                        </form>
                    </div>
                </div>

        </div>

    </div>

@endsection
