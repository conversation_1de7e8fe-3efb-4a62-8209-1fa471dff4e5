@extends('backend.master')
@section('content')

    @if(empty($token))
        <form action="{{ url("backend/webchat-login") }}" method="post" class="d-flex justify-content-center">
            {{ csrf_field() }}
            @method('POST')
            <div class="text-center p-5 mt-5 shadow">

                <h3 class="mb-3"><PERSON><PERSON> {{$agent->nickname}} !</h3>
                <button type="submit" class="btn btn-success p-5">
                    Jetzt den Chat betreten
                </button>
            </div>
        </form>
    @else

        <div class="alert alert-success mt-5">
            Loading...
        </div>

        {{--Store token and redirect to Chat--}}
        <script>

           const token =  '{!! $token !!}';

           const key = '{!! $key !!}';

           const chatUrl = '{!! $chatUrl !!}';

           localStorage.setItem(key, token)

           window.location.href = chatUrl;

        </script>
    @endif

@endsection
