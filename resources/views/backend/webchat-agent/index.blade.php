@extends('backend.master')
@section('content')

    <div class="py-3 d-flex justify-content-between">
        <h2>Webchat Agent-Verwaltung</h2>

        <button type="button" class="btn btn-brown mb-3" data-bs-toggle="modal" data-bs-target="#new-agent-modal">
            Neuen Agenten hinzufügen
        </button>
        @include('backend.webchat-agent.components.new-agent')
    </div>


    <div class="form-group mb-3 w-25">
        <label for="user_email">Suchen</label>
        <input type="text" class="form-control" id="userSearchInput"
               placeholder="">
    </div>



    <table class="table table-bordered ">
        <thead>
        <tr>
            <th style="width: 11rem"></th>
            <th>Agent</th>
            <th>Berechtigungen</th>
            <th class="text-center">Bearbeiten</th>
            <th class="text-center">Löschen</th>
        </tr>
        </thead>
        <tbody>

        @foreach($agents as $agent)

            @php
                if($agent->chatbot) continue;
            @endphp



            {{--backend/webchat-agent/{agent}--}}

            @php

                $mpUser = $agent->mpUser()->first();
                $chatBackendPermission= ($mpUser) ? $mpUser->hasPermission('webchat') : false;
                $chatAgentPermission =($mpUser) && $mpUser->hasPermission('webchat-agent');


            @endphp



            <tr class=" agent-table-row " data-agent="{!! $agent->email !!}">
                <td class="position-relative">

                    @if($chatAgentPermission)
                        <div class="position-absolute top-0 end-0 pt-1 " title="Agent-Verwaltung Admin">

                            <div class="fa-stack">
                                <i class="fa-solid fa-circle fa-stack-2x " style="color: #FFD700 "></i>
                                <i class="fa-solid fa-star fa-stack-1x fa-inverse"
                                   style="color: white "></i>
                            </div>

                        </div>
                    @endif

                    @php
                        $currentImage = ($agent->imageFullPath) ? : url('img/no-image-available.jpg');
                    @endphp
                    <img src="{{$currentImage . '?v=' . now()->timestamp}}" alt="" class="w-100"
                         onerror="this.src = '{!! url('img/no-image-available.jpg') !!}'">


                </td>
                <td>

                    <table>
                        @if($agent->ghost)
                            <tr>
                                <td colspan="100 text-center">
                                    <i class="fa-regular fa-ghost fa-2x text-info"></i>
                                </td>
                            </tr>
                        @endif
                        <tr>
                            <td class="fw-bold">E-Mail</td>
                            <td> {{$agent->email}}</td>
                        </tr>
                        <tr>
                            <td class="fw-bold">Vollname</td>
                            <td> {{$agent->user}}</td>
                        </tr>
                        <tr>
                            <td class="fw-bold pe-2">Agentenname </td>
                            <td> {{$agent->nickname}}</td>
                        </tr>

                    </table>


                </td>
                <td>

                    <div >
                        <h6 class="mb-1">CHAT VERWALTUNG</h6>
                        @if($mpUser)

                            <div class="  text-dark " style="white-space: nowrap">
                                @if($chatBackendPermission)
                                    <i class="fa-solid fa-circle-check text-success "></i> Berechtigt
                                @else
                                    <i class="fa-solid fa-circle-xmark text-danger "></i> Nicht berechtigt
                                @endif

                            </div>
                        @else
                            NO MP-User
                        @endif
                    </div>



                    @if($agent->media->count())
                        <div class="mt-4">
                            <h6 class="mb-1">MANDANTEN</h6>

                            @foreach($agent->media as $media)
                                <div class="  text-dark">
                                    <i class="fa-solid fa-circle-check text-success "></i> {{ $media->title  }}
                                </div>
                                @if(!$loop->last)
                                    <hr class=" my-1">
                                @endif
                            @endforeach
                        </div>
                    @endif




                </td>



                <td class="text-center">
                    {{--Edit Button--}}
                    @include('backend.webchat-agent.components.agent-update-modal',
                    ['agent' => $agent, 'chatBackendPermission' => $chatBackendPermission, 'allMedia' => $allMedia])
                </td>

                <td class="text-center">

                    @include('backend.webchat-agent.components.delete-modal', ['agent' => $agent])

                    <button type="button" class="btn btn-danger " data-bs-toggle="modal"
                            data-bs-target="#delete-modal-{{$agent->id}}">
                        <i class="fa-regular fa-trash"></i>
                    </button>
                </td>

            </tr>
        @endforeach
        </tbody>

    </table>


    {{--CroperJs Container--}}
    <div id="cropper-js-container" style="display:none;">
        <div class="position-fixed d-flex justify-content-center flex-column"
             style="bottom:0; top: 0; left: 0 ; right: 0; z-index:99999999; background:rgba(0,0,0,0.76)">
            <div class="row" style="bottom: 5rem;">
                <div class="col-md-8 offset-md-2 p-3 position-relative">
                    <div class="card shadow position-relative overflow-hidden">
                        <div style="max-height: 75vh">
                            <img src="" id="cropperID" alt="..." style="max-width:100%">
                        </div>
                        <div class="border-top border-danger p-3 text-end w-100 card-footer">
                            <div class="btn btn-outline-danger me-2" onclick="cancelCropper()">
                                Abbrechen
                            </div>
                            <div class="btn btn-danger me-2" onclick="getCroppedImage()">
                                Anwenden
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script>
        const agentsRows = Array.from(document.getElementsByClassName('agent-table-row'));
        const userSearchInput = document.getElementById('userSearchInput');

        userSearchInput.addEventListener('keyup', (ev) => {
            const key = ev.target.value.toLowerCase().trim();
            agentsRows.forEach(x => {
                x.classList.add('d-none')
                if (x.dataset.agent.includes(key)) x.classList.remove('d-none')
            })
        })
    </script>




    <script>


        const imageFileInputs = Array.from(document.querySelectorAll(`[data-image-file-input]`));
        const imageTags = Array.from(document.querySelectorAll(`[data-image-id]`));


        function getTarget(id) {
            const target = imageTags.find(x => x.dataset.imageId === id.toString())
            const imageFileInput = imageFileInputs.find(x => x.dataset.imageFileInput === id.toString())
            return {
                target,
                imageFileInput,
                resetBtn: document.getElementById('reset-image-' + id)
            }
        }

        function resetImage(id) {

            const {target, imageFileInput, resetBtn} = getTarget(id)

            imageFileInput.value = ""

            target.src = target.dataset.imageOriginal;

            resetBtn?.classList.add('d-none')

        }

        function putImage(input) {

            const id = input.target.dataset.imageFileInput;

            const fileList = input.target.files;

            const {target, resetBtn} = getTarget(id)

            if (!target) return;

            if (!fileList?.length) return;

            const file = fileList[0];

            target.src = URL.createObjectURL(file)

            resetBtn.classList.remove('d-none')

        }

        [...imageFileInputs].forEach((input) => {
            input.addEventListener('change', (input) => putImage(input))
        })


    </script>

@endsection
