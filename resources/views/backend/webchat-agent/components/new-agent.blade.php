<!-- Modal -->
<div class="modal fade" id="new-agent-modal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <form class="modal-content" action="" method="POST" id="addAgentForm">
            @csrf
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel"> Agent hinzufü<PERSON></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">


                <div class="form-group mb-3">
                    <label for="user_email">User nach E-Mail suchen <small>(Mindestens 3 Buchstaben)</small></label>
                    <input type="text" class="form-control" id="user_email"
                           placeholder="User E-Mail">
                </div>

                <div id="users_container" class=>
                </div>


            </div>
            <div class="modal-footer d-flex justify-content-between">
                <button type="button" class="btn btn-sm btn-secondary" data-bs-dismiss="modal">Abbrechen</button>
                <button type="submit" class="btn  btn-sm btn-brown">Als Agent hinzufügen</button>
            </div>
        </form>
    </div>
</div>

<script>

    class SearchUserService {
        previousSearch = '';
        endpoint = '/backend/ajax/webchat-agent/users';
        searchInput;
        usersContainer;

        constructor() {
            this.usersContainer = document.getElementById('users_container')
            this.searchInput = document.getElementById('user_email');
            this.searchInput.addEventListener('keyup', (event) => this.keyUpHandler(event))
        }


        keyUpHandler(event) {
            if (event.target.value.length < 3) {
                this.usersContainer.innerHTML = '';
                return; // reset
            }

            const response = this.getUsersAjax(event.target.value)

            response.then(result => {

                this.previousSearch = result.input;
                this.usersContainer.innerHTML = this.getUsersHtml(result.users);

            })
        }

        getUsersHtml(users) {
            return users.map((userEmail, index) => {
                return `
                    <div class="form-check mb-3">
                      <input class="form-check-input" type="radio" name="user_email" id="user-select-radio-${index}" value="${userEmail}" required />
                      <label class="form-check-label" for="user-select-radio-${index}"> ${userEmail} </label>
                    </div>
                `;
            }).join('');
        }


        getUsersAjax(keyword) {
            return fetch(this.endpoint, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="_token"]').getAttribute('content'),
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({keyword: keyword}),

            }).then(x => x.json())
        }


    }


    new SearchUserService();


</script>
