<!-- Button trigger modal -->
<button type="button" class="w-100 btn btn btn-success"   data-bs-toggle="modal" data-bs-target="#agent-info-update-modal-{{$agent->id}}">
      <i class="fas fa-edit"></i>
</button>

<!-- Modal -->
<div class="modal fade" id="agent-info-update-modal-{{$agent->id}}" tabindex="-1" aria-labelledby="agent-info-update-modal-{{$agent->id}}Label" aria-hidden="true">
    <div class="modal-dialog">

        <form class="modal-content"  action="{{ url("backend/webchat-agent/" . $agent->id) }}"  method="post" enctype="multipart/form-data">
            {{ csrf_field() }}
            @method('PUT')
            <div class="modal-header">
                <h5 class="modal-title" id="agent-info-update-modal-{{$agent->id}}Label">{{$agent->user}}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">


                <div class="d-flex justify-content-between align-content-center">
                    <div class=" w-50 pe-3"  >

                        <img src="{{$agent->imageFullPath . '?v=' . now()->timestamp}}" alt="" class="w-100" data-image-original ="{{$agent->imageFullPath}}" data-image-id ="{{$agent->id}}">
                        <button id="reset-image-{{$agent->id}}" type="button" class="btn btn-warning w-100 btn-sm d-none "  style="font-size: .9rem; white-space: nowrap" onclick="resetImage({{$agent->id}})">Reset Bild</button>

                        <label class="btn btn-success position-relative btn-sm w-100" style="font-size: .9rem; white-space: nowrap">
                            Austauschen
                            <input name="image_file" type="file" accept="image/*" class="visually-hidden position-absolute" data-image-file-input = "{{$agent->id}}">
                        </label>

                    </div>
                    <div class="w-50">
                        <div class=" w-100 text-start">
                            <label for="agent-nickname" class="">Agentenname im Chat</label>
                            <input name="nickname" type="text" class="form-control" id="agent-nickname" value="{{$agent->nickname}}">
                        </div>

                    </div>
                </div>

                <hr>

                <div class="text-start">



                    <h5>Webchat Verwaltung</h5>
                    <label class="form-check form-switch   fs-5">
                        <input
                            class="form-check-input"
                            name="web_chat_permission"
                            type="checkbox"
                            role="switch"
                            @if($chatBackendPermission) checked @endif />
                         Berechtigung <br>
                    </label>
                    <hr>
                    <h5>Agenten-Mandant-Chat-Zugriff</h5>
                    @foreach($allMedia as $md)
                        <label class="form-check form-switch  fs-5 ">
                            <input
                                class="form-check-input"
                                name="media[{{$md->id}}]"
                                type="checkbox"
                                role="switch"
                                @if($agent->media->contains('id',$md->id)) checked @endif />
                            {{$md->title}} <br>
                        </label>
                    @endforeach

                </div>



            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">ABBRECHEN</button>
                <button type="submit" class="btn btn-brown">SPEICHERN</button>
            </div>
        </form>
    </div>
</div>
