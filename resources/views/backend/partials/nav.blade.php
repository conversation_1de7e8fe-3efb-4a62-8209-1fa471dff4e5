@php use App\Facades\AdminUser; @endphp
<nav class="navbar navbar-expand-lg navbar-light bg-white  ">
    <div class="container  pt-lg-4 pt-2 d-block">
        <div class="row ">
            <div class="col-6">
                <a href="{{url('/')}}">
                    <img src="https://www.mediaprint.at/storage/app/public/logos/Mediaprint_Logo_RGB.png" width="200"
                         alt="MediaPrint" title="MediaPrint"/>
                </a>
            </div>
            <div class="col-6 text-end ">
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse"
                        data-bs-target="#navbarSupportedContent"
                        aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            @if(AdminUser::auth())
                <div class="col-12">
                    <div class="collapse navbar-collapse bg-white pt-lg-5" id="navbarSupportedContent">
                        <ul class="navbar-nav me-auto mb-2 mb-lg-0 w-100 justify-content-between bg-white">

                            <li class="nav-item py-1 py-lg-0  ">
                                <a class="nav-link px-0 text-dark" href="{{ url('backend') }}"><i
                                        class="fas fa-home"></i>
                                    START
                                </a>
                            </li>
                            @if(AdminUser::hasPermission("faq"))
                                <li class="nav-item py-1 py-lg-0">
                                    <a class="nav-link px-0 text-dark" href="{{ url('backend/faq') }}"><i
                                            class="fas fa-question-circle"></i>
                                        FAQ
                                    </a>
                                </li>
                            @endif

                            @if(AdminUser::hasPermission("mediathek"))
                                <li class="nav-item py-1 py-lg-0">
                                    <a class="nav-link px-0 text-dark" href="{{ url('backend/mediathek') }}"><i
                                            class="fas fa-newspaper"></i>
                                        MEDIATHEK
                                    </a>
                                </li>
                            @endif

                            <li class="nav-item py-1 py-lg-0 dropdown ">
                                <a class="nav-link px-0 text-dark dropdown-toggle"
                                   href="#" id="navbarDropdown" role="button"
                                   data-bs-toggle="dropdown"
                                   aria-expanded="false">
                                    MEDIAPRINT

                                </a>

                                <ul class="dropdown-menu shadow border-0 " style="min-width: 35vw">
                                    <div class="row">
                                        @foreach( AdminUser::get()->getPersonalizedMenu() as $current)

                                            <div class="col-xl-6">
                                                <div class="px-2">
                                                    <div class="p-3 fw-bold  fs-5 ">
                                                        {{$current['group']}}
                                                    </div>


                                                    @foreach($current['items'] as $item)
                                                        <li>
                                                            <a class="dropdown-item {{(!$loop->first) ? "border-top" : ""}} " href="{{$item['url']}}">{{$item['name']}}</a>
                                                        </li>
                                                    @endforeach
                                                </div>

                                            </div>
                                        @endforeach
                                    </div>
                                </ul>
                            </li>
                            <li class="nav-item ">
                                <a class="nav-link text-dark " href="{{url("backend/logout")}}">
                                    <i class="fas  fa-user "></i> {{mb_strtoupper(trans(AdminUser::getFirstName()))}}
                                    ABMELDEN
                                    <span class="fas fa-sign-out-alt"></span>
                                </a>
                            </li>
                        </ul>

                    </div>
                </div>
            @endif

        </div>
    </div>
</nav>

