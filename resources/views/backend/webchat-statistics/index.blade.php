@extends('backend.master')
@section('container-fluid')

    <div class="py-3 ">
        <h2>Webchat Analytics </h2>

    </div>


    <ul class="nav nav-tabs mb-3">

        @php
            $queryString = request()->getQueryString() ? "?" .request()->getQueryString() : "";

        @endphp

        @foreach($tabs as $tab)
            <li class="nav-item">
                <a class="nav-link fs-5
                {{($loop->index == $selectedIndex) ? 'active fw-bold text-dark' : 'link-secondary'}}"
                   aria-current="page"
                   href="{{url( "backend/webchat-statistics" . ($loop->index ? "/$loop->index" : "")  )}}">
                    {{$tab['title']}}
                </a>
            </li>
        @endforeach
    </ul>

    <div class="d-flex justify-content-between " id="command-panel">
        @if($tabs[$selectedIndex]['updatedAt'] instanceof  \Carbon\Carbon)
            <div>
                <span class=" fs-6">
                    Letzte Aktualisierung: <b>{{$tabs[$selectedIndex]['updatedAt']->format(' d.m.Y H:i')}}</b>
                </span>
            </div>

        @endif
        @if($tabs[$selectedIndex]['showDate'] && $dates)



            <div id="date-range-inputs">

                <form class="d-flex" action="{{url()->current()}}" method="get"  >



                    <div class="input-group mb-3 me-3 " style="max-width: 20rem ">
                        <span class="input-group-text bg-brown3 text-white" id="date-input">Von</span>
                        <input name="from" type="date" class="form-control" value="{{$dates['from']->format('Y-m-d')}}"

                               max="{{now()->format('Y-m-d')}}">
                    </div>

                    <div class="input-group mb-3 me-3 " style="max-width: 20rem ">
                        <span class="input-group-text bg-brown3 text-white" id="date-input">Bis</span>
                        <input name="to" type="date" class="form-control" value="{{$dates['to']->format('Y-m-d')}}"

                               max="{{now()->format('Y-m-d')}}">
                    </div>
                    <div>
                        <button type="submit" class="btn btn-brown ">Filter</button>
                    </div>
                </form>



                    <div>
                        @php
                            $tmp = $dates['dayBefore']->format('Y-m-d');
                            $queryDayBefore= "?".http_build_query(["from" => $tmp, "to" => $tmp]);


                                 $tmp = $dates['dayAfter']->format('Y-m-d');
                                 $queryDayAfter= "?".http_build_query(["from" => $tmp, "to" => $tmp]);

                        @endphp

                        <a href="{{url()->current().$queryDayBefore}}" class="btn btn-brown me-3" style="white-space: nowrap">

                            <i class="fa-solid fa-arrow-left"></i> Vorheriger Tag
                        </a>



                        <a href="{{url()->current().$queryDayAfter}}" class="btn btn-brown me-3" style="white-space: nowrap">

                           Nächster Tag   <i class="fa-solid fa-arrow-right"></i>
                        </a>



                    </div>

            </div>


        @else
            <span></span>
        @endif
        @if(!empty($table))
            <div>
                <a href="{{url("backend/webchat-statistics/" . $selectedIndex ."/download" .    $queryString )}}"
                   class="btn btn-brown mb-3"
                   target="_blank">Herunterladen <i class="fa-duotone fa-download"></i></a>
            </div>

        @endif
    </div>
    <div id="loading" class="d-none">
        <div class="alert alert-info d-flex align-items-center justify-content-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <div class="ms-3 fs-5">
                Die Seite wird aktualisiert
            </div>
        </div>
    </div>


    @if(empty($table))
        <div class="alert alert-warning text-center fs-5" id="no-data-alert">
            Für dieses Datum sind keine Daten verfügbar
        </div>
    @else

        <div class="w-100 overflow-auto mb-5 table-container">
            <table class="table table-bordered  ">
                <thead>
                <tr>
                    @foreach($table[0]['data'] as $key=>$x)
                        <th scope="col" style="white-space: nowrap">{{$key}}</th>
                    @endforeach
                </tr>
                </thead>
                <tbody>
                @foreach($table as $row)
                    <tr class="{{(!empty($row['isFullHour']) ?  "bg-light" :  '')}}">
                        @foreach($row['data'] as $tmp)
                            <td>{{$tmp}}</td>
                        @endforeach
                    </tr>

                @endforeach
                </tbody>
            </table>

        </div>

        <style>
            .table-container {
                transform: rotateX(180deg);
                overflow-x: auto;

            }

            table {
                transform: rotateX(180deg);
            }
        </style>

    @endif
    <script>

        const dateInput =  document.getElementById('date-range-inputs');

        if (!dateInput) {
            const currentUrl = window.location.href;
            const cleanUrl = currentUrl.split('?')[0];
            window.history.replaceState({}, document.title, cleanUrl);
        }




    </script>
@endsection
