@extends('backend.master')

@section('content')
    <div class="row">
        <div class="col my-5">
            <h1 class="card-title">ERGEBNISSE FREIZEIT
                                   VOTING {{ (request()->segment(3)??date("Y")) }}</h1>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <ul class="nav nav-tabs" id="myTab" role="tablist">
                <li class="nav-item">
                    <a class="nav-link active" id="gasthausTab" href="#gasthausVoting" role="tab" aria-controls="gasthausVoting" aria-selected="true">Ergebnisse
                                                                                                                                                      Gasthaus-Voting
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" id="gerichteTab" href="#gerichteVoting" role="tab" aria-controls="gerichteVoting" aria-selected="false">Ergebnisse
                                                                                                                                                Beste Gerichte
                    </a>
                </li>
            </ul>
            <div class="tab-content" id="myTabContent">
                <div class="tab-pane fade show active" id="gasthausVoting" role="tabpanel" aria-labelledby="gasthausTab">
                    <h2 class="my-5">Ergebnisse Gasthaus-Voting</h2>
                    <div class="row">
                        <div class="col-12 mb-5">
                            <a href="{{ url("backend/freizeitvoting/".(request()->segment(3) ?? date("Y"))."/export/gasthaeuser") }}">
                                <span class="btn btn-secondary pt-2">Als Excel downloaden</span>
                            </a>
                        </div>
                        <div class="col-12 mb-5">
                            @if(!empty($gasthausVotings))
                                <table class="table table-striped table-vertical-middle table-bordered"
                                       datatable="true"
                                       data-paging="true" data-page-length="50"
                                       data-order="[[1, &quot;asc&quot; ]]">
                                    <thead class="table-dark">
                                    <tr>
                                        <th data-orderable="true">#</th>
                                        <th data-orderable="true">Gasthaus</th>
                                        <th data-orderable="true">Essen<br>
                                            <small>0-50</small>
                                        </th>
                                        <th data-orderable="true">Service<br>
                                            <small>0-10</small>
                                        </th>
                                        <th data-orderable="true">Getränke<br>
                                            <small>0-15</small>
                                        </th>
                                        <th data-orderable="true">Ambiente<br>
                                            <small>0-25</small>
                                        </th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    @foreach($gasthausVotings as $voting)
                                        <tr>
                                            <td>{{ $voting["id"] }}</td>
                                            <td data-sort="{{ $voting["freizeit_gasthaus_id"] }}">{{ $voting->freizeit_gasthaus["title"] }}
                                                <br>
                                                ({{ $voting->freizeit_gasthaus["plz"] }} {{ $voting->freizeit_gasthaus["ort"] }}
                                                )
                                            </td>
                                            <td>{{ $voting["kategorie1"] }}</td>
                                            <td>{{ $voting["kategorie2"] }}</td>
                                            <td>{{ $voting["kategorie3"] }}</td>
                                            <td>{{ $voting["kategorie4"] }}</td>
                                        </tr>
                                    @endforeach
                                    </tbody>
                                </table>
                            @else
                                <div class="alert alert-info">
                                    Noch keine Votings vorhanden
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
                <div class="tab-pane fade" id="gerichteVoting" role="tabpanel" aria-labelledby="gerichteTab">
                    <h2 class="my-5">Ergebnisse Beste Gerichte</h2>
                    <a href="{{ url("backend/freizeitvoting/".(request()->segment(3)??date("Y"))."/export/gerichte") }}">
                        <span class="btn btn-secondary pt-2">Als Excel downloaden</span>
                    </a>
                    @if(!empty($besteGerichte))
                        @foreach($besteGerichte as $kategorie => $values)
                            <div class="row mt-3">
                                <div class="col mb-3">
                                    <div class="card mb-5">
                                        <div class="card-body px-3 py-4">
                                            <h3>
                                                @switch($kategorie)
                                                    @case(1)
                                                        Wo gibt es den besten Apfelstrudel?
                                                        @break
                                                    @case(2)
                                                        Wo gibt es das knusprigste Schnitzel?
                                                            @break
                                                    @case(3)
                                                        Wo gibt es das saftigste Cordon Bleu?
                                                            @break
                                                    @case(4)
                                                        Wo gibt es den zartesten Zwiebelrostbraten?
                                                            @break
                                                    @case(5)
                                                        Wo gibt es das gschmackigste Gulasch?
                                                            @break
                                                @endswitch
                                            </h3>

                                            <table class="table table-striped table-vertical-middle table-bordered"
                                                   datatable="true"
                                                   data-paging="false"
                                                   data-order="[[1, &quot;desc&quot; ]]">
                                                <thead class="table-dark ">
                                                <tr>
                                                    <th data-orderable="false">Gasthaus</th>
                                                    <th data-orderable="true">Stimmen</th>
                                                </tr>
                                                </thead>
                                                <tbody>
                                                @if(!empty($values))
                                                    @foreach($values as $value)
                                                        <tr>
                                                            <td>{{ $value["name"] }}</td>
                                                            <td>{{ $value["anzahl"] }}</td>
                                                        </tr>
                                                    @endforeach
                                                @endif
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    @else
                        <div class="alert alert-info">
                            Noch keine Votings vorhanden
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection
