@extends('backend.master')
@section('content')
    <div class="row mb-5">

        <div class="col-12 ">
            <div class="py-3 d-flex justify-content-between">
                <h2>Monitor Verwaltung</h2>
            </div>
        </div>

        @if( AdminUser::hasPermission("monitor-kurier") && AdminUser::hasPermission("monitor-krone"))
            <div class="col-12 mb-3">
                <div class="d-flex justify-content-end  ">

                    @foreach($sub_directories as $dir)

                        <div class="ms-2">
                            <a href="{{$dir['href']}}"
                               class="btn mb-0 {{(Str::lower($dir['name']) == $medium) ? 'btn-brown btn-active text-white' : 'btn-brown'}}">
                        <span>
                            {{$dir['name']}}
                        </span>
                            </a>
                        </div>
                    @endforeach
                </div>
            </div>
        @endif

        <div class="col-12 mb-3">
            <div class="row">

                <div class="col-12 mb-3">
                    <div class="card w-100">


                        <div id="preview">

                        </div>



                        <form action="{{ url()->current().'/create' }}" method="post" class="card-body" enctype="multipart/form-data">

                            {{ csrf_field() }}
                            <input type="hidden" value="{{$medium}}" name="medium">

                            <div class="mb-3">
                                <label for="formFile" class="form-label">Bild
                                    <span id="allowed-sizes-span">
                                        @foreach($allowedImageSizes as $current)
                                            {!! (!$loop->first) ? '/' : '' !!}
                                            ({!! $current['w'] !!} x {!! $current['h'] !!})
                                        @endforeach
                                    </span>
                                </label>
                                <input class="form-control" type="file" id="image-input" name="uploadedImage" accept=".jpg,.jpeg,.png">
                            </div>


                            <div class="mb-3">
                                <label class="w-100" for="from">From</label>
                                <input class="form-control form-control-sm" id="from" type="date" value="{{now()->format('Y-m-d')}}" name="from">
                            </div>
                            <div class="mb-3">
                                <label class="w-100" for="to">To</label>
                                <input class="form-control form-control-sm" id="to" type="date" value="{{now()->format('Y-m-d')}}" name="to">
                            </div>
                            <div class="mb-3">
                                <label class="w-100" for="new-image-name">Name</label>
                                <input class="form-control form-control-sm" id="new-image-name" type="text" name="name" value="" placeholder="name...">
                            </div>
                            <div>
                                <button type="submit" class="w-100 btn btn-brown " id="btn-create">SPEICHERN</button>
                            </div>

                        </form>
                    </div>
                </div>


                @foreach($elements as $index => $el)

                    <div class="col-4 mb-3">
                        <div class="card w-100 h-100  ">

                            <img src="{{$el['image']}}" class="card-img-top" alt="...">

                            <form action="{{ url()->current().'/edit' }}" method="post" class="card-body d-flex justify-content-end flex-column">
                                {{ csrf_field() }}
                                <input type="hidden" value="{{$el['storagePath']}}" name="storagePath">

                                <div class="mb-3">
                                    <label class="w-100" for="from">From</label>
                                    <input class="form-control form-control-sm" id="from" type="date" value="{{$el['from_formatted']}}" name="from">
                                </div>
                                <div class="mb-3">
                                    <label class="w-100" for="to">To</label>
                                    <input class="form-control form-control-sm" id="to" type="date" value="{{$el['to_formatted']}}" name="to">
                                </div>
                                <div class="mb-3">
                                    <label class="w-100" for="name">Name</label>
                                    <input class="form-control form-control-sm" id="name" type="text" name="name" value="{{$el['name']}}">
                                </div>
                                <div class="d-flex">
                                    <button type="button" class="btn btn-brown me-1 " data-bs-toggle="modal" data-bs-target="#image-delete-modal-{{$index}}">
                                        <i class="fad fa-trash "></i>
                                    </button>
                                    <button type="submit" class="w-100 btn btn-brown ">SPEICHERN</button>
                                </div>

                            </form>
                        </div>
                    </div>


                    <div class="modal fade" id="image-delete-modal-{{$index}}" tabindex="-1">
                        <div class="modal-dialog modal-dialog-centered">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h1 class="modal-title fs-5">Möchten Sie dieses Bild wirklich löschen?</h1>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <img src="{{$el['image']}}" class="w-100" alt="...">
                                </div>
                                <div class="modal-footer">

                                    <form action="{{ url()->current().'/delete' }}" method="post" class="w-25 ">
                                        {{ csrf_field() }}
                                        <input type="hidden" value="{{$el['storagePath']}}" name="storagePath">
                                        <button type="submit" class="w-100 btn btn-red"> JA <i class="fad fa-trash "></i></button>
                                    </form>

                                    <button type="button" class="w-25 btn btn-brown" data-bs-dismiss="modal">NEIN <i class="fas fa-undo-alt"></i></button>

                                </div>
                            </div>
                        </div>
                    </div>

                @endforeach
            </div>


        </div>

    </div>
    <script>


        const allowedImageSizes = JSON.parse('{!! json_encode($allowedImageSizes ) !!}');

        const btnCreate = document.getElementById('btn-create')

        const preview = document.getElementById('preview');

        const imageInput = document.getElementById('image-input');
        imageInput.addEventListener('change', (event) => {

            if(event?.target?.files?.length){
                getBase64(event.target.files[0]).then(base64 => {

                    const img = new Image();

                    img.src = `${base64}`;

                    img.onload = () => {

                        const imageSize = {
                            w : img.naturalWidth,
                            h : img.naturalHeight
                        }

                        if(!allowedImageSizes.some(x => x.w === imageSize.w && x.h === imageSize.h)){

                            preview.innerHTML = `<div class="p-3 pb-0"><div class="alert alert-danger">
                                                    Falsche Bildgröße! <br> Akzeptierte Bildgrößen:  ${document.getElementById('allowed-sizes-span')?.innerHTML}
                                                 </div></div>`;
                            btnCreate.classList.add('disabled')
                        }else{
                            btnCreate.classList.remove('disabled')
                            preview.innerHTML = `<img src="${base64}" class="card-img-top" alt="...">`
                        }
                    }
                })
            }

        })


        function getBase64(file){
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.readAsDataURL(file);
                reader.onload = () => resolve(reader.result);
                reader.onerror = error => reject(error);
            });
        }

    </script>
@endsection

