@extends('backend.master')

@section('content')
{{--	<div class="card">
		<div class="card-body">--}}
			<div class="row my-3">
				<div class="col-12">
					<h2 class="card-title">Webservice Aktionen</h2>
				</div>
				<div class="col-12 mt-4">
					<form action="{{ url("backend/logs/wsaction") }}" method="post">
						{{ csrf_field() }}
						<div class="row mb-3">
							<div class="col-12 col-md-4">
								<div class="input-group mb-3">
									<input type="text" class="form-control  " id="search" name="search" {{ (!empty($search)) ?  "value=$search"  :  "placeholder=Suche"  }} >
									<div class="input-group-append">
										<button class="btn btn-secondary ">
											<span class="fas fa-search "></span>
										</button>
									</div>
								</div>
							</div>
						</div>
					</form>
					<table class="table table-striped table-vertical-middle table-result table-bordered">
						<thead  class="table-dark">
						<tr>
							<th>#</th>
							<th>Transaktions-ID</th>
							<th>Aktion</th>
							<th>User Info</th>
							<th>Datum</th>
							<th>&nbsp;</th>
						</tr>
						</thead>
						<tbody>
						@foreach($messages as $tmp)
							<tr>
								<td>{{ $tmp->id }}</td>
								<td>{{ $tmp->transaktionsid }}</td>
								<td>{{ $tmp->ws_action }}</td>
								<td>
									<small>{{ $tmp->ip }} / {{ $tmp->platform }} / {{ $tmp->browser }} </small>
								</td>
								<td>{{ $tmp->created_at->format('d.m.Y H:i:s')}}</td>
								<td>
									<span class="fas fa-chevron-down pointer" data-bs-toggle="collapse" data-bs-target="#collapse_{{$tmp->id}}"></span>
								</td>
							</tr>
							<tr class="collapse" id="collapse_{{$tmp->id}}">
								<td colspan="10"><b>Request</b>
									<pre>{!! str_replace([',"','{','}'], [',<br>"','{<br>','<br>}'], print_r(unserialize(base64_decode($tmp->ws_body)),1))  !!}</pre>
									<hr>
									<b>Response</b>
									<pre>{!! $tmp->ws_result !!}</pre>
								</td>
							</tr>
						@endforeach
						</tbody>
					</table>
					<nav>
                        {{ $messages->links('pagination::bootstrap-4') }}
					</nav>
				</div>
			</div>
{{--		</div>
	</div>--}}
@endsection
