@extends('backend.master')

@section('content')
	<div class="card">
		<div class="card-body">
			<div class="row mb-4">
				<div class="col-12">
					<h2 class="card-title">Impressum bearbeiten</h2>
				</div>
			</div>
			<form action="{{ url("backend/imprints/".$imprint->medium) }}" id="imprintForm" name="imprintForm" method="post">
				{{ csrf_field() }}
				{{ method_field('put') }}
				<div class="row">
					<div class="col-12 col-md-6">
						<div class="form-group mb-3">
							<label for="medium">Medium <small>(nicht veränderbar)</small></label>
							<input type="text" id="medium" class="form-control" name="medium" value="{{ old('medium', $imprint->medium) }}" disabled>
						</div>
					</div>
					<div class="col-12 col-md-6">
						<div class="form-group mb-3">
							<label for="title">Titel *</label>
							<input type="text" id="title" class="form-control" name="title" value="{{ old('title', $imprint->title) }}">
							{!!  ($errors->has("title")) ? "<small class='form-text text-danger'><span class='fas fa-exclamation-circle'></span> ".join('<br/>', $errors->get('title'))."</small>" : "" !!}
						</div>
					</div>

					<div class="col-12">
						<div class="form-group mb-3">
							<label for="text">Text</label>
							<textarea
									ckeditor="true"
									ckeditor_height="400"
									ckeditor_removeButtons="{{env('CKEDITOR_ELEMENTS_MIDI')}}"
									id="text" class="form-control mb-3 " name="text">{{ old('text', $imprint->text) }}
							</textarea>
							{!!  ($errors->has("content")) ? "<small class='form-text text-danger'><span class='fas fa-exclamation-circle'></span> ".join('<br/>', $errors->get('content'))."</small>" : "" !!}
						</div>
					</div>
				</div>
				<div class="row">
					<div class="col-12">
						<hr>
					</div>
					<div class="col-12 col-md-6">
						<a href="{{ url("backend/imprints") }}">
							<span class="btn btn-secondary">
								 <span class="fas fa-angle-double-left "></span> Zurück
							</span>
						</a>
					</div>
					<div class="col-12 col-md-6 text-end">
						<button type="button" submit="true" loadicon="true" class="btn btn-success">
							<span class="fas fa-save"></span>
							Speichern
						</button>
					</div>
				</div>
			</form>
		</div>
	</div>
@endsection
