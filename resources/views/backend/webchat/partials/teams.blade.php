
@php
    $textAreaLabel = "E-Mail-Text ( Die Variable <b>{{KUNDE}}</b> wird die Kundeninformationen anzeigen.  )";
    @endphp


<div class="border p-3 mb-5">
    <h4 class="border-bottom pb-3"> Weiterleitung an andere Teams  </h4>



@foreach($emailForwardTeams as $team)

    <!--DELETE Modal -->
    <div class="modal fade"  tabindex="-1" id="emailForwardTeams-{{$team->id}}-delete" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" >Löschen</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">{{$team->name}}</div>
                <div class="modal-footer">

                    <form   method="post" action="{{url('backend/webchat/forward-team-email/'.$team->id .'/delete')}}">
                        {{ csrf_field() }}
                        @method('DELETE')
                        <button type="submit" class="btn btn-danger">LÖSCHEN</button>
                    </form>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ABBRECHEN</button>
                </div>
            </div>
        </div>
    </div>

        <div class="accordion  mb-3 " id="forward-team-email-accordion-{{$team->id}}">
            <div class="accordion-item">
                <h2 class="accordion-header ">
                    <button class="accordion-button collapsed "
                            type="button" data-bs-toggle="collapse"
                            data-bs-target="#forward-team-email-text-collapse-{{$team->id}}" >
                        {{$team->name}}
                    </button>
                </h2>
                <div id="forward-team-email-text-collapse-{{$team->id}}" class="accordion-collapse collapse " data-bs-parent="#forward-team-email-accordion-{{$team->id}}">
                    <div class="accordion-body ">

                        <form class="border p-3" method="post" action="{{url('backend/webchat/forward-team-email/'.$team->id)}}">
                            {{ csrf_field() }}



                            <div class="my-3 ">
                                <label for="title-{{$team->id}}" class="form-label">Name</label>
                                <input name="name" type="text" class="form-control" id="title-{{$team->id}}" placeholder="Der Titel"
                                       value="{{$team->name}}">
                            </div>

                            <div class="my-3 ">
                                <label for="email-fte-{{$team->id}}" class="form-label">E-Mail</label>
                                <input name="email" type="email" class="form-control" id="email-fte-{{$team->id}}"
                                       value="{{$team->email}}">
                            </div>

                            <div class="my-3 ">
                                <label for="subject-{{$team->id}}" class="form-label">Betreff</label>
                                <input name="subject" type="text" class="form-control" id="subject-{{$team->id}}" placeholder="Betreff"
                                       value="{{$team->subject}}">
                            </div>


                            <div class="my-3 ">
                                <label for="body-{{$team->id}}" class="form-label">{!! $textAreaLabel!!} </label>
                                <textarea rows="7" name="body" type="text" class="form-control" id="body-{{$team->id}}" placeholder=""
                                          >{{$team->body}}</textarea>
                            </div>

                            <div class="text-end">
                                <button type="button" class="btn btn-outline-danger me-3" data-bs-toggle="modal" data-bs-target="#emailForwardTeams-{{$team->id}}-delete">
                                    <i class="fad fa-trash"></i> LÖSCHEN
                                </button>
                                <button type="submit" class="btn btn-primary">SPEICHERN</button>
                            </div>
                        </form>


                    </div>
                </div>
            </div>

        </div>

    @endforeach

<div class="accordion  mb-3 " id="forward-team-email-accordion-fte-new">
    <div class="accordion-item">
        <h2 class="accordion-header ">
            <button class="accordion-button collapsed "
                    type="button" data-bs-toggle="collapse"
                    data-bs-target="#forward-team-email-text-collapse-fte-new" >
                Neu
            </button>
        </h2>
        <div id="forward-team-email-text-collapse-fte-new" class="accordion-collapse collapse " data-bs-parent="#forward-team-email-accordion-fte-new">
            <div class="accordion-body ">

                <form class="border p-3" method="post" action="{{url('backend/webchat/forward-team-email')}}">
                    {{ csrf_field() }}



                    <div class="my-3 ">
                        <label for="title-fte-new" class="form-label">Name</label>
                        <input name="name" type="text" class="form-control" id="title-fte-new"
                               value="">
                    </div>


                    <div class="my-3 ">
                        <label for="email-fte-new" class="form-label">E-Mail</label>
                        <input name="email" type="text" class="form-control" id="email-fte-new"
                               value="">
                    </div>

                    <div class="my-3 ">
                        <label for="subject-fte-new" class="form-label">Betreff</label>
                        <input name="subject" type="text" class="form-control" id="subject-fte-new"
                               value="">
                    </div>


                    <div class="my-3 ">
                        <label for="body-fte-new" class="form-label">{!! $textAreaLabel!!}</label>
                        <textarea rows="7" name="body" type="text" class="form-control" id="body-fte-new"
                        ></textarea>
                    </div>

                    <div class="text-end">

                        <button type="submit" class="btn btn-primary">SPEICHERN</button>
                    </div>
                </form>


            </div>
        </div>
    </div>

</div>

</div>
