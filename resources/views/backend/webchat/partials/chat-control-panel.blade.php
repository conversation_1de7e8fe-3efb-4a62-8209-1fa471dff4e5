<form action="{{ url("backend/webchat") }}" method="post" class="border p-3 mb-5" id="configurationForm">

    <div class="d-flex justify-content-between border-bottom">
        <h4 class=" pb-3">Cha<PERSON><PERSON><PERSON><PERSON><PERSON></h4>
        <div class="d-flex">
            @foreach($infos['specific'] as $key => $value)
                <div>
                    <div class="position-relative bg-primary text-white me-3 px-3 fs-5 rounded-6">
                        {{$key}}
                        <span
                            class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                {{$value }}
                            </span>
                    </div>
                </div>

            @endforeach
        </div>

    </div>


    {{ csrf_field() }}

    @foreach($settings as $set)
        <div class="pb-3">


            <table class="table caption-top border">

                <caption class="text-dark">
                    {{$set->desc}}
                </caption>

                <tbody>

                <tr>
                    <td colspan="1000">
                        <h3 class="m-0 d-flex justify-content-between">
                                <span>
                                    Chat <PERSON>
                                </span>
                            <small class="fw-normal  badge badge-warning d-none" id="not-saved-info">
                                <i class="fas fa-save"></i> Nicht gespeichert !
                            </small>

                        </h3>
                    </td>
                </tr>


                <tr>
                    <th>
                        Name
                    </th>
                    <th>
                        Beschreibung
                    </th>
                    <th>
                        STATUS
                    </th>
                </tr>


                <tr>
                    <td class="w-25">
                        Chat
                    </td>
                    <td>
                        Hauptschalter
                    </td>
                    <td style="width: 40%">
{{--                        <div class="border py-2 px-3 mb-2">
                            <div class="form-check">
                                <input value="active" class="form-check-input" type="radio"
                                       name="set[{{$chat->id}}]"
                                       id="always-on-{{$chat->id}}" {!! ($chat->active) ? 'checked' : '' !!}/>
                                <label class="form-check-label" style="padding-top: 1px;"
                                       for="always-on-{{$chat->id}}"> Immer Aktiv</label><br>
                                <small class="fw-bold"> Öffnungszeiten werden ignoriert</small>
                            </div>
                        </div>--}}
                        <div class="border py-2 px-3 mb-2">
                            <div class="form-check">
                                <input

                                    value="inactive"
                                    {!! ($chat->inactive_mode) ? 'checked' : '' !!}

                                    class="form-check-input" type="radio" name="set[{{$chat->id}}]"
                                    id="always-off-{{$chat->id}}"/>
                                <label class="form-check-label" style="padding-top: 1px;"
                                       for="always-off-{{$chat->id}}"> Inaktiv </label>
                            </div>
                        </div>


                        <div class="border py-2 px-3 mb-2">
                            <div class="form-check">
                                <input

                                    value="inactive_auto_tomorrow"
                                    {!! ($chat->inactive_auto_tomorrow) ? 'checked' : '' !!}

                                    class="form-check-input" type="radio" name="set[{{$chat->id}}]"
                                    id="auto-tomorrow-{{$chat->id}}"/>
                                <label class="form-check-label" style="padding-top: 1px;"
                                       for="auto-tomorrow-{{$chat->id}}"> Inaktiv bis
                                    Morgen (Automatisch)</label>

                            </div>
                        </div>


                        <div class="border py-2 px-3">
                            <div class="form-check">
                                <input value="auto_mode" class="form-check-input" type="radio"
                                       name="set[{{$chat->id}}]"
                                       id="nine-to-five-{{$chat->id}}" {!! ($chat->auto_mode) ? 'checked' : '' !!}/>
                                <label class="form-check-label" style="padding-top: 1px;"
                                       for="nine-to-five-{{$chat->id}}"> Automatisch </label> <br>
                                <small class="fw-bold">Je nach Öffnungszeiten aktiv / inaktiv</small>

                            </div>
                        </div>

                    </td>
                </tr>

                <tr>
                    <td colspan="1000">
                        <h3 class="m-0">
                            Einzelne mandanten
                        </h3>
                    </td>
                </tr>


                <tr>
                    <th>
                        Name
                    </th>
                    <th>
                        Beschreibung
                    </th>
                    <th>
                        STATUS
                    </th>
                </tr>

                @foreach($set->configs as $index => $m)
                    <tr>
                        <td class="w-25">

                            {{strtoupper($m->name)}}

                        </td>
                        <td>
                            {{($m->description) ? : ''}}
                        </td>
                        <td>


                            <div class="border py-2 px-3 mb-2">
                                <div class="form-check">
                                    <input value="active" class="form-check-input" type="radio"
                                           name="set[{{$m->id}}]"
                                           id="m-active-{{$m->id}}" {!! ($m->active) ? 'checked' : '' !!}/>
                                    <label class="form-check-label" style="padding-top: 1px;"
                                           for="m-active-{{$m->id}}"> Aktiv </label>
                                </div>
                            </div>

                            <div class="border py-2 px-3 mb-2">
                                <div class="form-check">
                                    <input value="inactive" class="form-check-input " type="radio"
                                           name="set[{{$m->id}}]"
                                           id="m-inactive-{{$m->id}}" {!! (!$m->active) ? 'checked' : '' !!}/>
                                    <label class="form-check-label" style="padding-top: 1px;"
                                           for="m-inactive-{{$m->id}}"> Inaktiv </label>
                                </div>
                            </div>


                        </td>
                    </tr>
                @endforeach
                </tbody>
            </table>
        </div>
    @endforeach

    <div class="text-end">
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#save-prompt-dialog">
            SPEICHERN
        </button>
    </div>


    <div class="modal fade" id="save-prompt-dialog" data-bs-backdrop="static" data-bs-keyboard="false"
         tabindex="-1" aria-labelledby="staticBackdropLabel"
         aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="staticBackdropLabel">Speichern</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body py-4">

                    Wollen Sie die Änderungen speichern?

                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-lite" data-bs-dismiss="modal">Abbrechen</button>
                    <button type="submit" class="btn btn-primary">Speichern</button>
                </div>
            </div>
        </div>
    </div>


</form>
