<div class="border p-3 mb-5">
    <h4 class="border-bottom pb-3"> Gründe für den Chat </h4>


    <div class="d-flex flex-column">
        @foreach($reasons as $parent)

            @php
                $s = "background: $parent->background_color;color:$parent->color;"
            @endphp
            @component('backend.webchat.partials._accordion', ['accordion_id' => $parent->id, 'accordion_title' => $parent->name])
                @slot('accordion_body')

                    <form action="{{ url("backend/webchat/reasons/" . $parent->id) }}" method="post">
                        {{ csrf_field() }}
                        @method('POST')
                        <b>Hauptkategorie Name</b>
                        <div class="w-100 d-flex mb-3">
                            <label class=" fw-bold">
                                <input type="text" class="form-control" value="{{$parent->name}}" name="main_category_name">
                            </label>

                            <div class="form-check mt-1 ms-1">
                                <input class="form-check-input" type="checkbox" value="delete"
                                       id="check-parent-{{$parent->id}}" name="delete"/>
                                <label class="form-check-label" for="check-parent-{{$parent->id}}">Löschen</label>
                                <span>(alle Unterkategorien werden ebenfalls gelöscht!)</span>
                            </div>
                        </div>

                        <div class="d-flex">
                            <div class="square mb-3 me-1">
                                <label for="bg-color-{{$parent->id}}" class="d-block">Hintergrund Farbe</label>
                                <input data-target="{{$parent->id}}" data-attribute="backgroundColor"
                                       id="bg-color-{{$parent->id}}" class="form-control" type="text" data-coloris
                                       name="backgroundColor" value="{{$parent->background_color}}">
                            </div>
                            <div class="square mb-3 me-1">
                                <label for="color-{{$parent->id}}" class="d-block">Text Farbe</label>
                                <input data-target="{{$parent->id}}" data-attribute="color" id="color-{{$parent->id}}"
                                       class="form-control" type="text" data-coloris name="color"
                                       value="{{$parent->color}}">
                            </div>
                        </div>
                        <div class="fw-normal " style="font-family:  sans-serif">

                            <div data-badge-parent="{{$parent->id}}" class="badge mb-3 ms-1 fs-6"
                                 style="{{$s}}">{{$parent->name}}</div>
                            <br>
                            @foreach($parent->children as $child)
                                <div data-badge-parent="{{$parent->id}}" class="badge mb-3 ms-1 fs-6"
                                     style="{{$s}}">{{$child->name}}</div>
                            @endforeach
                        </div>


                        <hr>

                        <b>Unterkategorien</b>

                        @foreach($parent->children as $child)
                            <div class="w-100 d-flex">
                                <label class="d-block ">
                                    <input name="sub_categories[{!! $child->id !!}]" type="text" class="form-control"
                                           value="{{$child->name}}">
                                </label>

                                <div class="form-check mt-1 ms-1">
                                    <input class="form-check-input" type="checkbox" value="delete"
                                           id="check-{{$parent->id}}--{{$child->id}}"
                                           name="delete_sub_categories[{!! $child->id !!}]"/>
                                    <label class="form-check-label"
                                           for="check-{{$parent->id}}--{{$child->id}}">Löschen</label>
                                </div>
                            </div>

                            <hr>

                        @endforeach

                        <b>Neue Unterkategorien</b> <span>(Eingeben der Kategorien durch Semikolon getrennt)</span>

                        <label class="d-block ">
                            <input name="new_sub_categories" type="text" class="form-control"
                                   value="" placeholder="Kat 1; Kat 2; Kat 3...">
                        </label>

                        <div class="mt-3">
                            @include('backend.webchat.partials._modal-submit', ['modal_id' => 'save-cat-'.$parent->id])
                        </div>
                    </form>

                @endslot
            @endcomponent

        @endforeach



            @component('backend.webchat.partials._accordion',
['accordion_id' => 'new-category', 'accordion_title' => '<b>+ Neue Hauptkategorie</b>'])
                @slot('accordion_body')
                    <form action="{{ url("backend/webchat/reasons/") }}" method="post">
                        {{ csrf_field() }}
                        @method('POST')

                        <div class="w-100 d-flex mb-3">
                            <label class=" fw-bold">
                                Hauptkategorie Name
                                <input type="text" class="form-control" value="" name="main_category_name">
                            </label>
                        </div>

                        @php
                            $defaultBgColor = '#0091ea';
                            $defaultColor = '#ffffff';
                        @endphp
                        <div class="d-flex">
                            <div class="square mb-3 me-1">
                                <label for="bg-color-new-category" class="d-block">Hintergrund Farbe</label>
                                <input data-target="new-category" data-attribute="backgroundColor"
                                       id="bg-color-new-category" class="form-control" type="text" data-coloris
                                       name="backgroundColor" value="{{$defaultBgColor}}">
                            </div>
                            <div class="square mb-3 me-1">
                                <label for="color-new-category" class="d-block">Text Farbe</label>
                                <input data-target="new-category" data-attribute="color" id="color-new-category"
                                       class="form-control" type="text" data-coloris name="color"
                                       value="{{$defaultColor}}">
                            </div>
                        </div>
                        <div class="fw-normal " style="font-family:  sans-serif">

                            <div data-badge-parent="new-category" class="badge mb-3 ms-1 fs-6"
                                 style="background-color: {{$defaultBgColor}}; color: {{$defaultColor}}">Neue Hauptkategorie</div>
                            <br>
                        </div>


                        <hr>

                        <b>Neue Unterkategorien</b> <span>(Eingeben der Kategorien durch Semikolon getrennt)</span>

                        <label class="d-block ">
                            <input name="new_sub_categories" type="text" class="form-control"
                                   value="" placeholder="Kat 1; Kat 2; Kat 3...">
                        </label>

                        <div class="mt-3">
                            @include('backend.webchat.partials._modal-submit', ['modal_id' => 'save-cat-new-category'])
                        </div>
                    </form>
                @endslot
            @endcomponent

    </div>





</div>

<style>
    .form-check-input[type=checkbox]:checked {
        background-image: none;
        background-color: #dc2535 !important;
    }

    .form-check-input:checked {
        border-color: #dc2535 !important;
    }

    .form-check-input:checked {
        background-color: #dc2535 !important;
    }
</style>

<link rel="stylesheet" href="{{url('inc/js/coloris/dist/coloris.min.css')}}"/>
<script src="{{url('inc/js/coloris/dist/coloris.min.js')}}"></script>


<script>
    Coloris({
        swatches: [
            '#56B9D2',
            '#D0A078',
            '#ff5231',
            '#56C3B1'
        ],
    })
</script>

<style>

    .square .clr-field button {
        width: 22px;
        height: 22px;
        right: 5px;
        left: auto;
        border-radius: 5px;
    }

    .clr-picker {
        -webkit-box-shadow: -5px 10px 45px -12px rgba(0, 0, 0, 0.75) !important;
        -moz-box-shadow: -5px 10px 45px -12px rgba(0, 0, 0, 0.75) !important;
        box-shadow: -5px 10px 45px -12px rgba(0, 0, 0, 0.75) !important;
    }
</style>


<script>
    /*
        on input change preview
    */

    const colorInputs = [...document.querySelectorAll(`[data-attribute]`)];
    const badgesPerParent = [...document.querySelectorAll(`[data-badge-parent]`)];

    function updateBadgesColor(target) {

        if (!target) {
            console.log('no target')
            return;
        }

        const inputs = colorInputs.filter(x => x.dataset?.target === target)


        badgesPerParent.filter(x => x.dataset.badgeParent === target).forEach(badge => {


            inputs.forEach(input => {

                badge.style[input.dataset.attribute] = input.value;
            })

        })

        console.log(inputs)
    }

    colorInputs.forEach(x => x.addEventListener('change', (event) => updateBadgesColor(event.target.dataset.target)))
    /*   badgesPerParent.forEach(x => x.addEventListener('change', (event) =>updateBadgesColor(event)))*/
</script>
