@php
    $today = \Carbon\Carbon::today()->dayOfWeek ;
@endphp

@foreach($sortedOpeningTimes as $media => $weekDays)

    <div class="row mb-5">
        <div class="col-12">
            <h2 class="m-0">
                {{Illuminate\Support\Str::title($media)}}
            </h2>
        </div>
        <div class="col-12">

                <form class="row row-cols-7" action="{{url('backend/webchat/opening-times/' . $media)}}" method="POST">
                    @csrf
                    @method('POST')

                    @foreach($weekDays as $day)

                        @php
                        $isToday = ($today === $day->week_day);
                        @endphp

                        <div class="col pt-3 {{($isToday) ? ' border' : ''}}">
                            <span class="{{($isToday) ? 'fw-bold' : ''}}">
                                 {{$day->week_day_de}}
                            </span>

                            <hr>
                            <div class="mb-3">
                                <label for="meeting-time" class="form-label" >Öffnungszeit:</label>
                                <input  type="time" id="meeting-time" name="days[{{$day->week_day}}][from]" class="form-control" value="{{$day->from}}">
                            </div>
                            <div class="mb-3">
                                <label for="meeting-time" class="form-label">Schließzeit:</label>
                                <input  type="time" id="meeting-time" name="days[{{$day->week_day}}][to]" class="form-control" value="{{$day->to}}">
                            </div>
                        </div>

                    @endforeach
                    <div class="text-end mt-3">
                        <button type="submit" class="btn btn-success" >Speichern</button>
                    </div>

                </form>



        </div>
    </div>
    @if(!$loop->last)
        <hr class="my-5">
    @endif
@endforeach
