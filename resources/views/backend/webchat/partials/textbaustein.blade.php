<div class="border p-3 mb-5">
    <h4 class="border-bottom pb-3">Textbausteine</h4>


    @foreach($boilerplate_per_mandant as $mandant_name => $boilerplate)

        <h4 class="border-bottom py-3">{{Str::title($mandant_name)}}</h4>

        <div class="sortable-js">


            @foreach($boilerplate as $index => $current )

                <div data-sortable-id="{{$current->id}}">

                    @include('backend.webchat.partials._text-block', [
                      "textBlock" => $current,
                      "unique_id" => $mandant_name.'-'.$index,

                  ])
                </div>

            @endforeach
        </div>

    @endforeach

    <h4 class="border-bottom py-3">Neue Textbausteine erstellen</h4>

    <div class="accordion  " id="text-accordion-create-new">
        <div class="accordion-item">
            <h2 class="accordion-header">
                <button class="accordion-button collapsed"
                        type="button" data-bs-toggle="collapse"
                        data-bs-target="#text-collapse-create-new" aria-controls="text-collapse-create-new">
                    {{--      <b>+</b>--}}

                    <i class="far fa-plus"></i>
                </button>
            </h2>
            <div id="text-collapse-create-new" class="accordion-collapse collapse "
                 data-bs-parent="#text-accordion-create-new">
                <div class="accordion-body">

                    <form class="border p-3" method="post" action="{{$text_boilerplate_action_url}}">
                        {{ csrf_field() }}

                        <div class="my-3">
                            <div>Mandant</div>
                            @foreach($mandanten as $mandant)
                                <label class="btn btn-light border">
                                            <span class="form-check  ">
                                                <input class="form-check-input" type="radio" name="mandant"
                                                       value="{{$mandant}}"   {!! ($loop->first) ? 'checked' : '' !!}  />
                                                <span class="form-check-label "> {{ $mandant }} </span>
                                            </span>
                                </label>
                            @endforeach
                        </div>


                        <div class="my-3">
                            <label for="title-create-new" class="form-label">Titel</label>
                            <input name="title" type="text" class="form-control" id="title-create-new"
                                   placeholder=""
                                   value="">
                        </div>
                        <div class="mb-3">

                            			<textarea
                                            ckeditor="true"
                                            ckeditor_height="400"
                                            ckeditor_removeButtons="{{env('CKEDITOR_ELEMENTS_MIDI')}}"
                                            id="ck-editor-new-content" class="form-control mb-3 "
                                            name="text"

                                        >
							            </textarea>

                        </div>
                        <div class="text-end">
                            <button type="submit" class="btn btn-primary">SPEICHERN</button>
                        </div>
                    </form>


                </div>
            </div>
        </div>

    </div>


</div>
