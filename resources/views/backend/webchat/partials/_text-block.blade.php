<!-- Modal Delete-->
<div class="modal fade" id="modal-delete-{{$unique_id}}" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Löschen</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">Wirklich löschen?</div>
            <div class="modal-footer">


                <form action="{{ $text_boilerplate_delete_url . '/' . $textBlock->id }}" id="" name="" method="post" enctype="multipart/form-data">
                    {{ csrf_field() }}
                    @method('DELETE')
                    <button type="submit" class="btn btn-outline-danger">J<PERSON>, LÖSCHEN</button>
                </form>

                <button type="button" class="btn btn-primary" data-bs-dismiss="modal">NEIN, ABBRECHEN</button>
            </div>
        </div>
    </div>
</div>
{{--Accordion--}}
<div class="accordion  mb-3 " id="text-accordion-{{$unique_id}}">
    <div class="accordion-item">
        <h2 class="accordion-header ">
            <button class="accordion-button collapsed "
                    type="button" data-bs-toggle="collapse"
                    data-bs-target="#text-collapse-{{$unique_id}}" >
                <div class="me-3 handle">
                    <i class="fas fa-arrows-alt"></i>
                </div>
                  {{$textBlock->title}}
            </button>
        </h2>
        <div id="text-collapse-{{$unique_id}}" class="accordion-collapse collapse " data-bs-parent="#text-accordion-{{$unique_id}}">
            <div class="accordion-body ">

                <form class="border p-3" method="post" action="{{$text_boilerplate_action_url.'/'.$textBlock->id}}">
                    {{ csrf_field() }}

                    <div class="my-3">
                        <div >Mandant</div>

                        @foreach($mandanten as $mandant)
                            <label class="btn btn-light border ">
                                            <span class="form-check  ">
                                                <input class="form-check-input" type="radio" name="mandant"  value="{{$mandant}}"
                                                       {!! ($mandant == $textBlock->mandant) ? 'checked' : '' !!}  />
                                                <span class="form-check-label "> {{ $mandant }}     </span>
                                            </span>
                            </label>
                        @endforeach
                    </div>


                    <div class="my-3 ">
                        <label for="title-{{$unique_id}}" class="form-label">Titel</label>
                        <input name="title" type="text" class="form-control" id="title-{{$unique_id}}" placeholder="Der Titel"
                               value="{{$textBlock->title}}">
                    </div>
                    <div class="mb-3">


                        	<textarea
                                ckeditor="true"
                                ckeditor_height="400"
                                ckeditor_removeButtons="{{env('CKEDITOR_ELEMENTS_MIDI')}}"

                                class="form-control mb-3 "
                                name="text"
                                id="ck-editor-{{$mandant_name.'-'.$index}}"

                            >

                                {!! $textBlock->text !!}
							            </textarea>



                    </div>
                    <div class="text-end">
                        <button type="button" class="btn btn-outline-danger me-3" data-bs-toggle="modal" data-bs-target="#modal-delete-{{$unique_id}}">
                            <i class="fad fa-trash"></i> LÖSCHEN
                        </button>
                        <button type="submit" class="btn btn-primary">SPEICHERN</button>
                    </div>
                </form>


            </div>
        </div>
    </div>

</div>
