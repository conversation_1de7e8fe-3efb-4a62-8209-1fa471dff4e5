@extends('backend.master')
@section('content')

    <div class="py-3 ">
        <h2>Webchat Verwaltung</h2>
    </div>


    @php

        $index = (int)(request()->segment(3) ?? 0);

        $includes = config('web-chat.tabs');

        if(!array_key_exists($index,$includes)){
            $index = 0;
        }


        $component = $includes[$index]['component'];

    @endphp


        <!-- Tabs navs -->
    <ul class="nav nav-tabs mb-3" id="ex1" role="tablist">
        @foreach($includes as $loopIndex => $inc)
            <li class="nav-item" role="presentation">
                <a
                    class="nav-link @if($loopIndex == $index) active @endif"
                    href="{{url('backend/webchat/'. (($loopIndex) ?: ''))}}"
                >{{$inc['title']}}</a>
            </li>
        @endforeach
    </ul>


    <div>
        @include('backend.webchat.partials.'.$component )
    </div>




    <script src="{{url('inc/sortableJS/sortableJS.js')}}"></script>

    <script>

        const endpoint = `{{url('backend/webchat/js')}}`;

        [...document.getElementsByClassName('sortable-js')].forEach(el => {

            Sortable.create(el, {
                ghostClass: 'ghost',
                direction: 'vertical',
                handle: '.handle',
                onEnd: () => {
                    /*
                    *  array of datasets [{sort: '0', id: '12'}]
                    */
                    const newOrder = Array.from(el.querySelectorAll(`[data-sortable-id]`))
                        .map((element, index) => {
                            return {
                                id: element.dataset.sortableId,
                                sort: index
                            }
                        })


                    fetch(endpoint,
                        {
                            method: 'POST',
                            headers: {
                                'X-CSRF-TOKEN': "{{ csrf_token() }}",
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({orderArray: newOrder}),

                        }
                    )
                        .then(response => response.json())
                        .then(json => console.log(json))
                        .catch((e) => {
                            // On Error reload the Page
                            console.log(e)
                            //location.reload()
                        });


                }
            })
        })



        /*
        * Form changes
        */


        const inputs = document.getElementById('configurationForm')?.querySelectorAll('input');

        /*
       * Used to be checked against changes
       */
        const startValues = (inputs) ? [...inputs].filter(x => x.checked).map(x => x.value) : [];

        const notSavedInfo = document.getElementById('not-saved-info')

        document.getElementById('configurationForm')?.addEventListener('change', (ev) => {

            if (startValues.length && notSavedInfo) {

                let i = document.getElementById('configurationForm').querySelectorAll('input');

                const newValues = [...i].filter(x => x.checked).map(x => x.value);
                if (JSON.stringify(newValues) === JSON.stringify(startValues)) {
                    notSavedInfo.classList.add('d-none')
                } else {

                    notSavedInfo.classList.remove('d-none')
                }
            }
        })


    </script>

    <style>

        .sortable-js .ghost .accordion-button {
            border: 2px solid #3b71ca !important;
            color: #3b71ca !important;
        }

        .sortable-js .handle {
            cursor: grab;
        }

        [data-bs-original-title="_hidden_"] {
            display: none;
        }


    </style>

@endsection
