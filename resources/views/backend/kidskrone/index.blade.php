@extends('backend.master')

@section('content')

    <div class="row mb-5">
        <div class="col-12">
            <div>
                <h2>Kidskrone Verwaltung</h2>
            </div>
        </div>

        <div class="col-12">
            {{--  <div>
                  <h3>Slideshow</h3>
              </div>--}}

            <div class="col-12 ">
                <div class="py-3 d-flex justify-content-between">

                    <div>
                        <h3>Slideshow</h3>
                    </div>

                    <div>
                        <div class="text-end">
                            <a class="btn btn-brown fs-6" href="{{url('backend/kidskrone/slideshow')}}">Slideshow
                                Bearbeiten</a>
                        </div>
                    </div>

                </div>
            </div>

            <div>
                <div class="row">
                    @foreach($images as $index => $img)

                        <div class="col-lg-3 mb-3">
                            <div class="card shadow">
                                <img src="{{$img->fullPath}}" class="img-fluid" alt="...">
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>

            {{--   <div class="text-end">
                   <a class="btn btn-brown fs-6" href="{{url('backend/kidskrone/slideshow')}}">Slideshow Bearbeiten</a>
               </div>--}}
        </div>

        <div class="col-12 py-3">
            <hr>
        </div>

        <div class="col-12 ">
            <div class="py-3 d-flex justify-content-between">

                <div>
                    <h3>Videos</h3>
                    <span id="grid-change" style="cursor: pointer">Grid <i class="fas fa-th"></i></span>
                </div>

                <div>
                    <a href="{{url()->current().'/create'}}" class="btn btn-brown fs-6">
                        Neues Video Erstellen
                    </a>
                </div>

            </div>
        </div>


        <div class="col-12" style="display:none" id="error">
            <div class="alert alert-danger" id="error-msg">
                Error, please try again later...
            </div>
        </div>







        @foreach($video_collection as $index => $video)

            <div class="col-lg-6  columns mb-4">

                <div class="card mb-3  shadow h-100  "
                >

                    <div class="card-header border-0 bg-white position-absolute right-0 m-0 text-end rounded-0 " >
                        <a href="{{url('backend/kidskrone').'/'.$video->id}}" style="z-index: 123" class="position-relative">
                            <i class="fa fa-cog fs-3 text-dark" aria-hidden="true"></i>
                        </a>

                    </div>




                    @if($video->coverFullPath || $video->image)
                        <div style="max-height: 300px!important;" class="text-center pb-1">
                            <img src="{{$video->coverFullPath ?: $video->image}}" class=" h-100 mw-100" alt="...">
                        </div>
                    @endif
                    <div class="card-body">
                        @if(!$video->isOnVimeo())

                            <div class=" ">
                                <div class="alert alert-primary fw-bold    ">
                                    Video noch nicht hochgeladen.
                                </div>

                            </div>

                        @endif

                        <div class="d-flex justify-content-between">
                            <h5 class="card-title">{{$video->name}}</h5>
                            <div>


                                @if($video->is_playable)
                                    <span class="badge bg-success fw-normal fs-6">{{$video->status}}</span>
                                @else
                                    <span class="badge bg-warning fw-normal fs-6">{{$video->status}}</span>
                                @endif

                            </div>

                        </div>

                        <div class="accordion mb-3" id="accordionText{{$index}}">
                            <div class="accordion-item ">
                                <h2 class="accordion-header  bg-brown1 border-0" id="flush-heading-{{$index}}">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                            data-bs-target="#flush-collapse-{{$index}}" aria-expanded="false"
                                            aria-controls="flush-collapse-{{$index}}">
                                        Text anzeigen
                                    </button>
                                </h2>
                                <div id="flush-collapse-{{$index}}" class="accordion-collapse collapse"
                                     aria-labelledby="flush-heading-{{$index}}"
                                     data-bs-parent="#accordionText{{$index}}">
                                    <div class="accordion-body">
                                        <p class="card-text fst-italic">{{$video->description}}</p>
                                        <p class="card-text">{{$video['long_description']}}</p>
                                        @if($video->isOnVimeo())
                                            <a class="link-danger"
                                               href="https://vimeo.com/manage/videos/{{$video->vimeo_id}}"
                                               target="_blank">Vimeo
                                                (Video {{$video->vimeo_id}})</a>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>


                        <div>
                            <label class="d-block mb-3">
                                    <span
                                        class="d-block me-2 d-flex flex-column justify-content-center ">Kategorie:   </span>
                                <select class="form-select" name="category" onchange="notSaved('{{$video->id}}')"
                                        data-select-input="{{$video->id}}" >

                                    @foreach($categories as $cat)
                                        <option
                                            {!! ($video['category'] == $cat['value'])  ?  'selected' : ''  !!}
                                            value="{{$cat['value']}}">{{$cat['viewValue']}}</option>
                                    @endforeach

                                </select>
                            </label>


                            <label class="d-block  ">
                                    <span
                                        class="d-block me-2 d-flex flex-column justify-content-center ">Saison:  </span>
                                <select class="form-select" name="season"

                                        onchange="notSaved('{{$video->id}}')"
                                        data-select-input="{{$video->id}}">


                                    @foreach($seasons as $_season)
                                        @if($loop->first)
                                            <option
                                                {!! ($video->season === $_season)  ?  'selected' : ''  !!}
                                                value="">Ungeordnet
                                            </option>
                                        @endif
                                        <option
                                            {!! ($_season->value === $video->season)  ?  'selected' : ''  !!}
                                            value="{{$_season->value}}">{{$_season->value}} {{($_season->current) ? '(aktuelle Saison)' : ''}}</option>
                                    @endforeach

                                </select>
                            </label>


                            <div class="justify-content-between mt-3" style="display: none"
                                 data-show-send-button="{{$video->id}}">
                                <span class="d-flex justify-content-center flex-column"><span class="text-danger"><i
                                            class="fas fa-save"></i> Nicht gespeichert</span></span>
                                {{--SUBMIT button--}}

                                <button class="btn btn-brown fs-6" data-submit data-media-id="{{$video->id}}">
                                    Speichern
                                </button>

                            </div>
                            <div class="text-success text-end" data-successfully-saved="{{$video->id}}"
                                 style="display:none">
                                <i class="fas fa-save"></i> Erfolgreich gespeichert
                            </div>


                        </div>

                    </div>

                    <div class="card-footer bg-transparent py-3 d-flex justify-content-end">

                        <div class="d-flex justify-content-center flex-column">

                            Erstellungsdatum: {{ Carbon\Carbon::parse($video->created_at)->format('d.m.y H:i')}}</div>
                    </div>
                </div>
            </div>



        @endforeach


    </div>





    <script>


        const endpointSave = '{{$endpoint}}';


        document.getElementById('grid-change').addEventListener('click', (x) => {
            document.querySelectorAll('.columns').forEach(el => {
                el.classList.toggle('col-lg-6')
                el.classList.toggle('col-lg-4')
            })
        })


        function notSaved(id) {
            document.querySelector(` [data-show-send-button="${id}"]`).style.display = 'flex'
        }


        function onSuccess(successResponse) {
            if (!successResponse?.success) {
                document.getElementById('error').style.display = 'block'
                document.getElementById('error-msg').innerHTML = successResponse.msg
                window.scrollTo(0, 0)
                return
            }
            document.getElementById('error').style.display = 'none'
            const id = successResponse.media_id;

            document.querySelector(` [data-show-send-button="${id}"]`).style.display = 'none'

            const successInfo = document.querySelector(`[data-successfully-saved="${id}"]`);
            successInfo.style.display = 'block'
            setTimeout(() => {
                successInfo.style.display = 'none'
            }, 2000)
        }

        function saveChanges(endpoint, body) {
            fetch(endpoint,
                {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': "{{ csrf_token() }}",
                    },
                    body: body,
                    processData: false,
                    contentType: false
                }
            )
                .then(response => response.json())
                .then(json => onSuccess(json))
                .catch((e) => {
                    // On Error reload the Page
                    console.log('ERROR --> ', e)
                    //location.reload()
                });
        }


        // Save Event
        document.querySelectorAll('[data-submit]').forEach((el) => {
            el.addEventListener('click', () => {
                const mediaId = el.dataset.mediaId


                //    console.log(category, document.querySelectorAll(`[data-select-input="${mediaId}"]`))


                let myFormData = new FormData();

                myFormData.append('media_id', mediaId);

                document.querySelectorAll(`[data-select-input="${mediaId}"]`).forEach(x => {
                    myFormData.append(x.name, x.value ?? null);
                })


                /*                myFormData.append('category', (category) ? category.value : null);*/


                console.log(myFormData)

                saveChanges(endpointSave, myFormData);
            })
        })


    </script>
    <style>
        .accordion-button:not(.collapsed) {
            color: inherit;
            background-color: inherit;
        }

        .accordion-button:focus {
            border: 1px solid rgba(0, 0, 0, .125);
        }

        select > option {
            font-family: "Inconsolata", monospace;
        }

    </style>

@endsection
