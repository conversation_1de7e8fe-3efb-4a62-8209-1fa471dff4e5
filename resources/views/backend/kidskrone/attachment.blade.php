@extends('backend.master')
@section('content')

    <style>
        #buttonName {
            display: none;
        }

        #option-new:checked + label + div > input[type="text"] {
            display: block;
        }

        #option-new:checked + label + div + label {
            display: block;
        }
    </style>

    <form class="mb-3" action="{{url('backend/kidskrone/attachment/' . $attachment->id)}}" id="" name="" method="post"
          enctype="multipart/form-data">
        {{ csrf_field() }}
        @method('POST')

        <div class="border p-3">
            <div class="row ">
                <div class="offset-lg-1 col-lg-10">

                    <div class=" p-3">

                        <h2>Attachment</h2>

                        <div>

                            Button Name:

                            @foreach($buttons as $index =>  $btn)

                                <div class="form-check mb-1 d-flex align-items-center">
                                    <input class="form-check-input" type="radio" name="button_name"
                                           id="option-{{$index}}" value="{{$btn}}" {!! ($attachment->button == $btn) ? 'checked' : '' !!}>
                                    <label class="border border-info rounded px-2 py-1 " style="cursor: pointer" for="option-{{$index}}">
                                        {{$btn}}
                                    </label>
                                </div>

                            @endforeach



                            <div class="form-check mb-1 d-flex align-items-center mb-3">
                                <input class="form-check-input" type="radio" name="button_name" id="option-new" value="new">
                                <label class="border border-info rounded px-2 py-1" for="option-new" style="cursor: pointer">
                                    <i class="fa-solid fa-plus"></i> Neu
                                </label>

                                <div class="ps-2">
                                    <input  value="" type="text" class="form-control" id="buttonName" name="new_button_name" placeholder="Button-Namen eingeben">
                                </div>


                              {{--  <label class="ps-2" id="enter-button-name" for="buttonName">
                                    Enter Button Name
                                </label>--}}
                            </div>

                        </div>

                       {{-- <div class="mb-3">
                            <div class="form-group">
                                <label for="buttonName">Button Name:</label>
                                <input required value="{!!$attachment->button!!} " type="text" class="form-control"
                                       id="buttonName"
                                       name="buttonName"
                                       placeholder="Enter Button Name">
                            </div>
                        </div>
--}}

                        @if($attachment->filePath)
                            <div class=" p-2 fw-bold mb-3 border ">
                                <a href="{{$attachment->fullPath}}" target="_blank"
                                   class="link-success text-center d-block">
                                    <i class="fa-regular fa-file display-1"></i>
                                    <div>
                                        Gespeicherte Datei ansehen
                                    </div>

                                </a>
                            </div>
                        @endif

                        <div class="mb-3">
                            <label for="formFile" class="form-label">File @if($attachment->filePath)
                                    (die bereits gespeicherte Datei wird
                                    ersetzt.)
                                @endif</label>
                            <input name="file" class="form-control" type="file" id="formFile">
                        </div>
{{--{{dd($attachment)}}--}}
                        <div class=" d-flex justify-content-between">
                            <a href="{{url('backend/kidskrone/'.$attachment->kidskrone_video_id)}}" class="btn btn-brown">Zum
                                Video</a>
                            <button type="submit" class="btn btn-brown">Speichern</button>
                        </div>
                    </div>


                </div>
            </div>

        </div>

    </form>

@endsection
