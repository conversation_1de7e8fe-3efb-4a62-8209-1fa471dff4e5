@extends('backend.master')

@section('content')
    <div class="row pb-3">
        <div class="col-12">

            <h2>Slideshow</h2>
            <a class="btn btn-brown mb-3" href="{{url('backend/kidskrone')}}"><i class="fas fa-arrow-alt-left"></i> Zurück</a>
        </div>

        <div class="row sortable-js">


            @foreach($images as $index => $img)


                <div class="col-lg-4 mb-3 sortable-jss" data-sortable-id="{{$img->id}}">
                    <div class="card shadow">
                        <img src="{{$img->fullPath}}" class="img-fluid" alt="...">
                        <div class="card-footer p-0 d-flex justify-content-between">


                            <button type="button" class="btn text-danger" data-bs-toggle="modal" data-bs-target="#imgDeleteModal{{$index}}">
                                LÖSCHEN <i class="fad fa-trash"></i>
                            </button>

                            <div class="handle p-3 text-dark" >
                                VERSCHIEBEN <i class="far fa-arrows"></i>
                            </div>
                        </div>
                    </div>
                    <!-- Modal -->
                    <div class="modal fade" id="imgDeleteModal{{$index}}" tabindex="-1" aria-labelledby="imgDeleteModal{{$index}}Label" aria-hidden="true">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="imgDeleteModal{{$index}}Label">Wirlklisch löschen?</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body"><img src="{{$img->fullPath}}" class="img-fluid" alt="..."></div>
                                <div class="modal-footer d-flex justify-content-between">

                                    <form action="{{ $img->deleteUrl }}" id="" name="" method="post" enctype="multipart/form-data">
                                        {{ csrf_field() }}
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-danger fs-6">LÖSCHEN <i class="fad fa-trash"></i></button>
                                    </form>
                                    <button type="button" class="btn btn-brown fs-6" data-bs-dismiss="modal">ABBRECHEN</button>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>




            @endforeach
        </div>

        <div class="col-12">
            <form class="card shadow" action="{{ $save_url }}" method="post" enctype="multipart/form-data">
                {{ csrf_field() }}
                <div class="card-header fs-5">
                    Neues Bild
                </div>
                <div class="card-body text-end">


                    <div class="w-100">
                        {{-- Image container --}}
                        <div id="cropped-image-container" class="position-relative"></div>
                        {{--Input for backend--}}
                        <input type="hidden" name="image_base64" id="cropped-image-form-input">
                    </div>

                    <div class="position-relative text-center pt-3">
                        <label for="image-file-input" class="d-flex justify-content-between flex-column" style="cursor: pointer" id="image-uploader-button">
                            <i class="fas fa-file-upload fa-3x mb-1"></i>
                            <span>Bild auswählen / austauschen</span>
                            <input type="file"
                                   id="image-file-input" accept="image/*" class="invisible position-absolute">
                        </label>
                    </div>


                </div>
                <div class="card-footer text-end p-2">
                    <button type="submit" class="btn btn-brown">Speichern</button>
                </div>
            </form>
        </div>


        {{--CroperJs Container--}}
        <div id="cropper-js-modal" class="d-none">
            <div class="position-fixed d-flex justify-content-center flex-column"
                 style="bottom:0; top: 0; left: 0 ; right: 0; z-index:99999999; background:rgba(0,0,0,0.76)">
                <div class="row" style="bottom: 5rem;">
                    <div class="col-md-8 offset-md-2 p-3 position-relative">
                        <div class="card shadow position-relative overflow-hidden">
                            <div style="max-height: 75vh" id="cropper-js-image-container">
                                {{--<img src="" id="cropperImage" alt="..." style="max-width:100%">--}}
                            </div>
                            <div class="border-top border-primary p-3 text-end w-100 card-footer">
                                <div class="btn btn-outline-primary me-2" onclick="cancelCropper()">
                                    Abbrechen
                                </div>
                                <div class="btn btn-primary me-2" onclick="getCroppedImage()">
                                    Anwenden
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


    </div>


    <script src="{{url('inc/cropperjs/dist/cropper.js')}}"></script>
    <link rel="stylesheet" href="{{url('inc/cropperjs/dist/cropper.css')}}">
    <script src="{{url('inc/sortableJS/sortableJS.js')}}"></script>

    <script>

        const imageUploaderButton = document.getElementById('image-uploader-button');
        const imageFileInput = document.getElementById('image-file-input');
        /*
        * image container in the modal
        */
        const cropperImageContainer = document.getElementById('cropper-js-image-container')


        const cropperImageModal = document.getElementById('cropper-js-modal')


        /*
        * Image & Input after cropping
        */
        const croppedImageContainer = document.getElementById('cropped-image-container')
        const croppedImageFormInput = document.getElementById('cropped-image-form-input')

        let cropperInstance = null;

        imageFileInput?.addEventListener('change', (event) => {
            if(!event.target?.files?.length) return;

            createImageBase64(event.target.files[0]).then(base64 => {

                croppedImageContainer.innerHTML = '';
                croppedImageFormInput.value = null;
                /*
                * Reset Image tag & cropper instance
                */
                cropperImageContainer.innerHTML = '';
                cropperInstance = null;
                /*
                * Transform file in base64 image
                */
                /*
                * Show Modal
                */
                cropperImageModal.classList.remove('d-none')

                const image = new Image()

                image.style.maxWidth = '100%';

                image.src = base64;

                cropperImageContainer.appendChild(image)

                cropperInstance = new Cropper(image, {
                    aspectRatio : 16 / 9,
                });

            })
        })


        function createImageBase64(file){
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.readAsDataURL(file);
                reader.onload = () => {
                    if(typeof reader.result == 'string'){
                        resolve(reader.result)
                    }else{
                        reject('Base64 error')
                    }
                };
            });
        }

        function getCroppedImage(){
            if(cropperInstance){
                const croppedImageBase64 = cropperInstance.getCroppedCanvas({
                                                                                fillColor : '#FFFFFF',
                                                                            }).toDataURL()


                const imagePreview = new Image();
                imagePreview.src = croppedImageBase64;
                imagePreview.classList.add('w-100');
                /*
                * Append image preview
                */
                croppedImageContainer.appendChild(imagePreview)
                /*
                * Add value to hidden input
                */
                croppedImageFormInput.value = croppedImageBase64;
            }
            cancelCropper()
        }

        function cancelCropper(){
            cropperImageModal.classList.add('d-none')
        }

        /*
        *
        *  SORTBALE JS
        *
        *
        * */

        const endpoint = `{{url('backend/kidskrone/slideshow/js')}}`;



        [...document.getElementsByClassName('sortable-js')].forEach(el => {

            console.log(el)

            Sortable.create(el, {
                ghostClass : 'ghost',
                direction : 'vertical',
                handle : '.handle',
                onEnd : () => {

                    console.log(el)

                    /*
                    *  array of datasets [{sort: '0', id: '12'}]
                    */
                    const newOrder = Array.from(el.querySelectorAll(`[data-sortable-id]`))
                                          .map((element, index) => {
                                              return {
                                                  id : element.dataset.sortableId,
                                                  sort : index
                                              }
                                          })



                    fetch(endpoint,
                          {
                              method : 'POST',
                              headers : {
                                  'X-CSRF-TOKEN' : "{{ csrf_token() }}",
                                  'Content-Type' : 'application/json'
                              },
                              body : JSON.stringify({ orderArray : newOrder }),

                          }
                    )
                        .then(response => response.json())
                        .then(json => console.log(json))
                        .catch((e) => {

                            console.log(e)
                        });



                }
            })
        })


    </script>
    <style>

        .sortable-js .ghost .accordion-button{
            border:2px solid #3b71ca !important;
            color:#3b71ca !important;
        }

        .sortable-js .handle{
            cursor:grab;
        }

        [data-bs-original-title="_hidden_"]{
            display:none;
        }



    </style>
@endsection
