@extends('backend.master')
@section('content')

    <!-- Modal Delete Video-->

    @if($video)
        <div class="modal fade" id="delete-modal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1"
             aria-hidden="true">
            <div class="modal-dialog  modal-dialog-centered">
                <form class="modal-content" action="{{url('backend/kidskrone/'.$video->id . '/delete')}}"
                      id="form" name=""
                      method="post">
                    {{ csrf_field() }}
                    @method('delete')
                    <div class="modal-header">
                        <h5 class="modal-title" id="any-label">{{$video->name}}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        W<PERSON><PERSON> löschen?
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-brown" data-bs-dismiss="modal">N<PERSON>, abbrechen</button>
                        <button type="submit" class="btn btn-danger auto-disable"><i class="fad fa-trash"></i>
                            Ja, löschen
                        </button>
                    </div>
                </form>
            </div>
        </div>





        @foreach($video->attachments()->get() as $attachment)
            {{--  @include('backend.kidskrone.components.attachment')--}}
            <div class="modal fade" id="attachment-{{$attachment->id}}-delete-modal" data-bs-backdrop="static"
                 data-bs-keyboard="false" tabindex="-1"
                 aria-hidden="true">
                <div class="modal-dialog  modal-dialog-centered">
                    <form class="modal-content"
                          action="{{url('backend/kidskrone/attachment/'.$attachment->id.'/delete')}}"
                          id="form" name=""
                          method="post">
                        {{ csrf_field() }}
                        @method('delete')
                        <div class="modal-header">
                            <h5 class="modal-title" id="any-label">{{$attachment->button}}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"
                                    aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            Wirklich löschen?
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-brown" data-bs-dismiss="modal">Nein, abbrechen
                            </button>
                            <button type="submit" class="btn btn-danger auto-disable"><i class="fad fa-trash"></i>
                                Ja, löschen
                            </button>
                        </div>
                    </form>
                </div>
            </div>

        @endforeach

    @endif

    <div id="form-container" class="row  shadow pt-3">

        <div class="col-12">
            <div class="alert alert-danger alert-dismissible d-none" role="alert" id="alert-error">
                <h3>Errors</h3>
                <span class="text"></span>
                <button type="button" class="btn-close"
                        onclick="document.querySelector('#alert-error').classList.add('d-none')"></button>
            </div>
        </div>

        @if($video)
            @if($video->isOnVimeo())
                <div class="col-12">
                    <div class="border">
                        <div class="row">
                            <div class="col-8 pe-0">

                                <div class="ratio ratio-16x9">
                                    @if($video->is_playable)
                                        <iframe
                                                id="iframe-video-player"
                                                name="iframe-{{Str::random(10)}}"
                                                src="{{$video->url}}" frameborder="0"></iframe>
                                    @else
                                        <div class="d-flex justify-content-center align-items-center">
                                            <div class="text-center">
                                                <h2>
                                                    Das Video wird vorbereitet.
                                                </h2>
                                                <h6>
                                                    Bitte aktualisieren Sie diese Seite, oder die
                                                    <a href="{{url('/backend/kidskrone')}}" class="link-primary">Startseite</a>
                                                    , in ein paar Minuten.
                                                </h6>
                                                <h6>
                                                    Man kann in der Zwischenzeit weitere Videos hochladen.
                                                </h6>
                                            </div>

                                        </div>
                                    @endif
                                </div>


                                <div class="px-3 py-2   d-flex justify-content-between">
                                    <div>

                                        <div>
                                            <span
                                                    class="badge bg-primary fw-normal fs-6">ID: {{$video->vimeo_id}}</span>
                                        </div>

                                        <div class="mt-2">
                                            Erstellungsdatum: {{$video->created_at}}
                                        </div>

                                    </div>


                                    <div class="text-end">
                                        <div>
                                            Status:

                                            @if($video->is_playable)
                                                <span class="badge bg-success fw-normal fs-6">{{$video->status}}</span>
                                            @else
                                                <span class="badge bg-warning fw-normal fs-6">{{$video->status}}</span>
                                            @endif
                                        </div>
                                        <div class="mt-2">
                                            <a class="link-success"
                                               href="https://vimeo.com/manage/videos/{{$video->vimeo_id}}"
                                               target="_blank">Vimeo
                                                (Video {{$video->vimeo_id}})</a>
                                        </div>

                                    </div>


                                </div>
                            </div>
                            <div class="col-lg-4">

                                @if($video->is_playable)
                                    <div class="  p-3 mb-3">
                                        <div class="mb-1">
                                            Video-Vorschaubild
                                        </div>

                                        <div class="w-100 text-center">
                                            <img
                                                    id="thumbnail"
                                                    src="{{$video->image}}"
                                                    alt="Thumbnail"
                                                    class="img-fluid rounded"
                                            />
                                        </div>
                                        <div class="text-center d-none mb-1 p-2" id="saving-thumbnail">
                                            <div>
                                                Wird gespeichert...
                                            </div>
                                            <div class="spinner-border" role="status">
                                                <span class="visually-hidden">Loading...</span>
                                            </div>
                                        </div>
                                        <div class=" text-center pt-3">
                                            <button
                                                    class="btn btn-brown btn-sm auto-disable w-100"
                                                    id="video-frame-image-button"
                                                    data-endpoint="{{url('backend/kidskrone/'.$video->id.'/thumbnail')}}"
                                                    data-image-tag="#thumbnail"
                                            >

                                                Aktuelles Videobild aufnehmen

                                            </button>
                                        </div>

                                    </div>
                                @endif


                            </div>
                        </div>
                    </div>

                </div>

            @else
                <div class="col-12 ">
                    <div class="alert alert-primary fw-  fs-5">
                        <b>Video noch nicht hochgeladen. </b> <br>
                        <small>Alle Beiträge ohne Videos werden auf kidskrone.at nicht angezeigt.</small>
                    </div>

                </div>
            @endif
        @endif


        <div class="col-12  ">


            <form action="{{ url('backend/kidskrone/' . (($video) ? $video->id : '')) }}" method="post" class="pt-3"
                  id="vimeo-form"
            >
                {{ csrf_field() }}


                <div class="row">


                    <div class="col-lg-8">


                        <label class="w-100 mb-3 d-block">
                            Video File @if($video && $video->isOnVimeo())
                                (Video austauschen)
                            @endif
                            <span class="input-group">
                                <input type="file"
                                       accept="video/*"
                                       class="form-control"
                                       {{--Important--}}
                                       data-vimeo-video
                                >
                            </span>
                        </label>

                        <label class="d-block  mb-3">
                            <span
                                    class="d-block me-2 d-flex flex-column justify-content-center ">Kategorie:</span>

                            <select class="form-select"
                                    name="category">


                                @foreach($categories as $cat)
                                    <option
                                            {!! ($video && $video->category == $cat['value'])  ?  'selected' : ''  !!}
                                            value="{{$cat['value']}}">{{$cat['viewValue']}}</option>
                                @endforeach

                            </select>
                        </label>


                        <label class="d-block  mb-3">
                            <span
                                    class="d-block me-2 d-flex flex-column justify-content-center ">Saison:</span>
                            <select class="form-select"
                                    name="season">


                                @foreach($seasons as $_season)
                                    @if($loop->first)
                                        <option
                                                {!! ($video && $video->season == $_season)  ?  'selected' : ''  !!}
                                                value="">Ungeordnet
                                        </option>
                                    @endif
                                    <option
                                            {!! ($_season->selected)  ?  'selected' : ''  !!}
                                            value="{{$_season->value}}">{{$_season->value}} {{($_season->current) ? '(aktuelle Saison)' : ''}}</option>
                                @endforeach

                            </select>
                        </label>

                        <div class="mb-3">
                            <label for="name">Name</label>
                            <input type="text" class="form-control" id="name" name="name"
                                   value="{{$video ? $video->name : ''}}">
                        </div>

                        <div class="mb-3">
                            <label for="short_description">Videountertext</label>
                            <textarea name="description" id="description" class="form-control"
                                      maxlength="4500"
                                      rows="4">{{$video ? $video->description : ''}}</textarea>
                        </div>

                        <div class="mb-3">
                            <label for="long_description">Seitentext (max. 550 Zeichen inkl. Leerzeichen)</label>
                            <textarea name="long_description" id="long_description" class="form-control"
                                      maxlength="4500"
                                      rows="10">{{$video ? $video->long_description : ''}}</textarea>
                        </div>


                        <div class="d-flex justify-content-between mb-3">
                            <div>
                                <a href="{{url('/backend/kidskrone/')}}" class="btn btn-brown auto-disable">
                                    <i class="far fa-arrow-left"></i>
                                    Zurück</a>

                            </div>
                            <div class="text-end">

                                <button type="button" class="btn btn-danger auto-disable " data-bs-toggle="modal"
                                        data-bs-target="#delete-modal">
                                    <i class="fad fa-trash"></i>
                                    Löschen
                                </button>
                                <button type="submit" class="btn btn-brown auto-disable">
                                    <i class="fad fa-save"></i>
                                    Speichern
                                </button>
                            </div>
                        </div>

                    </div>

                    <div class="col-lg-4">
                        <div class=" border p-3">

                            <div class="custom-image-preview--label">Cover Bild</div>


                            {{--  {{$video->coverFullPath}}--}}

                            <div class=" rounded overflow-hidden position-relative ">
                                <img src="{{($video && $video->coverFullPath) ? url($video->coverFullPath) : ''}}"
                                     alt=""
                                     class="custom-image-preview w-100  ">
                            </div>

                            @if($video && $video->coverFullPath)
                                <div class="form-check position-relative mt-3" id="image-delete--container">
                                    <input class="form-check-input" type="checkbox" value="delete" id="image-delete"
                                           name="image_delete_checkbox"/>
                                    <label class="form-check-label" for="image-delete">Dieses Bild vom Server
                                        löschen</label>
                                </div>
                            @endif

                            <div class="file-upload-wrapper border rounded  mt-3">
                                <input
                                        {{--        @if($video->coverFullPath)
                                                    data-bs-default-file="{{$video->coverFullPath}}"
                                                @endif--}}
                                        type="file"

                                        class="file-upload-input"
                                        data-bs-file-upload="file-upload"
                                        data-bs-default-msg="Bild hierher ziehen oder klicken"
                                        data-bs-preview-msg="Bild hierher ziehen oder klicken"
                                        data-bs-remove-btn="Abbrechen"
                                        data-bs-format-error="Ihre Datei hat ein falsches Dateiformat (korrektes Format: ~~~)"
                                        data-bs-main-error="Ooops, ein Fehler ist aufgetreten"
                                        data-bs-accepted-extensions="image/*"
                                        data-bs-height="100"
                                        {{--VIMEO--}}
                                        name="file_image"
                                />
                            </div>


                        </div>
                    </div>


                    <hr>


                    @if($video instanceof \App\Models\Kidskrone\KidskroneVideo && $video->name)
                        <div class="col-12">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <h2 class="m-0">Attachments</h2>
                                <div>
                                    <a class="btn btn-sm btn-outline-success text-success w-100"
                                       href="{{url('backend/kidskrone/' . $video->id . '/attachment')}}"
                                    > ADD <i class="fa-solid fa-plus"></i></a>
                                </div>

                            </div>

                            <table class="table table-bordered">
                                <thead>
                                <tr>
                                    <th>Button Name</th>
                                    <th>File Type</th>
                                    <th>Bearbeiten</th>
                                    <th>Löschen</th>
                                </tr>

                                </thead>

                                <tbody>
                                @foreach($video->completedAttachments()->get() as $attachment)
                                    <tr>
                                        <td>
                                            {{$attachment->button}}
                                        </td>
                                        <td>
                                            {{$attachment->type}}
                                        </td>
                                        <td style="width: 12rem" class="align-content-center">
                                            <a class="btn btn-sm btn-outline-success text-success w-100"
                                               href="{{url('backend/kidskrone/attachment/' . $attachment->id )}}"
                                            > <i class="fa-duotone fa-pen-to-square"></i></a>
                                        </td>
                                        <td style="width: 12rem" class="align-content-center">
                                            <button type="button"
                                                    class="btn btn-sm btn-outline-danger text-danger w-100"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#attachment-{{$attachment->id}}-delete-modal"
                                            ><i class=" fa-duotone fa-trash"></i></button>
                                        </td>
                                    </tr>
                                @endforeach

                                </tbody>


                            </table>
                        </div>
                    @endif

                </div>


            </form>
        </div>
    </div>

    <div class="w-100 fs-5">
        {{--Before starting--}}
        <div class="indicators text-center d-none" id="before-starting">
            <div class="mb-1">
                Bitte warten...
            </div>
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
        </div>
        {{--Uploading--}}
        <div class="indicators  d-none" id="progress-bar">
            <div class="text-center">
                <div id="percentage">
                    0%
                </div>
                <div>
                    <div class="progress" style="height: 20px;">
                        <div id="bar" class="progress-bar progress-bar-striped bg-primary" role="progressbar"
                             aria-valuemin="0"
                             aria-valuemax="100"
                             style="width: 0"></div>
                    </div>
                </div>
            </div>
            <div class="p-3 text-center fw-bold">
                Schließen Sie diesen Tab nicht, bevor der Upload abgeschlossen ist.
            </div>
        </div>
        {{--Done--}}
        <div class="indicators text-center d-none" id="successfully-uploaded">
            <div class="alert alert-success text-center">
                Erfolgreich gespeichert!
            </div>
        </div>
    </div>





    <script src={{url("vimeo-sdk/js/tus-js-client/dist/tus.min.js")}}></script>
    <script src={{url("vimeo-sdk/js/VimeoVideoUploader.js")}}></script>
    <script>
        const onSuccessAction = `{{($video) ? 'refresh' : 'redirect'}}`;

        const afterCompletionRedirect = `{{url('backend/kidskrone')}}`;

        /*
        * indicators
        */
        const beforeStarting = document.querySelector('#before-starting');
        const progressBar = document.querySelector('#progress-bar');
        const successfullyUploaded = document.querySelector('#successfully-uploaded');
        const alertError = document.querySelector('#alert-error');
        /*
        * Form
        */
        const form = document.querySelector('#vimeo-form');
        const formContainer = document.querySelector('#form-container');

        const vimeoVideoUploader = new VimeoVideoUploader(form);

        vimeoVideoUploader.onStart = () => {

            formContainer.classList.add('d-none')
            beforeStarting.classList.remove('d-none')
        }

        vimeoVideoUploader.onError = (error) => {
            Array.from(document.querySelectorAll('.indicators')).forEach((x) => x.classList.add('d-none'))
            formContainer.classList.remove('d-none')
            alertError.querySelector('.text').innerText = error.error
            alertError.classList.remove('d-none')
        }

        vimeoVideoUploader.onProgress = (bytesUploaded, bytesTotal) => {
            beforeStarting.classList.add('d-none')
            document.querySelector('#vimeo-form').classList.add('d-none')
            progressBar.classList.remove('d-none')
            let percentage = (bytesUploaded / bytesTotal * 100).toFixed(2) + '%'
            progressBar.querySelector('#percentage').textContent = percentage
            progressBar.querySelector('#bar').style.width = percentage
        }

        vimeoVideoUploader.onSuccess = (upload) => {
            // upload is null when the no Video is being uploaded
            Array.from(document.querySelectorAll('.indicators')).forEach((x) => x.classList.add('d-none'))
            successfullyUploaded.classList.remove('d-none')
            setTimeout(() => {

                if (onSuccessAction === 'refresh') {
                    window.location.reload()
                } else {
                    window.location.replace(afterCompletionRedirect);
                }
            }, 700)


        }


    </script>
    {{--
            Video-Vorshaubild
    --}}
    {{-- vimeo player sdk --}}
    {{-- DOCU https://developer.vimeo.com/player/sdk/basics --}}
    <script src="{{url('vimeo-sdk/js/vimeo-player.js')}}"></script>
    <script>

        const player = new Vimeo.Player(document.querySelector('#iframe-video-player'));
        const buttonVideoFrame = document.querySelector('#video-frame-image-button');
        buttonVideoFrame.addEventListener('click', async () => {
            console.log('CLI')
            const image = document.querySelector(buttonVideoFrame.dataset.imageTag);
            buttonVideoFrame.disabled = true;
            player.pause();
            const seconds = await player.getCurrentTime();
            const body = {
                time: seconds,
                active: true // set it immediately
            };
            const token = document.querySelector('meta[name="_token"]')?.content;
            if (!token) {
                console.error('Meta tag "_token" not found!!!')
                return;
            }
            const response = await fetch(buttonVideoFrame.dataset.endpoint,
                {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': token,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(body),
                }
            );

            const obj = await response.json();
            if (response.ok) {
                image.src = obj.image
            }
            buttonVideoFrame.disabled = false;
        })

    </script>

    {{--
        Cover Bild
    --}}
    <script>
        const previewImg = document.querySelector('.custom-image-preview')
        const imageDeleteContainer = document.querySelector('#image-delete--container');
        const previewImgOriginalSrc = previewImg.src
        setTimeout(() => {


            const el = document.querySelector('.file-upload-previews')


            el?.addEventListener('DOMSubtreeModified', () => {
                const image = el.querySelector(`img.file-upload-preview-img`);

                if (image?.src) {
                    /*
                    *  if image is set
                    */
                    previewImg.src = image.src;
                    /*
                    * set default no delete and show only if the image shown is the one saved
                    */
                    if (imageDeleteContainer) {
                        /*
                        * only here when the image exists
                        */
                        imageDeleteContainer.querySelector('input').checked = false;
                        imageDeleteContainer.classList.add('d-none')
                    }

                } else {
                    previewImg.src = previewImgOriginalSrc;
                    /*
                    * set default no delete and show only if the image shown is the one saved
                    */
                    if (imageDeleteContainer) {
                        /*
                        * only here when the image exists
                        */
                        imageDeleteContainer.querySelector('input').checked = false;
                        imageDeleteContainer.classList.remove('d-none')
                    }

                }


            }, false);

        }, 300)
    </script>

@endsection
