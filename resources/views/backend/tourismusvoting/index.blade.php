@extends('backend.master')

@section('content')
    <div class="row">
        <div class="col my-5">
            <h1 class="card-title">ERGEBNISSE NÖ TOURISMUSWAHL 2022</h1>
        </div>
    </div>

    <div class="row mb-5">
        <div class="col">
            <div class="alert alert-secondary text-center">
                <h3>
                    <div class="fw-bold mb-2">Votings gesamt</div>
                    {{number_format($votings_raw ,0,",",".") }}
                </h3>
            </div>
        </div>
        <div class="col">
            <div class="alert alert-success text-center">
                <h3>
                    <div class="fw-bold mb-2">Votings bereinigt</div>
                    {{number_format($votings_clean ,0,",",".") }}
                </h3>
            </div>

        </div>
    </div>

    @foreach($data_raw as $kategorie => $value)
        <div class="row">
            <div class="col mb-3">
                <h3>{{ $projekte[$kategorie] }}</h3>
            </div>
        </div>
        <div class="row">
            <div class="col-12 mb-2">Original (alles wird gezählt)</div>
            <div class="col-12 col-md">
                <div class="alert alert-secondary">
                    <b>{{ $value["prozent"] }}%</b>
                    <small>({{ number_format($value["anzahl"],0,",",".") }})</small>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-12 mb-2">Bereinigt (max 5 Votings pro Session)</div>
            <div class="col-12 col-md">
                <div class="alert alert-success">
                    <b>{{ $data_clean[$kategorie]["prozent"] }}%</b>
                    <small>({{ number_format($data_clean[$kategorie]["anzahl"],0,",",".") }})</small>
                </div>
            </div>
        </div>
    @endforeach
@endsection
