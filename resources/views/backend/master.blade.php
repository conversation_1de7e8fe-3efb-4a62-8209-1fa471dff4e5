<!DOCTYPE html>
<html lang="de">
<head>
    <title>MediaPrint Backend</title>

    <!-- METATAGS -->
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=10,IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="_token" content="{!! csrf_token() !!}"/>
    <meta name="robots" content="noindex, nofollow"/>


    <link rel="stylesheet" href="{{url('inc/bootstrap-5.2.2/dist/css/bootstrap.min.css')}}">
    <script src="{{url('inc/bootstrap-5.2.2/dist/js/bootstrap.bundle.min.js')}}"></script>



    <script src="{{ url("inc/fontawesome-pro-6.5.2-min/dist/all.min.js") }}" type="text/javascript"></script>
    <script src="{{ url("inc/fontawesome-pro-6.5.2-min/dist/all.min.js") }}" type="text/javascript"></script>

    <script src="{{url('inc/jquery/jquery.js')}}"></script>
    <!-- CSS -->
    <link rel="stylesheet" type="text/css" href='{{ url("inc/DataTables-1.10.15/css/dataTables.bootstrap4.css") }}'/>
    <link rel="stylesheet" type="text/css"
          href='{{ url("inc/bootstrap-4-datetimepicker/css/bootstrap-datetimepicker.css") }}'/>
    <link rel="stylesheet" type="text/css" href='{{ url("inc/css/animate.min.css") }}'>

    <link rel="stylesheet" type="text/css" href='{{ url("inc/notie/notie.css") }}'/>
    <link rel="stylesheet" type="text/css" href='{{ url("inc/jquery-ui-1.12.1/jquery-ui.css") }}'/>
    <link rel="stylesheet" type="text/css" href='{{ url("inc/select2-4.0.12/dist/css/select2.min.css") }}'/>
    <link rel="stylesheet" type="text/css" href='{{ url("inc/aos/aos.css") }}'/>

    <link rel="stylesheet" type="text/css" href='{{ url("inc/css/frontend_fonts.css") }}#{{now()->format("Ymdh")}}'/>
    <link rel="stylesheet" type="text/css" href='{{ url("inc/css/frontend.css") }}#{{now()->format("Ymdh")}}'/>
    <link rel="stylesheet" type="text/css" href='{{ url("inc/css/backend21.css") }}#{{now()->format("Ymdhi")}}'/>
    <link rel="stylesheet" type="text/css" href='{{ url("inc/autocomplete/easy-autocomplete.min.css")}}'/>

    <!-- SCRIPT INCLUDES -->
    <script type="text/javascript" src='{{ url("inc/js/bluebird.min.js") }}'></script>
    <script type="text/javascript" src='{{ url("inc/jquery-ui-1.12.1/jquery-ui.min.js") }}'></script>
    <script type="text/javascript" src='{{ url("inc/DataTables-1.10.15/js/jquery.dataTables.min.js") }}'></script>
    <script type="text/javascript" src='{{ url("inc/DataTables-1.10.15/js/dataTables.bootstrap4.min.js") }}'></script>

    <script type="text/javascript"
            src='{{ url("inc/js/bs-custom-file-input.min.js") }}#{{\Carbon\Carbon::now()->format("Ymdh")}}'></script>


    <script type="text/javascript" src='{{ url("inc/select2-4.0.12/dist/js/select2.min.js") }}'></script>

    <script type="text/javascript" src='{{ url("inc/js/moment-with-locales.js") }}'></script>
    <script type="text/javascript"
            src='{{ url("inc/bootstrap-4-datetimepicker/js/bootstrap-datetimepicker.min.js") }}'></script>
    <script type="text/javascript" src='{{ url("inc/js/bowser.js") }}'></script>
    <script type="text/javascript" src='{{ url("inc/notie/notie.js") }}'></script>


    <script type="text/javascript" src='{{ url("inc/ckeditor/ckeditor.js") }}'></script>

    <script type="text/javascript"
            src='{{ url("inc/js/clipboard.min.js") }}#{{\Carbon\Carbon::now()->format("Ymdh")}}'></script>
    <script type="text/javascript" src='{{ url("inc/aos/aos.js") }}'></script>
    <script type="text/javascript"
            src='{{ url("inc/js/backend.js") }}#{{\Carbon\Carbon::now()->format("Ymdh")}}'></script>
    <script type="text/javascript" src="{{ url("inc/autocomplete/jquery.easy-autocomplete.min.js") }}"></script>


</head>
<body class="p-0">

<script>
    var baseurl = "{{url('/')}}";
    var ckeditor_fontcolor = "{{env('CKEDITOR_ELEMENTS_FONTCOLOR')}}";
    var notieError = "";
    var notieWarning = "";
    var notieInfo = "";
    var notieSuccess = "";
    {!! (Session::has("notieSuccess")) ? "notieSuccess = '".Session::get("notieSuccess")."';" : "" !!}
    {!! (Session::has("notieWarning")) ? "notieWarning = '".Session::get("notieWarning")."';" : "" !!}
    {!! (Session::has("notieInfo")) ? "notieInfo = '".Session::get("notieInfo")."';" : "" !!}
    {!! (count($errors)) ? "notieError = '".join(', ',$errors->all())."'" : "" !!}
    $(function () {
        {!! (Session::has("jumpToClass")) ? "jumpToClass('".Session::get("jumpToClass")."');" : "" !!}
    });
</script>

<span id="is-xs" class="d-sm-none"></span>

@include('backend.messages.confirm')
@include("backend.partials.nav")

<div class="container">
    <div id="content">
        @if(env('APP_ENV') != "production")
            <div class="alert alert-info fs-5">
                <b><i class="fa-solid fa-circle-info"></i> INFO</b> <br><br>
                Sie befinden sich auf dem <b>Testserver</b>. <br>Jegliche hier vorgenommene Änderung wird keine
                Auswirkungen auf die Online-Website haben.
            </div>
        @endif
        @yield("content")
    </div>
</div>

{{--Spekout.program.index--}}
<div class="container-fluid">
    @yield("container-fluid")
</div>

{{--
// Use it like this in your template:
@section("scripts")
    <script src="{{url('inc/js/my-script.js') }}"></script>
@endsection
--}}
@yield('scripts', null)

</body>
</html>
