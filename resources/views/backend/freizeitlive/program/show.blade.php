@php

    use App\Models\Freizeitlive\FreizeitlivePartner;use App\Models\Freizeitlive\FreizeitliveSpeaker;use App\Models\Freizeitlive\FreizeitliveProgram;
    $allSpeakers = FreizeitliveSpeaker::getWholeSpeakersList();

        $allSponsors = FreizeitlivePartner::whereNotNull('logoPath')->get();
@endphp


@extends('backend.master')

@section('container-fluid')

    <div class="row mb-5">
        <div class="col-12 mb-3">
            <div>
                <h2>Freizeitlive - Programm</h2>
            </div>
        </div>



        @php

            if(empty($program)){
                $program = new FreizeitliveProgram();
            }
            $speakerIds = ($program->id) ? $program->speakers->map(function($x){return $x->id;})->join(';') :"";
            $sponsorIds = ($program->id) ? $program->sponsors->map(function($x){return $x->id;})->join(';') :"";
        @endphp



            <form class="modal-dialog modal-dialog-centered  speaker--form"
                  action="{{ url()->current() . (($program->id) ? "/$program->id" : "") }}" method="post"
                  enctype="multipart/form-data">
                {{ csrf_field() }}
                @method('POST')
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Bearbeiten</h5>
                        <button type="button" class="btn-close" data-bs-ripple-init data-bs-dismiss="modal"
                                aria-label="Close"></button>
                    </div>
                    <div class="modal-body">

                        <div class="mb-3">
                            <label for="" class="w-100">
                                Zimmer
                                <select class="form-select" name="room_id">
                                    @foreach(FreizeitliveProgram::getRooms() as $room)
                                        <option @if($room['id'] == $program->room_id) selected
                                                @endif  value="{{$room['id']}}">{{$room['name']}}</option>
                                    @endforeach
                                </select>
                            </label>

                        </div>
                        <div class="row mb-3">
                            <div class="col-sm-6">
                                <label for="input-{{$program->id}}-start" class="form-label">Startzeit</label>
                                <input type="time" class="form-control" id="input-{{$program->id}}-start" name="starts_at"
                                       value="{{$program->starts_at ? $program->starts_at->format('H:i') : null}}">
                            </div>


                            <div class="col-sm-6">
                                <label for="input-{{$program->id}}-end" class="form-label">Endzeit </label>
                                <input type="time" class="form-control" id="input-{{$program->id}}-end" name="ends_at"
                                       value="{{$program->ends_at ? $program->ends_at->format('H:i') : null}}">
                            </div>

                        </div>


                        <div class="mb-3">
                            <label for="input-{{$program->id}}-title" class="form-label">Titel</label>
                            <input name="title" type="text" class="form-control" id="input-{{$program->id}}-title"
                                   value="{{$program->title}}">
                        </div>


                        <div class="mb-3">
                            <label for="input-{{$program->id}}-description" class="form-label">Beschreibung</label>
                            <textarea name="description"
                                      class="form-control"
                                      id="input-{{$program->id}}-description"
                                      rows="3">{{$program->description}}</textarea>
                        </div>

                        <div class="mb-3">
                            <label for="input-{{$program->id}}-text" class="form-label">Text</label>


                            <textarea
                                ckeditor="true"
                                ckeditor_height="300"
                                ckeditor_removeButtons="{{env('CKEDITOR_ELEMENTS_MIDI')}}"
                                id="input-{{$program->id}}-text" class="form-control mb-3 " name="text">{{$program->text}}
							</textarea>
                        </div>


                        {{--Js--}}
                        {{--                <input name="speakers" class="added-speakers-input" type="hidden" value="{{$speakerIds}}" >--}}


                        {{--Js--}}
                        {{--                <input name="partners" class="added-partners-input" type="hidden" value="{{$partnerIds}}" >--}}


                    </div>
                    <div class="modal-footer">
                        <button type="submit" class="btn btn-primary" data-bs-ripple-init>Speichern</button>
                    </div>
                </div>
            </form>




        <script>


        function removeSpeaker(element) {
            const id = element.dataset.speakerId
            const addedSpeakersInput = element.closest('form').querySelector('.added-speakers-input');
            const all = (addedSpeakersInput.value ?? "").split(';').filter(x => x.toString() !== id.toString());
            addedSpeakersInput.value = all.join(';');
            document.getElementById("speaker--" + id)?.remove();
        }


        function addSpeakerEvents(speakerSelectors, speakersRow) {
            speakerSelectors.forEach(speaker => {
                const id = speaker.dataset.speakerId.toString();
                const name = speaker.dataset.speakerName;
                const image = speaker.dataset.speakerImage;

                const addedSpeakersInput = speaker.closest('form').querySelector('.added-speakers-input');

                speaker.onclick = (ev) => {

                    const all = (addedSpeakersInput.value ?? "").split(';').filter(x => x);

                    if (all.includes(id)) {
                        // already here
                        return;
                    }

                    all.push(id)

                    addedSpeakersInput.value = all.join(';');

                    const column = `  <div class="col-lg-3  col-6 mb-3" id="${"speaker--" + id}">

                                    <div class="card h-100">
                                         <div class="text-end">
                                            <button type="button" class="btn btn-sm"
                                            data-speaker-id="${id}"
                                            onclick="removeSpeaker(this)"
                                            >
                                                    <i class="fa-regular fa-xmark text-danger"></i>
                                            </button>
                                        </div>
                                        <img src="${image}"  alt="">
                                        <div class="card-body" >
                                             ${name}
                                        </div>
                                    </div>
                                </div>`;

                    speakersRow.innerHTML += column;
                }
            })
        }


        document.querySelectorAll('.speaker--form').forEach((form) => {
            const speakerSelectors = form.querySelectorAll('.speaker--selector');
            const speakersRow = form.querySelector('.speakers--row');
            addSpeakerEvents(speakerSelectors, speakersRow);
        })

        function removeSponsor(element) {
            const id = element.dataset.sponsorId;
            const addedSponsorsInput = element.closest('form').querySelector('.added-sponsors-input');
            const all = (addedSponsorsInput.value ?? "").split(';').filter(x => x.toString() !== id.toString());
            addedSponsorsInput.value = all.join(';');
            document.getElementById("sponsor--" + id)?.remove();
        }

        function addSponsorEvents(sponsorSelectors, sponsorsRow) {
            sponsorSelectors.forEach(sponsor => {
                const id = sponsor.dataset.sponsorId.toString();
                const name = sponsor.dataset.sponsorName;
                const logo = sponsor.dataset.sponsorLogo;

                const addedSponsorsInput = sponsor.closest('form').querySelector('.added-sponsors-input');

                sponsor.onclick = (ev) => {
                    const all = (addedSponsorsInput.value ?? "").split(';').filter(x => x);
                    if (all.includes(id)) {
                        // already here
                        return;
                    }
                    all.push(id);
                    addedSponsorsInput.value = all.join(';');

                    const column = `<div class="col-lg-3 col-6 mb-3" id="sponsor--${id}">
                                <div class="card h-100">
                                    <div class="text-end">
                                        <button type="button" class="btn btn-sm"
                                        data-sponsor-id="${id}"
                                        onclick="removeSponsor(this)">
                                            <i class="fa-regular fa-xmark text-danger"></i>
                                        </button>
                                    </div>


                                        <div class="p-3" style="max-width: 10rem">
                                            <img src="${logo}" alt="" class="img-fluid">
                                        </div>


                                </div>
                            </div>`;
                    sponsorsRow.innerHTML += column;
                }
            })
        }

        document.querySelectorAll('.speaker--form').forEach((form) => {
            const sponsorSelectors = form.querySelectorAll('.sponsor--selector');
            const sponsorsRow = form.querySelector('.sponsors--row');
            addSponsorEvents(sponsorSelectors, sponsorsRow);
        })

    </script>
@endsection
