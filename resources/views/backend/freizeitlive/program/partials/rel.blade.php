{{--Speaker and Sponsor form inputs--}}

<input name="speakers" class="added-speakers-input" type="hidden" value="{{$speakerIds}}" {{--array like [2,4,6,7]--}} >


<div class="row speakers--row">
    @foreach($program->speakers as $speaker)
        <div class="col-lg-3 col-6 mb-3" id="speaker--{{$speaker->id}}">

            <div class="card h-100">
                <div class="text-end">

                    <button type="button" class="btn btn-sm"
                            data-speaker-id="{{$speaker->id}}"
                            onclick="removeSpeaker(this)">
                        <i class="fa-regular fa-xmark text-danger"></i>
                    </button>

                </div>
                <img src="{{$speaker->imageOneFullPath}}" alt="">
                <div class="card-body">
                    {{$speaker->name}}
                </div>
            </div>
        </div>
    @endforeach
</div>


<div class="col-lg-12 ">

    <div class="card h-100 py-3 text-center">
        <div class="dropdown ">
            <button class="btn btn-primary dropdown-toggle" type="button"
                    id="{{$editAccordionId}}-speaker-selector"
                    data-bs-toggle="dropdown" aria-expanded="false">
                Speaker hinzufügen
            </button>
            <ul class="dropdown-menu" aria-labelledby="{{$editAccordionId}}-speaker-selector"
                style="
                                        overflow: hidden;
                                        overflow-y: auto;
                                        max-height: 280px;
                                        "
            >
                @foreach($allSpeakers as $current)
                    <li>
                        <div class="dropdown-item speaker--selector" style="cursor: pointer"
                             style="cursor: pointer"
                             class="speaker--selector"
                             data-speaker-id="{{$current->id}}"
                             data-speaker-name="{{$current->name}}"
                             data-speaker-image="{{$current->imageOneFullPath}}">
                            <img src="{{$current->imageOneFullPath}}" alt=""
                                 style="width: 30px;">
                            {{$current->name}}
                        </div>
                    </li>
                @endforeach
            </ul>
        </div>

    </div>


</div>



<input name="sponsors" class="added-sponsors-input" type="hidden" value="{{$sponsorIds}}" {{--array like [2,4,6,7]--}} >

<div class="row sponsors--row mt-3">
    @foreach($program->sponsors as $sponsor)
        <div class="col-lg-3 col-6 mb-3" id="sponsor--{{$sponsor->id}}">
            <div class="card h-100">
                <div class="text-end">
                    <button type="button" class="btn btn-sm"
                            data-sponsor-id="{{$sponsor->id}}"
                            onclick="removeSponsor(this)">
                        <i class="fa-regular fa-xmark text-danger"></i>
                    </button>
                </div>
                <div class="p-3" style="max-width: 10rem">
                    <img src="{{$sponsor->logoFullPath}}" alt="" class="img-fluid">
                </div>


            </div>
        </div>
    @endforeach
</div>

<div class="col-lg-12">
    <div class="card h-100 py-3 text-center">
        <div class="dropdown">
            <button class="btn btn-primary dropdown-toggle" type="button"
                    id="dropdown-sponsor-selector"
                    data-bs-toggle="dropdown" aria-expanded="false">
                Sponsor hinzufügen
            </button>
            <ul class="dropdown-menu" aria-labelledby="dropdown-sponsor-selector"
                style="overflow: hidden; overflow-y: auto; max-height: 380px;">
                @foreach($allSponsors as $current)
                    <li class="border-top border-bottom py-3">
                        <div class="dropdown-item sponsor--selector" style="cursor: pointer"
                             data-sponsor-id="{{$current->id}}"
                             data-sponsor-name="{{$current->name}}"
                             data-sponsor-logo="{{$current->logoFullPath}}">


                            <div class="p-3" style="max-width: 10rem">
                                <img src="{{$current->logoFullPath}}" alt="" class="img-fluid">
                            </div>

                        </div>
                    </li>
                @endforeach
            </ul>
        </div>
    </div>
</div>

