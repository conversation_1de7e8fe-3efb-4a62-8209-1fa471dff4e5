@php
    use App\Models\Freizeitlive\FreizeitliveProgram;
    if(empty($program)){
        $program = new FreizeitliveProgram();
    }
    $speakerIds = ($program->id) ? $program->speakers->map(function($x){return $x->id;})->join(';') :"";
    $sponsorIds = ($program->id) ? $program->sponsors->map(function($x){return $x->id;})->join(';') :"";
@endphp

<div>

    <form class="modal-dialog modal-dialog-centered  speaker--form"
          action="{{ url()->current() . (($program->id) ? "/$program->id" : "") }}" method="post"
          enctype="multipart/form-data">
        {{ csrf_field() }}
        @method('POST')
        <div class="modal-content">

            <div class="modal-body">


                <div class="d-flex pt-3 justify-content-end">
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" role="switch"
                               id="program-hidden--{!! $program->id ?? 'new' !!}" name="program_hidden"
                               @if($program->hidden) checked @endif>
                        <label class="form-check-label fs-5" for="program-hidden--{!! $program->id ?? 'new' !!}">Planpunkt
                            ausblenden</label>
                    </div>
                </div>


                <div class="mb-3">
                    <label for="" class="w-100">
                        Zimmer
                        <select class="form-select" name="room_id">
                            <option value="">- Bitte wählen -</option>
                            @foreach(FreizeitliveProgram::getRooms() as $room)
                                <option @if($room['id'] == $program->room_id) selected
                                        @endif  value="{{$room['id']}}">{{$room['name']}}</option>
                            @endforeach
                        </select>
                    </label>

                </div>
                <div class="row mb-3">

                    <div class="col-sm-4">
                        <label for="select-{{$program->id}}-eventdate" class="form-label">Tag</label>
                        <select class="form-select" id="select-{{$program->id}}-eventdate" name="eventdate">
                            <option value="">- Bitte wählen -</option>
                            @foreach(FreizeitliveProgram::getEventdates() as $date => $date_carbon)
                                <option value="{{$date}}"
                                    {{(!empty($program->eventdate) && $program->eventdate->format("Y-m-d") == $date) ? 'selected' : ''}}
                                >{{$date_carbon->format("d.m.Y")}}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-sm-4">
                        <label for="input-{{$program->id}}-start" class="form-label">Startzeit</label>
                        <input type="time" class="form-control" id="input-{{$program->id}}-start" name="starts_at"
                               value="{{$program->starts_at ? $program->starts_at->format('H:i') : null}}">
                    </div>

                    <div class="col-sm-4">
                        <label for="input-{{$program->id}}-end" class="form-label">Endzeit </label>
                        <input type="time" class="form-control" id="input-{{$program->id}}-end" name="ends_at"
                               value="{{$program->ends_at ? $program->ends_at->format('H:i') : null}}">
                    </div>

                </div>


                <div class="mb-3">
                    <label for="input-{{$program->id}}-title" class="form-label">Titel</label>
                    <input name="title" type="text" class="form-control" id="input-{{$program->id}}-title"
                           value="{{$program->title}}">
                </div>


                <div class="mb-3">
                    <label for="input-{{$program->id}}-description" class="form-label">Beschreibung</label>
                    <textarea name="description"
                              class="form-control"
                              id="input-{{$program->id}}-description"
                              rows="3">{{$program->description}}</textarea>
                </div>

                <div class="mb-3">
                    <label for="input-{{$program->id}}-text" class="form-label">Text</label>


                    <textarea
                        ckeditor="true"
                        ckeditor_height="300"
                        ckeditor_removeButtons="{{env('CKEDITOR_ELEMENTS_MIDI')}}"
                        id="input-{{$program->id}}-text" class="form-control mb-3 " name="text">{{$program->text}}
							</textarea>
                </div>

                <div class="mb-3 row border p-3">
                    <div class="col-8">
                        <label for="program_image_1-{{$program->id}}" class="form-label">Bild 1</label>
                        <input class="form-control" type="file" id="program_image_1-{{$program->id}}" name="image_1">

                    </div>
                    <div class="col-4">
                        @if($program->image1full)
                            <img src="{{$program->image1full}}" class="img-fluid" alt="" style="max-height: 15rem">
                            <div class="form-check text-danger fs-5 py-2">
                                <input name="image_1_delete" class="form-check-input" type="checkbox" value="delete"
                                       id="delete-image-1-check-{{$program->id}}">
                                <label class="form-check-label" for="delete-image-1-check-{{$program->id}}">
                                    Bild 1 löschen
                                </label>
                            </div>
                        @endif
                    </div>
                </div>

                <div class="mb-3 row border p-3">
                    <div class="col-8">
                        <label for="program_image_2-{{$program->id}}" class="form-label">Bild 2</label>
                        <input class="form-control" type="file" id="program_image_2-{{$program->id}}" name="image_2">
                    </div>
                    <div class="col-4">
                        @if($program->image2full)
                            <img src="{{$program->image2full}}" class="img-fluid" alt="" style="max-height: 15rem">
                            <div class="form-check text-danger fs-5 py-2">
                                <input name="image_2_delete" class="form-check-input" type="checkbox" value="delete"
                                       id="delete-image-2-check-{{$program->id}}">
                                <label class="form-check-label" for="delete-image-2-check-{{$program->id}}">
                                    Bild 2 löschen
                                </label>
                            </div>
                        @endif
                     </div>

                </div>


                <div class="mb-3 row border p-3">
                    <div class="col-8">
                        <label for="program_image_3-{{$program->id}}" class="form-label">Bild 3</label>
                        <input class="form-control" type="file" id="program_image_3-{{$program->id}}" name="image_3">
                    </div>
                    <div class="col-4">
                        @if($program->image3full)
                            <img src="{{$program->image3full}}" class="img-fluid" alt="" style="max-height: 15rem">
                            <div class="form-check text-danger fs-5 py-2">
                                <input name="image_3_delete" class="form-check-input" type="checkbox" value="delete"
                                       id="delete-image-3-check-{{$program->id}}">
                                <label class="form-check-label" for="delete-image-3-check-{{$program->id}}">
                                    Bild 3 löschen
                                </label>
                            </div>
                        @endif
                    </div>

                </div>

                {{--Js--}}
                {{--                <input name="speakers" class="added-speakers-input" type="hidden" value="{{$speakerIds}}" >--}}
                @include('backend.freizeitlive.program.partials.rel', ["editAccordionId" => $editAccordionId ?? 0])

                {{--Js--}}
                {{--                <input name="partners" class="added-partners-input" type="hidden" value="{{$partnerIds}}" >--}}


            </div>
            <div class="modal-footer">
                <button type="submit" class="btn btn-primary mt-3" data-bs-ripple-init>Speichern</button>
            </div>
        </div>
    </form>
</div>

