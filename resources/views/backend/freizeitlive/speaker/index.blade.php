@extends('backend.freizeitlive.fz-master')

@section('fz-content')
    <div class="row mb-5">
        <div class="col-12 mb-3">
            <div>
                <h2>Freizeitlive - Persönlichkeiten</h2>
            </div>
        </div>

        <div class="col-12">
            <div class="d-flex justify-content-between">
                <div>
                    <a class="btn btn-brown" href="{{url('backend/freizeitlive')}}">Zurück</a>
                </div>
                <form action="{{ url("backend/freizeitlive/speaker") }}" method="post">
                    {{ csrf_field() }}
                    <div class="form-group">
                        <button type="submit" class="btn btn-brown">
                            <i class="fa-solid fa-plus"></i> NEU
                        </button>
                    </div>
                </form>
            </div>
            <hr>
            <div class="sortable-js">
                @foreach($speakers as $speaker)
                    <div class="modal fade" id="delete-post-{{$speaker->id}}-modal" aria-hidden="true">
                        <div class="modal-dialog" role="document">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title"><i class="fa-solid fa-trash"></i></h5>
                                    <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                                        <span aria-hidden="true">&times;</span>
                                    </button>
                                </div>
                                <div class="modal-body">
                                    <p>Wirklich löschen?</p>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Abbrechen
                                    </button>

                                    <form action="{{ url("backend/freizeitlive/speaker/".$speaker->id."/delete") }}" method="post">
                                        {{ csrf_field() }}
                                        <input name="_method" type="hidden" value="delete">
                                        <button type="submit" class="btn btn-danger ms-3"><i
                                                class="fa-solid fa-trash"></i> Löschen
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div data-sortable-id="{{$speaker->id}}"class="d-flex align-items-center border-bottom p-3 mb-3 {{ ($speaker->active==1) ? "" : "bg-light" }}" title="{{ ($speaker->active==1) ? "Promi ist eingeblendet" : "Promi ist ausgeblendet" }}">
                        <div class="handle p-2 ">
                            <i class="fa-solid fa-up-down-left-right fs-2"></i>
                        </div>

                        <div class="ms-2" style="max-width: 15rem;">
                            <img src="{{$speaker->imageOneFullPath}}" alt="..." class="mw-100">
                        </div>

                        <div class="ms-3">
                            <h4>{{$speaker->name}}</h4>
                        </div>

                        <div style="flex: 1" class="d-flex justify-content-end">
                            <a href="{{ 'https://freizeitlive.kurier.at/speaker/'.$speaker->id.'/' . $speaker->seo_url.'?vorschau=true' }}" target="blank" class="btn btn-warning me-2" title="Vorschau anzeigen">
                                <i class="fa-regular fa-link"></i>
                            </a>
                            @if($speaker->active==1)
                                <a href="{{url('backend/freizeitlive/speaker/'.$speaker->id.'/active/'.$speaker->active)}}" class="btn btn-primary me-2" title="Ausblenden">
                                    <i class="fa-solid fa-eye-slash"></i>
                                </a>
                            @else
                                <a href="{{url('backend/freizeitlive/speaker/'.$speaker->id.'/active/'.$speaker->active)}}" class="btn btn-info text-white me-2" title="Einblenden">
                                    <i class="fa-solid fa-eye"></i>
                                </a>
                            @endif
                            <a href="{{url('backend/freizeitlive/speaker/' . $speaker->id)}}" class="btn btn-brown me-2" title="Bearbeiten">
                                <i class="fa-regular fa-pen-to-square"></i>
                            </a>
                            <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#delete-post-{{$speaker->id}}-modal" title="Löschen">
                                <i class="fa-solid fa-trash"></i>
                            </button>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>

    <style>
        .handle{
            cursor:grab;
        }

        .handle:hover{
            color:#07ED89;
        }
    </style>


    <script src="{{url('inc/sortableJS/sortableJS.js')}}"></script>

    <script>

        const endpoint = `{{url('backend/ajax/freizeitlive/speaker/sort')}}`;

        [...document.getElementsByClassName('sortable-js')].forEach(el => {

            Sortable.create(el, {
                ghostClass : 'ghost',
                direction : 'vertical',
                handle : '.handle',
                onEnd : () => {
                    /*
                    *  array of datasets [{sort: '0', id: '12'}]
                    */
                    const newOrder = Array.from(el.querySelectorAll(`[data-sortable-id]`))
                                          .map((element, index) => {
                                              return {
                                                  id : element.dataset.sortableId,
                                                  sort : index
                                              }
                                          })


                    fetch(endpoint,
                          {
                              method : 'POST',
                              headers : {
                                  'X-CSRF-TOKEN' : "{{ csrf_token() }}",
                                  'Content-Type' : 'application/json'
                              },
                              body : JSON.stringify({ orderArray : newOrder }),

                          }
                    )
                        .then(response => response.json())
                        .then(json => console.log(json))
                        .catch((e) => {
                            // On Error reload the Page
                            console.log(e)
                            //location.reload()
                        });


                }
            })
        })
    </script>
@endsection
