@extends('backend.freizeitlive.fz-master')
@section('fz-content')

    <div class="row ">
        <div class="col-12">
            <div class="d-flex  ">
                <h2>Links (index)</h2>
            </div>
        </div>

        <div class="  col-lg-8 col-12 ">
            <form action="{{ url()->current()  }}" id="" name="" method="post"
                  enctype="multipart/form-data">
                @method('POST')
                {{ csrf_field() }}


                <div class="w-75 border p-3 mb-3">
                    <div class="mb-3 w-100">
                        <label for="href">Url</label>
                        <input type="text" class="form-control w-100" id="href" name="href"
                               value="{{ old('href', $link->href) }}" required>
                    </div>

                    <div class="mb-3 w-100">
                        <label for="name">Name</label>
                        <input type="text" class="form-control w-100" id="name" name="name"
                               value="{{ old('name', $link->name) }}" required>
                    </div>




                    <div class="mb-3 w-100">
                        <label for="fontawesomeClassInput" class="d-flex justify-content-between">
                            <span>Fontawesome Icon Class</span>
                            <a href="https://fontawesome.com/icons" target="_blank" class="text-success">
                                Icon suchen <i class="fa-solid fa-arrow-up-right-from-square"></i>
                            </a>
                        </label>
                        <div class="d-flex w-100">

                            @php
                            if(!$link->href  ){
                                // it is a new link

                                $link->color = $defaultColor;
                            }

                            @endphp

                            <input type="text" class="form-control w-100" id="fontawesomeClassInput"
                                   name="fontawesome_class"
                                   value="{{ old('fontawesome_class', $link->fontawesome_class) }}">
                            <i class="fa-2x  {{ old('fontawesome_class', $link->fontawesome_class) }} ms-2 "
                               id="fontawesomeIcon"></i>
                        </div>

                    </div>

                    <div>
                        <div class="square mb-3">
                            <label for="color" class="d-block">Farbe</label>
                            <input class="form-control" type="text" data-coloris name="color" id="color" value="{{$link->color}}">
                        </div>

                    </div>
                </div>


                <div class="d-flex justify-content-between w-75">

                    @php
                        $url = explode('/', url()->current());
                        array_pop($url);
                        $backLink = implode('/', $url);
                    @endphp

                    <a href="{{$backLink }}" class="btn btn-secondary">Zurück</a>
                    <button type="submit" class="btn btn-brown">Speichern</button>
                </div>


            </form>
        </div>

    </div>






    <script>
        const classInput = document.getElementById('fontawesomeClassInput')

        const fontawesomeIcon = document.getElementById('fontawesomeIcon')

        function checkAndAdapt() {

            let value = classInput.value;

            let regex = /"(.*?)"/;

            let match = regex.exec(value);

            if (match && match[1]) {
                value = match[1]
            }

            value = value.trim().replace(/\s+/g, ' ');

            classInput.value = value;

            fontawesomeIcon.className = ''

            fontawesomeIcon.classList.add(...classInput.value.split(' ') , 'fa-2x', 'ms-2')

        }
        classInput.addEventListener('change', () => checkAndAdapt())
    </script>

@endsection
