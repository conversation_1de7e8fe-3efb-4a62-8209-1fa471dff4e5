@extends('backend.freizeitlive.fz-master')

@section('fz-content')




    <div class="row mb-5">
        <div class="col-12 mb-3">
            <div class="d-flex justify-content-between">
                <h2>Links (index)</h2>
            </div>
        </div>

        <div class="col-12">

            <div class="d-flex justify-content-between">
                <div>
                    <a class="btn btn-brown" href="{{str_replace("/link","",url()->current())}}">Zurück</a>

                </div>
                <form action="{{ url()->current() }}" method="post">
                    {{ csrf_field() }}
                    <div class="form-group">
                        <button type="submit" class="btn btn-brown">
                            <i class="fa-solid fa-plus"></i> NEU
                        </button>
                    </div>
                </form>
            </div>

            <hr>


            <div class="sortable-js">
                @foreach($links as $link)

                    <div class="modal fade" id="delete-link-{{$link->id}}-modal"
                         aria-hidden="true">
                        <div class="modal-dialog" role="document">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title"><i class="fa-solid fa-trash"></i></h5>
                                    <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                                        <span aria-hidden="true">&times;</span>
                                    </button>
                                </div>
                                <div class="modal-body">
                                    <p>Wirklich löschen?</p>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Abbrechen
                                    </button>

                                    <form action="{{url()->current(). "/$link->id/delete" }}"
                                          method="post">
                                        {{ csrf_field() }}
                                        @method('DELETE')
                                        <input name="_method" type="hidden" value="delete">
                                        <button type="submit" class="btn btn-danger ms-3"><i
                                                class="fa-solid fa-trash"></i> Löschen
                                        </button>
                                    </form>


                                </div>
                            </div>
                        </div>
                    </div>



                    <div data-sortable-id="{{$link->id}}" class="d-flex align-items-center border-bottom pb-3 mb-3">

                        <div class="handle p-2 ">
                            <i class="fa-solid fa-up-down-left-right fs-2"></i>
                        </div>



                        <div class="ms-3 ">


                            @php
                                $style = ($link->color) ?  "color:$link->color!important;" : "";
                            @endphp


                            <div class="text-start">
                                <i  class="fa-2x  {{ $link->fontawesome_class }}  " id="fontawesomeIcon" ></i>

                              @if($style) <i class="ms-3 fa-solid fa-circle "  style="{!! $style !!}" ></i> {{$link->color}}@endif


                                <h4>{{$link->name}}</h4>

                            </div>

                            <div>
                                <a class="text-dark" href="{{$link->href}}" target="_blank">{{$link->href}} <i class="fa-solid fa-arrow-up-right-from-square"></i></a>
                            </div>
                        </div>


                        <div style="flex: 1" class="d-flex justify-content-end">
                            <a href="{{ url()->current(). "/$link->id"}}" class="btn btn-brown">
                                <i class="fa-regular fa-pen-to-square"></i> Bearbeiten
                            </a>


                            <button type="button" class="btn btn-danger ms-3" data-bs-toggle="modal"
                                    data-bs-target="#delete-link-{{$link->id}}-modal">
                                <i class="fa-solid fa-trash"></i> Löschen
                            </button>


                        </div>


                    </div>

                @endforeach
            </div>


        </div>

    </div>

    <style>
        .handle {
            cursor: grab;
        }

        .handle:hover {
            color: #07ED89;
        }
    </style>


    <script src="{{url('inc/sortableJS/sortableJS.js')}}"></script>

    <script>

        const endpoint = `{{url('backend/ajax/freizeitlive-link-sort')}}`;

        [...document.getElementsByClassName('sortable-js')].forEach(el => {

            Sortable.create(el, {
                ghostClass: 'ghost',
                direction: 'vertical',
                handle: '.handle',
                onEnd: () => {
                    /*
                    *  array of datasets [{sort: '0', id: '12'}]
                    */
                    const newOrder = Array.from(el.querySelectorAll(`[data-sortable-id]`))
                        .map((element, index) => {
                            return {
                                id: element.dataset.sortableId,
                                sort: index
                            }
                        })


                    fetch(endpoint,
                        {
                            method: 'POST',
                            headers: {
                                'X-CSRF-TOKEN': "{{ csrf_token() }}",
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({orderArray: newOrder}),

                        }
                    )
                        .then(response => response.json())
                        .then(json => console.log(json))
                        .catch((e) => {
                            // On Error reload the Page
                            console.log(e)
                            //location.reload()
                        });


                }
            })
        })


    </script>
@endsection
