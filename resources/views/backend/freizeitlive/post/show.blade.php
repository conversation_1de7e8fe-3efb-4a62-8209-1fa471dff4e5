@extends('backend.freizeitlive.fz-master')
@section('fz-content')
	<form action="{{ url("backend/freizeitlive/post/".$currentPost->id) }}"
	      method="post"
	      enctype="multipart/form-data">
		@method('POST')
		{{ csrf_field() }}

		<div class="row mb-5">
			<div class="col-12">
				<div class="d-flex justify-content-between align-items-center">
					<h2>Freizeitlive - Post</h2>
					@if($currentPost->fullPath)
						<a href="{{$currentPost->fullPath}}" target="_blank">Post ansehen
							<i class="fa-solid fa-arrow-up-right-from-square link-primary"></i></a>
					@endif
				</div>
			</div>

			<div class=" col-lg-8 col-12">


				@php
					$imgOne = $currentPost->imageOneFullPath;
					$imgTwo = $currentPost->imageTwoFullPath;
				@endphp

				<div class="mb-3 border p-3">
					<div class="d-flex">
						@if($imgOne)
							<div class="w-50 pe-3">
								<img src="{{url($imgOne)}}" alt=""
								     class="img-fluid">
							</div>
						@endif
						<div class="d-flex flex-column">
							<div class="mb-3">
								<label for="imageOne"> <b>Hauptbild</b> {!! ($imgOne) ? 'austauschen' : ''!!}</label>
								<input type="file" class="form-control" id="imageOne" name="image_one"
								       accept="image/*">
							</div>
							<div class="mb-3">
								<label for="imageOne_CR">Copyright</label>
								<input type="text" class="form-control" id="imageOne_CR" name="image_one_cr"
								       value="{{ old('image_one_cr', $currentPost->image_one_cr) }}">
							</div>
							<div class="mb-3">
								<label for="imageOne_ALT">ALT</label>
								<input type="text" class="form-control" id="imageOne_ALT" name="image_one_alt"
								       value="{{ old('image_one_alt', $currentPost->image_one_alt) }}">
							</div>
						</div>
					</div>
				</div>


				<div class="mb-3 border p-3 ">
					<div class="d-flex ">
						@if($imgTwo)
							<div class="w-50 pe-3">
								<img src="{{url($imgTwo)}}" alt=""
								     class="img-fluid">
							</div>
						@endif
						<div class="" style="flex:1">
							<div class="mb-3">
								<label for="imageOne"><b>Nebenbild</b> {!! ($imgTwo) ? 'austauschen' : ''!!}</label>
								<input type="file" class="form-control" id="imageTwo" name="image_two"
								       accept="image/*">
							</div>
							<div class="mb-3">
								<label for="imageTwo_CR">Copyright</label>
								<input type="text" class="form-control" id="imageTwo_CR" name="image_two_cr"
								       value="{{ old('image_two_cr', $currentPost->image_two_cr) }}">
							</div>
							<div class="mb-3">
								<label for="imageTwo_ALT">ALT</label>
								<input type="text" class="form-control" id="imageTwo_ALT" name="image_two_alt"
								       value="{{ old('image_two_alt', $currentPost->image_two_alt) }}">
							</div>
							@if($imgTwo)
								<a href="{{url("backend/freizeitlive/post/{$currentPost->id}/delete-image/two")}}" class="btn btn-danger pb-0"><i class="fas fa-times me-2"></i>Foto löschen</a>
							@endif
						</div>
					</div>
				</div>


				<div class="mb-3">
					<label for="colorSelector" class="form-label">Type:</label>
					<select class="form-select" id="colorSelector" name="post_type">
						<option value="" data-color=""> -- auswählen --</option>
						@foreach ($postTypes as $type)
							<option
								data-color="{{$type->color_2}}"
								value="{{ $type->id }}" {{($currentPost->postType && ($type->id === $currentPost->postType->id)) ? 'selected' : ''}}>{{ $type->name }}</option>
						@endforeach
					</select>
				</div>
				<div class="mb-3">
					<label for="title">Titel</label>
					<input type="text" class="form-control w-100" id="title" name="title"
					       value="{{ old('title', $currentPost->title) }}">
				</div>


				<div class="mb-3">
					<label for="seo_url">Seo Url
						<small class="text-muted">(Wird automatisch aus dem Titel generiert,
						                          wenn nichts eingegeben wurde)
						</small>
					</label>
					<input type="text" class="form-control w-100" id="seo_url" name="seo_url"
					       value="{{ old('seo_url', $currentPost->seo_url) }}">
				</div>


				<div class="mb-3">
					<label for="desc_short">Kurze Beschreibung</label>
					<textarea type="text" class="form-control w-100" id="desc_short"
					          style="height: 100px;"
					          name="desc_short">{{ old('desc_short', $currentPost->desc_short) }}</textarea>
				</div>

				<div class="mb-3">
					<label for="desc_long">Lange Beschreibung</label>

					<textarea
						ckeditor="true"
						ckeditor_height="400"
						ckeditor_removeButtons="{{env('CKEDITOR_ELEMENTS_MIDI')}}"
						id="desc_long" class="form-control mb-3 " name="desc_long">{{ old('desc_long', $currentPost->desc_long) }}
							</textarea>


				</div>


				<div class="d-flex justify-content-between">
					<a href="{{url('/backend/freizeitlive/post')}}" class="btn btn-secondary">Zurück</a>
					<button type="submit" class="btn btn-brown">Speichern</button>
				</div>


			</div>

			@if($currentPost->title)
				{{--if post is saved--}}
				<div class="col-lg-4">
					<div class="border p-3">


						<div class="d-flex justify-content-between border-bottom mb-3 align-items-center">
							<h2 class="">Links</h2>
							<div>
								<a href="{{url()->current() . "/link"}}" class="btn btn-sm btn-brown">Bearbeiten</a>

							</div>
						</div>

						<div>
							@foreach($currentPost->links()->get() as $link)
								<div class="d-flex align-items-center mb-3">
									<i class="{{$link->fontawesome_class}} fa-xl me-2 "></i>
									<div>{{$link->name}}</div>
								</div>
							@endforeach

							@if($currentPost->links()->count())
								<div class="form-check">
									<input class="form-check-input" type="checkbox" id="toggleTextInput"
									       @if($currentPost->uniqueLinksColor()) checked @endif
									       name="all_links_same_color"
									>
									<label class="form-check-label" for="toggleTextInput">
										Gleiche Farbe für alle Links
									</label>

									<div class="square mb-3" id="colorInput">

										<input class="form-control"
										       type="text"
										       data-coloris
										       name="all_links_color"
										       id="links-color-input"
										       value="{{$currentPost->uniqueLinksColor()}}">

										<button type="button" id="setLinkColorBtn" class="btn btn-sm btn-brown mt-2 ">
											Beitrags Farbe übernehmen
										</button>
									</div>


								</div>
							@endif


						</div>


					</div>

				</div>
			@endif
		</div>
	</form>



	<style>
		/* Hide the text input by default */
		#colorInput {
			visibility: hidden;
		}

		/* Show the text input when the checkbox is checked */
		#toggleTextInput:checked + label + #colorInput {
			visibility: visible;
		}
	</style>





	<script>

		const colorSelector = document.getElementById('colorSelector')

		const setLinkColorBtn = document.getElementById('setLinkColorBtn')

		let color;

		function update(){
			try{
				color = colorSelector.options[colorSelector.options.selectedIndex].dataset.color;

			}catch(e){
				console.log(e)
			}

		}

		update();

		setLinkColorBtn.addEventListener('click', () => {

			document.getElementById('links-color-input').value = color;
			document.getElementById('links-color-input').dispatchEvent(new Event('input', { bubbles : true }));
		})

		colorSelector.addEventListener('change', () => update())
	</script>

@endsection
