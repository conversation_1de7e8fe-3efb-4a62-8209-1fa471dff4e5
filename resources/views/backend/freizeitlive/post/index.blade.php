@extends('backend.freizeitlive.fz-master')

@section('fz-content')
    <div class="row mb-5">
        <div class="col-12 mb-3">
            <div>
                <h2>Freizeitlive - Posts</h2>
            </div>
        </div>
        <div class="col-12">
            <div class="d-flex justify-content-between">
                <div>
                    <a class="btn btn-brown" href="{{url('backend/freizeitlive')}}">Zurück</a>
                </div>
                <form action="{{ url("backend/freizeitlive/post") }}" method="post">
                    {{ csrf_field() }}
                    <div class="form-group">
                        <button type="submit" class="btn btn-brown">
                            <i class="fa-solid fa-plus"></i> NEU
                        </button>
                    </div>
                </form>
            </div>
            <hr>
            <div class="sortable-js">
                @foreach($posts as $post)
                    <div class="modal fade" id="delete-post-{{$post->id}}-modal"
                         aria-hidden="true">
                        <div class="modal-dialog" role="document">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title"><i class="fa-solid fa-trash"></i></h5>
                                    <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                                        <span aria-hidden="true">&times;</span>
                                    </button>
                                </div>
                                <div class="modal-body">
                                    <p>Wirklich löschen?</p>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Abbrechen
                                    </button>

                                    <form action="{{ url("backend/freizeitlive/post/".$post->id."/delete") }}"
                                          method="post">
                                        {{ csrf_field() }}
                                        <input name="_method" type="hidden" value="delete">
                                        <button type="submit" class="btn btn-danger ms-3"><i
                                                class="fa-solid fa-trash"></i> Löschen
                                        </button>
                                    </form>


                                </div>
                            </div>
                        </div>
                    </div>

                    <div data-sortable-id="{{$post->id}}" class="d-flex align-items-center border-bottom p-3 mb-3 {{ ($post->active==1) ? "" : "bg-light" }}" title="{{ ($post->active==1) ? "Beitrag ist eingeblendet" : "Beitrag ist ausgeblendet" }}">
                        <div class="handle p-2 ">
                            <i class="fa-solid fa-up-down-left-right fs-2"></i>
                        </div>
                        <div class="ms-2" style="max-width: 15rem;">
                            <img src="{{$post->imageOneFullPath}}" alt="..." class="mw-100">
                        </div>
                        <div class="mx-4">
                            <h4>{{$post->title}}</h4>
                            <div>({{($post->postType) ? $post->postType->name : 'No type'}})</div>
                        </div>
                        <div style="flex: 1" class="d-flex justify-content-end">
                            <a href="{{ 'https://freizeitlive.kurier.at/post/'.$post->id.'/' . $post->seo_url.'?vorschau=true' }}" target="blank" class="btn btn-warning me-2" title="Vorschau anzeigen">
                                <i class="fa-regular fa-link"></i>
                            </a>
                            @if($post->active==1)
                                <a href="{{url('backend/freizeitlive/post/'.$post->id.'/active/'.$post->active)}}" class="btn btn-primary me-2" title="Ausblenden">
                                    <i class="fa-solid fa-eye-slash"></i>
                                </a>
                            @else
                                <a href="{{url('backend/freizeitlive/post/'.$post->id.'/active/'.$post->active)}}" class="btn btn-info text-white me-2" title="Einblenden">
                                    <i class="fa-solid fa-eye"></i>
                                </a>
                            @endif
                            <a href="{{url('backend/freizeitlive/post/' . $post->id)}}" class="btn btn-brown me-2" title="Bearbeiten">
                                <i class="fa-regular fa-pen-to-square"></i>
                            </a>
                            <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#delete-post-{{$post->id}}-modal" title="Löschen">
                                <i class="fa-solid fa-trash"></i>
                            </button>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>

    <style>
        .handle {
            cursor: grab;
        }

        .handle:hover {
            color: #07ED89;
        }
    </style>

    <script src="{{url('inc/sortableJS/sortableJS.js')}}"></script>

    <script>

        const endpoint = `{{url('backend/ajax/freizeitlive/post/sort')}}`;

        [...document.getElementsByClassName('sortable-js')].forEach(el => {

            Sortable.create(el, {
                ghostClass : 'ghost',
                direction : 'vertical',
                handle : '.handle',
                onEnd : () => {
                    /*
                    *  array of datasets [{sort: '0', id: '12'}]
                    */
                    const newOrder = Array.from(el.querySelectorAll(`[data-sortable-id]`))
                                          .map((element, index) => {
                                              return {
                                                  id : element.dataset.sortableId,
                                                  sort : index
                                              }
                                          })


                    fetch(endpoint,
                          {
                              method : 'POST',
                              headers : {
                                  'X-CSRF-TOKEN' : "{{ csrf_token() }}",
                                  'Content-Type' : 'application/json'
                              },
                              body : JSON.stringify({ orderArray : newOrder }),

                          }
                    )
                        .then(response => response.json())
                        .then(json => console.log(json))
                        .catch((e) => {
                            // On Error reload the Page
                            console.log(e)
                            //location.reload()
                        });


                }
            })
        })


    </script>
@endsection
