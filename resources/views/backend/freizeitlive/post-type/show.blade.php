@extends('backend.freizeitlive.fz-master')
@section('fz-content')

    <div class="row mb-5">
        <div class="col-12">
            <div>
                <h2>Freizeitlive - Post</h2>
            </div>
        </div>

        <div class="  col-lg-9 col-12">
            <form action="{{ url("backend/freizeitlive/post-type/".$currentPost->id) }}" id="" name=""
                  method="post"
                  enctype="multipart/form-data">
                @method('POST')
                {{ csrf_field() }}






                <div class="d-flex">
                    <div class="mb-3 pe-3" style="flex: 1">
                        <label for="name">Name</label>
                        <input type="text" class="form-control w-100" id="name" name="name"
                               value="{{ old('title', $currentPost->name) }}">
                    </div>


                    <div>
                        <div class="square mb-3 pe-3">
                            <label for="color" class="d-block">Farbe (background)</label>
                            <input class="form-control" type="text" data-coloris name="color" id="color" value="{{$currentPost->color}}">
                        </div>
                    </div>

                    <div>
                        <div class="square mb-3">
                            <label for="color2" class="d-block">Farbe (text)</label>
                            <input class="form-control" type="text" data-coloris name="color_2" id="color2" value="{{$currentPost->color_2}}">
                        </div>
                    </div>


                </div>


                <div class="d-flex justify-content-between">
                    <a href="{{url('/backend/freizeitlive/post-type')}}" class="btn btn-secondary">Zurück</a>
                    <button type="submit" class="btn btn-brown">Speichern</button>
                </div>


            </form>
        </div>

    </div>




@endsection
