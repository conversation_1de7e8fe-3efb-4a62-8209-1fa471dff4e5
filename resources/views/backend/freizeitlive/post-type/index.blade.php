@extends('backend.freizeitlive.fz-master')

@section('fz-content')

    <div class="row mb-5">
        <div class="col-12 mb-3">
            <div>
                <h2>Freizeitlive - Post Types</h2>
            </div>
        </div>

        <div class="col-12">

            <div class="d-flex justify-content-between">
                <div>
                    <a class="btn btn-brown" href="{{url('backend/freizeitlive')}}">Zurück</a>

                </div>
                <form action="{{ url("backend/freizeitlive/post-type") }}" method="post">
                    {{ csrf_field() }}
                    <div class="form-group">
                        <button type="submit" class="btn btn-brown">
                            <i class="fa-solid fa-plus"></i> NEU
                        </button>
                    </div>
                </form>
            </div>

            <hr>


            <div class="sortable-js">
                @foreach($postTypes as $post)

                    <div class="modal fade" id="delete-post-{{$post->id}}-modal"
                         aria-hidden="true">
                        <div class="modal-dialog" role="document">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title"><i class="fa-solid fa-trash"></i></h5>
                                    <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                                        <span aria-hidden="true">&times;</span>
                                    </button>
                                </div>
                                <div class="modal-body">
                                    <div>Wirklich löschen?</div>
                                    @if($count = $post->posts()->count())
                                        <div class="alert alert-danger fs-5 mt-1">
                                            <h2>Achtung!</h2>
                                            Es gibt {{$count}} {!! ($count === 1) ? 'Beitrag' : 'Beiträge' !!} dieser
                                            Art ({{$post->name}})!!

                                            <ul class="list-group ps-3">
                                                @foreach($post->posts()->get() as $child)
                                                    <li>
                                                        <a class="link-danger fw-bold" href="{{url('backend/freizeitlive/post/' . $child->id)}}" target="_blank">
                                                            {{$child->title}}
                                                        </a>
                                                    </li>
                                                @endforeach
                                            </ul>

                                        </div>
                                    @endif
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Abbrechen
                                    </button>

                                    <form action="{{ url("backend/freizeitlive/post-type/".$post->id."/delete") }}"
                                          method="post">
                                        {{ csrf_field() }}
                                        <input name="_method" type="hidden" value="delete">
                                        <button type="submit" class="btn btn-danger ms-3"><i
                                                class="fa-solid fa-trash"></i> Löschen
                                        </button>
                                    </form>


                                </div>
                            </div>
                        </div>
                    </div>



                    <div class="d-flex align-items-center border-bottom pb-3 mb-3">


                        <div class="ms-3">
                            <div class="fs-5 d-flex align-items-center">

                                <div style="min-width: 10rem">{{$post->name}}</div>



                                <div>
                                  <span class="fa-stack fa-lg">
                                    <i class="fa-solid fa-circle fa-stack-2x" style="color: {{$post->color}}"></i>
                                    <i class="fa-solid fa-brush fa-stack-1x fa-inverse"
                                       style="color: {{$post->color_2}}"></i>
                                  </span>
                                </div>
                            </div>
                        </div>


                        <div style="flex: 1" class="d-flex justify-content-end">
                            <a href="{{url('backend/freizeitlive/post-type/' . $post->id)}}" class="btn btn-brown">
                                <i class="fa-regular fa-pen-to-square"></i> Bearbeiten
                            </a>


                            <button type="button" class="btn btn-danger ms-3" data-bs-toggle="modal"
                                    data-bs-target="#delete-post-{{$post->id}}-modal">
                                <i class="fa-solid fa-trash"></i> Löschen
                            </button>


                        </div>


                    </div>

                @endforeach
            </div>


        </div>

    </div>

@endsection
