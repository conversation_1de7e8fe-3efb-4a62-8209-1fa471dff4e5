@extends('backend.master')

@section('content')
{{--	<div class="card">
		<div class="card-body">--}}
			<div class="row my-4">
				<div class="col-12">
					<h2 class="card-title">User-Verwaltung</h2>
				</div>

				<div class="col-12 mt-4 px-0">
					<table class="table table-striped table-vertical-middle table-bordered p-0"
                           datatable="true"
                           data-paging="false"
                           data-order="[[5, &quot;desc&quot; ]]">
						<thead class="table-dark ">
						<tr>
							<th data-orderable="false">Aktiv</th>
							<th data-orderable="true">Nachname</th>
							<th data-orderable="true">Vorname</th>
							<th data-orderable="true">Username</th>
							<th data-orderable="true">Email</th>
							<th data-orderable="true">letztes Login</th>
							<th data-orderable="false"></th>
						</tr>
						</thead>
						<tbody>
						@foreach($users as $tmp)
							<tr class="{{ ($tmp->deleted_at) ? "text-muted" : "" }}">
								<td>{!!  ($tmp->active) ? "<i class='far fa-lg fa-user-check text-success'></i>" : "<i class='far fa-lg fa-user'></i>" !!}</td>
								<td>{{ $tmp->lastname }}</td>
								<td>{{ $tmp->firstname }}</td>
								<td>{{ $tmp->username }}</td>
								<td>{{ $tmp->email }}</td>

								<td data-sort="{{(!empty($tmp->last_login_at))? $tmp->last_login_at->format("Y-m-d H:i:s"): "0000-00-00"}}">{{ ($tmp->last_login_at) ? $tmp->last_login_at->format('d.m.Y H:i:s') : '' }}</td>
								<td>
									@if($tmp->deleted_at)
										<a href="{{ url("backend/user/".$tmp->id."/restore") }}" title="Wiederherstellen">
											<span class="btn btn-info">
													<span class="fas fa-sync"></span>
											</span>
										</a>
									@else
										<button type="button" class="btn btn-danger" confirmUrl="{{url("backend/user/".$tmp->id."/delete")}}" confirmTxt="User &quot;{{ $tmp->lastname }}&quot; wirklich löschen?" title="Löschen">
											<span class="fas fa-trash"></span>
										</button>
										<a href="{{ url("backend/user/".$tmp->id."/edit") }}" title="Bearbeiten">
										<span class="btn btn-secondary">
											<span class="fas fa-cog"></span>
										</span>
										</a>
									@endif
								</td>
							</tr>
						@endforeach
						</tbody>
					</table>
				</div>
			</div>
{{--		</div>
	</div>--}}
@endsection
