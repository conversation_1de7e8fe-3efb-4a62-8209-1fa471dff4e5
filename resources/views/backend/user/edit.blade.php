@extends('backend.master')

@section('content')
    <div class="card">
        <div class="card-body">
            <div class="row mb-4">
                <div class="col-12">
                    <h2 class="card-title">User bearbeiten</h2>
                </div>
            </div>
            <form action="{{ url("backend/user/".$user->id) }}" id="userForm" name="userForm" method="post">
                {{ csrf_field() }}
                {{ method_field('put') }}
                <div class="row">
                    <div class="col-12 col-md-6">
                        <div class="form-group">
                            <label for="lastname">Nachname *</label>
                            <input type="text" class="form-control mb-3 " id="lastname" name="lastname"
                                   value="{{ old("lastname", $user->lastname) }}"/>
                            {!!  ($errors->has("lastname")) ? "<small class='form-text text-danger'><span class='fas fa-exclamation-circle'></span> ".join('<br/>', $errors->get('lastname'))."</small>" : "" !!}
                        </div>
                    </div>
                    <div class="col-12 col-md-6">
                        <div class="form-group">
                            <label for="firstname">Vorname *</label>
                            <input type="text" class="form-control mb-3 " id="firstname" name="firstname"
                                   value="{{ old("firstname", $user->firstname) }}"/>
                            {!!  ($errors->has("firstname")) ? "<small class='form-text text-danger'><span class='fas fa-exclamation-circle'></span> ".join('<br/>', $errors->get('firstname'))."</small>" : "" !!}
                        </div>
                    </div>
                    <div class="col-12 col-md-6">
                        <div class="form-group">
                            <label for="email">E-Mail *</label>
                            <input type="email" class="form-control mb-3 " id="email" name="email"
                                   value="{{ old("email", $user->email) }}"/>
                            {!!  ($errors->has("email")) ? "<small class='form-text text-danger'><span class='fas fa-exclamation-circle'></span> ".join('<br/>', $errors->get('email'))."</small>" : "" !!}
                        </div>
                    </div>
                    <div class="col-12 col-md-6">
                        <div class="form-group mt-3">
                            <label for="active">&nbsp;</label><br>
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input" id="active" name="active" value="1"
                                       onfocus="this.blur()" {{($user->active) ? 'checked' : ''}}>
                                <label class="custom-control-label" for="active">Account aktiv</label>
                            </div>
                        </div>
                    </div>

                    <div class="col-12">
                        <hr>
                    </div>


                    <div class="col-12 ">
                        <h3 class="mb-4">
                            Berechtigungen
                        </h3>
                        <div class="row">
                            @foreach(\App\Models\Permission::generatePermissionsChecklist() as $perms)

                                <div class="col-lg-4 mb-3">
                                    <h4>{{$perms['group']}}</h4>
                                    <hr>

                                    @foreach($perms['items'] as $item)
                                        <div class="custom-control custom-switch mb-1">

                                            <input type="checkbox" class="custom-control-input"
                                                   id="permissions_{{$item->id}}" name="permissions[]"
                                                   value="{{ $item->id }}"
                                                   onfocus="this.blur()" {{($user->hasPermission($item->url)) ? 'checked' : ''}}>
                                            <label class="custom-control-label"
                                                   for="permissions_{{$item->id}}">{{$item->description}}</label>
                                        </div>
                                    @endforeach

                                </div>

                            @endforeach

                        </div>
                    </div>


                    {{--<div class="col-lg-6 col-12 ">
                        <div class="form-group">
                            <label class="fw-bold">Berechtigungen</label><br>
                            <div class="alert alert-light">
                                @foreach($permissions as $mtPermission)
                                    <div class="custom-control custom-switch mb-1">

                                        <input type="checkbox" class="custom-control-input"
                                               id="permissions_{{$mtPermission->id}}" name="permissions[]"
                                               value="{{ $mtPermission->id }}"
                                               onfocus="this.blur()" {{($user->hasPermission($mtPermission->url)) ? 'checked' : ''}}>
                                        <label class="custom-control-label"
                                               for="permissions_{{$mtPermission->id}}">{{$mtPermission->description}}</label>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-6 col-12 ">
                        <div class="form-group">
                            <label class="fw-bold">Mediathek Berechtigungen</label><br>
                            <div class="alert alert-light">
                                @foreach($mediathekPermissions as $mtPermission)
                                    <div class="custom-control custom-switch mb-1">

                                        <input type="checkbox" class="custom-control-input"
                                               id="permissions_{{$mtPermission->id}}" name="permissions[]"
                                               value="{{ $mtPermission->id }}"
                                               onfocus="this.blur()" {{($user->hasPermission($mtPermission->url)) ? 'checked' : ''}}>
                                        <label class="custom-control-label"
                                               for="permissions_{{$mtPermission->id}}">
                                            {{$mtPermission->description}}
                                            @if(\Illuminate\Support\Str::contains($mtPermission->description, 'Admin'))
                                                <b>(Vorschau-Berechtigungen sind enthalten)</b>
                                            @endif
                                        </label>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>--}}


                    <div class="col-12">
                        <hr>
                    </div>
                    <div class="col-12 col-md-6">
                        <a href="{{ url("backend/user") }}">
                            <span class="btn btn-secondary">
                                <span class="fas fa-angle-double-left "></span> Zurück
                            </span>
                        </a>
                    </div>
                    <div class="col-12 col-md-6 text-end">
                        <button type="submit" class="btn btn-success">
                            <span class="fas fa-save"></span>
                            Speichern
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
@endsection
