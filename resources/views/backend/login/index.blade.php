@extends('backend.master')

@section('content')
    <div class="row">
        <div class="col-12  mt-4 w-100 d-flex justify-content-center">
            <div class="card w-100 border" style="max-width: 40rem;">
                <div class="card-body ">
                    <h3 class="card-title mb-4">Anmelden</h3>

                    @include("backend.messages.alert")

                    <form action="{{ url()->current() }}" method="post" name="loginForm" id="loginForm">
                        {{ csrf_field() }}
                        <div class="form-group">
                            <input autocomplete="off" type="username" class="form-control mb-3  {{ ($errors->has("username")) ? 'danger' : '' }}"
                                   value="{{old('username')}}" id="username" name="username" placeholder="Windows Username *"/>
                        </div>
                        <div class="form-group">
                            <input autocomplete="off" type="password" class="form-control mb-3 " id="password" name="password"
                                   placeholder="Windows Passwort *"/>
                        </div>



                        <div class="form-group">
                            <button type="submit" class="btn btn-success btn-block">Anmelden</button>
                        </div>


                    </form>
                </div>
                @if($allowed_ip)
                    <div class="card-footer  text-center">
                        <p class="mb-0">
                            <i class="fas fa-key text-success"></i>
                            Authentifizierung ohne 2 Faktor,<br> da Sie sich im internen Netzwerk befinden.
                        </p>
                    </div>
                @endif


            </div>
        </div>
    </div>
    <small class="d-block  text-center mt-2">IP: {{\Request::ip()}}</small>

    @if(session()->get("redirectUrl"))
        <div class="col-12 mt-3 text-center">
            Sie wollen zu {{session()->get("redirectUrl")}}
        </div>
    @endif

@endsection
