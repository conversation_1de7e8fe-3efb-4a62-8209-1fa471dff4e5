@php use Illuminate\Support\Str; @endphp
@extends('backend.master')


@section('content')
    <div class="containers">
        <div class="row">
            <div class="col my-5">
                <h1 class="card-title">Geocoder Api (Photon)</h1>
            </div>
        </div>
        <div class="col-lg-5">
            <div class="border p-3">
                <form action="{{ url()->current() . "/run" }}" method="post" enctype="multipart/form-data">
                    {{ csrf_field() }}
                    <div class="mb-3">
                        <label for="formFile" class="form-label fw-bold">Excel-Datei </label>
                        <input class="form-control" type="file" id="formFile" name="excelFile" required>
                        <small class="fw-normal">(Die Spalten <b>{{$requiredKeysString}}</b> müssen enthalten sein) </small>
                    </div>


                    <input id="input-uuid" type="hidden" name="uuid" value="{{Str::uuid()}}">


                    <b>Optionen</b>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" value="" name="rawResponse" id="rawResponse">
                        <label class="form-check-label" for="rawResponse">
                            Unformatierte Antwort einbeziehen
                        </label>
                    </div>
                    <div class="mb-3  ">

                            <label  class="form-label" for="limit" style="white-space: nowrap">
                                Unformatierte Antwort Limit (1-5)
                            </label>

                        <div >
                            <input class="form-control" type="number" max="5" min="1" step="1" value="1" name="limit" id="limit">

                        </div>

                    </div>
                    <b>Mode</b>
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="showMode" id="showMode1" value="excel"
                               checked>
                        <label class="form-check-label" for="showMode1">
                            Als Excel-Datei herunterladen
                        </label>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="radio" name="showMode" id="showMode2" value="table">
                        <label class="form-check-label" for="showMode2">
                            In einer Tabelle anzeigen
                        </label>
                    </div>

                    <div id="loading" class="mb-3 d-none">
                        <div class="d-flex justify-content-center mb-1">
                            <div class="fs-5 fw-bold me-1" id="loading-percentage">
                                <div class="spinner-border text-primary" role="status">
                                </div>
                            </div>

                        </div>

                        <div class="progress-stacked">
                            <div class="progress" role="progressbar" aria-label="Basic example" aria-valuenow="25"
                                 aria-valuemin="0" aria-valuemax="100">
                                <div id="progress-bar" class="progress-bar" style="width: 0%"></div>
                            </div>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-brown w-100">Starten</button>
                </form>
            </div>

        </div>
    </div>


    <script>

        function updateProgressBar() {

            const uuid = document.querySelector('#input-uuid').value;

            fetch(`/backend/ajax/geocoder-api/progress/${uuid}`)
                .then(response => response.json())
                .then(data => {


                    const percentage = data.progress + "%";
                    document.querySelector('#loading-percentage').innerHTML = percentage;
                    document.getElementById('progress-bar').style.width = percentage;

                    if (data.progress < 100) {

                        setTimeout(updateProgressBar, 1500);
                    } else {
                        console.log('Process completed');
                        setTimeout(() => {
                            location.reload()
                        }, 300)
                    }
                });
        }


        const form = document.querySelector('form')

        form.onsubmit = () => {
            form.querySelector('#loading').classList.remove('d-none');
            form.querySelector('button[type="submit"]').classList.add('d-none');

            updateProgressBar();
        }


    </script>

@endsection
