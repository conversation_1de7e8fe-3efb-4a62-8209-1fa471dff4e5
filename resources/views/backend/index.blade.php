@extends('backend.master')

@section('content')

    <div class="row my-4">


        <div class="col-12">
            <h2>Start</h2>
            <hr>
            <div class=" row">

                @php
                    $webChatAgent = App\Facades\AdminUser::get()->webChatAgent()->first();
                @endphp

                @if($webChatAgent && $webChatAgent->active)
                    <div class="col-lg-12 col-md-6 col-12  mb-3">
                        <div class="card h-100 bg-brown2 rounded-custom border-0">
                            <div class="card-body ">
                                <i class="fa-regular fa-message fa-2x"></i> <span class="card-title h2">Webchat</span>
                                <p class="card-text mt-4">Zum Webchat Login.</p>
                            </div>
                            <div class="card-footer bg-none  border-top-0">
                                <a href="{{url("backend/webchat-login")}}" >
                                    <span class="btn mt-2 btn-brown">Login</span>
                                </a>
                            </div>
                        </div>
                    </div>
                @endif

                <div class="col-lg-12 col-md-6 col-12  mb-3">
                    <div class="card h-100 bg-brown2 rounded-custom border-0">
                        <div class="card-body ">
                            <i class="far fa-2x fa-question-circle"></i> <span
                                class="card-title h2">Fragen & Antworten</span>
                            <p class="card-text mt-4">Hier finden sie in viele Antworten rund um das Thema Telearbeit
                                bzw. Anleitungen, Tipps & Tricks.</p>
                        </div>
                        <div class="card-footer bg-none  border-top-0">
                            <a href="{{url("backend/faq")}}" target="_blank">
                                <span class="btn mt-2 btn-brown">Weiter zu den FAQ</span>
                            </a>
                        </div>
                    </div>
                </div>


                <div class="col-lg-12 col-md-6 col-12  mb-3">
                    <div class="card h-100 bg-brown2 rounded-custom border-0">
                        <div class="card-body">
                            <i class="far fa-2x fa-newspaper"></i><span class="card-title h2"> Mediathek</span>
                            <p class="card-text mt-4">Unsere Tageszeitungen inklusive aller Journale finden Sie
                                hier.</p>
                        </div>
                        <div class="card-footer bg-none border-top-0">
                            <a href="{{url("backend/mediathek")}}" target="_blank">
                                <span class="btn mt-2 btn-brown">Weiter zur Mediathek</span>
                            </a>
                        </div>
                    </div>
                </div>


                <div class="col-lg-6 col-md-6 col-12  mb-3">
                    <div class="card h-100 bg-brown2 rounded-custom border-0">
                        <div class="card-body">
                            <i class="far fa-2x fa-users"></i><span class="card-title h2"> HR Informiert</span>
                            <div class="card-text mt-4"><b>5 Tipps für ...</b><br><br>
                                <a href="backend/faq/download/16" target="_blank"><i class="fas fa-download"></i>
                                    erfolgreiche Videokonferenzen</a>
                                <hr>
                                <a href="backend/faq/download/14" target="_blank"><i class="fas fa-download"></i>
                                    produktives Arbeiten von Zuhause</a>
                                <hr>
                                <a href="backend/faq/download/15" target="_blank"><i class="fas fa-download"></i>
                                    gesundes Arbeiten von Zuhause</a>
                            </div>
                        </div>
                    </div>

                </div>


                <div class="col-lg-6 col-md-6 col-12  mb-3">
                    <div class="card h-100 bg-brown2 rounded-custom border-0">
                        <div class="card-body">
                            <i class="far fa-2x fa-headset"></i><span class="card-title h2"> IT-ServiceDesk</span>
                            <p class="card-text mt-4">
                                Servicezeiten: Mo-Fr 7:00-18:00<br>
                                Telefon:
                                <a href="tel:05 1727 21345">05 1727 21345</a>
                                <br>
                                Mail:
                                <a href="mailto:<EMAIL>"><EMAIL></a>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <div class="col-md-12 col-12 ">
            <div class="alert alert-warning">
                Die Telefonbuch-Funktion wurde entfernt. Ab sofort können Sie diejenige auf <a href="https://inside.mediaprint.co.at/">https://inside.mediaprint.co.at/</a>
                verwenden.

            </div>
        </div>


        {{--<div class="col-md-12 col-12 mb-3">
            <div class="card h-100 bg-brown2 rounded-custom border-0">
                <div class="card-body">
                    <i class="far fa-2x  fa-phone-rotary"></i><span class="card-title h2"> Telefonbuch</span>

                    <div class="row">
                        <div class="col-12 col-md-5 text-start mt-4 pb-4">
                            <input class="w-90" id="basics"/>
                        </div>
                        <div class="col-12 col-md-7 ps-1">
                            <div class="phone-name font-weight-bold"></div>
                            <hr class="phone-hr  display-none">
                            <div class="phone-company"></div>

                            <hr class="phone-hr  display-none">
                            <table class="w-100 ">
                                <tr class=" display-none">
                                    <td class="w-8"><i class="fal fa-phone"></i></td>
                                    <td class="phone-number"></td>
                                </tr>

                                <tr class=" display-none">
                                    <td class="w-8"><i class="fal fa-mobile"></i></td>
                                    <td class="phone-mobile"></td>
                                </tr>

                                <tr class=" display-none">
                                    <td class="w-8"><i class="fal fa-envelope "></i></td>
                                    <td class="phone-mail"></td>
                                </tr>
                            </table>

                        </div>
                    </div>
                </div>
            </div>
        </div>--}}
        <div class="col-md-12 col-12 mb-3">
            <div class="card h-100 bg-brown2 rounded-custom border-0">

                <div class="card-body">
                    <i class="far fa-2x  fa-exclamation-triangle text-danger"></i><span class="card-title h2"> Vorsicht bitte!</span>
                    <div class="card-text mt-4">
                        <p>
                            Aufgrund vermehrten Teleworkings sind wieder sehr <b>viele Phishing Mails</b> unterwegs.<br>
                            <b>Bitte achten Sie unbedingt darauf, dass Sie Ihre Zugangsdaten auf keinen Fall auf
                                betriebsfremden Websites oder Apps
                                eingeben!</b><br>
                            Im Zweifelsfall fragen Sie bitte unbedingt vorher den Servicedesk.
                        </p>
                        <p>
                            <a href="backend/faq/download/4" target="_blank"><i class="fas fa-download"></i> PDF mit
                                weiteren Informationen</a>
                        </p>
                    </div>
                </div>
            </div>


        </div>
    </div>





    <script>
        $(function () {

            var options = {
                url: function () {
                    return "storage/app/public/phone_{{now()->dayOfWeek}}.json";
                },

                getValue: function (element) {
                    return element.nachname + " " + element.vorname;
                },
                placeholder: "Suche nach Name",
                highlightPhrase: true,
                list: {
                    maxNumberOfElements: 10,
                    match: {
                        enabled: true
                    },
                    onSelectItemEvent: function () {
                        $(".phone-user").hide();
                        $(".phone-hr").show();
                        var employee = $("#basics").getSelectedItemData();

                        $(".phone-name").html(employee.title_pre + " " + employee.vorname + " " + employee.nachname + employee.title_post);

                        var company = employee.json.info.abteilung;
                        if (employee.json.info.funktion) company = company + "<br>" + employee.json.info.funktion;

                        if (employee.json.info.funktion == employee.json.info.abteilung) company = employee.json.info.abteilung;

                        $(".phone-company").html(company);

                        $(".phone-mobile").html("");
                        $(".phone-mobile").closest("tr").hide();
                        if (employee.json.contact.mobil != 'undefined') {
                            $(employee.json.contact.mobil).each(function (index, item) {
                                var number = item.replace(/700/gi, '700 ');
                                var number1 = item.replace(/\(58\)/gi, ' ');
                                $(".phone-mobile").append('<a href="tel:' + number1 + '">' + number + '</a><br>');
                                $(".phone-mobile").closest("tr").show();
                            });
                        }

                        $(".phone-number").html("");
                        $(".phone-number").closest("tr").hide();
                        if (employee.json.contact.telefon != 'undefined') {
                            $(employee.json.contact.telefon).each(function (index, item) {

                                var number = item.replace(/;/gi, ' ');
                                $(".phone-number").append('<a href="tel:' + number + '">' + number + '</a><br>');
                                $(".phone-number").closest("tr").show();
                            });
                        }

                        $(".phone-mail").html("");
                        $(".phone-mail").closest("tr").hide();
                        if (employee.json.contact.email != 'undefined') {
                            $(employee.json.contact.email).each(function (index, item) {
                                $(".phone-mail").append('<a href="mailto:' + item + '">' + item + '</a><br>');
                                $(".phone-mail").closest("tr").show();
                            });
                        }

                    }
                }
            };

            $("#basics").easyAutocomplete(options);

        })


    </script>

@endsection
