<div class="accordion-item draggable" data-sortjs-position="{{$benefit->position}}" data-sortjs-id="{{$benefit->id}}">
    <h2 class="accordion-header ">

        <button class="accordion-button collapsed"
                type="button"
                data-bs-toggle="collapse"
                data-bs-target="#acc-benefit-{{$benefit->id}}"
                aria-expanded="true"
                aria-controls="acc-benefit-{{$benefit->id}}">
            <div class="draggable-handle me-3 p-2" style="cursor: grab">
                <i class="far fa-arrows-alt fa-lg"></i>
            </div>

            <div  class="me-3">
                @include('components.image-circle', ['image' => url($benefit->imagePath), 'size' => '60px', 'color' => 'red'])
            </div>
            {{$benefit->key}}
        </button>
    </h2>
    <div
        id="acc-benefit-{{$benefit->id}}"
        class="accordion-collapse collapse border-top"
        data-bs-parent="#{{$parent}}">
        <div class="accordion-body">

            <div class="row">
                <div class="col-md-2">
                    {{--Image--}}
                    @include('components.image-circle', ['image' => url($benefit->imagePath), 'size' => '170px', 'color' => 'red', 'padding' => '20px'])

                </div>


                <div class="col-md-10">
                    <div class="mb-3 fs-4">
                        {{$benefit->title}}
                    </div>
                    <div class="mb-3">
                        {{$benefit->text}}
                    </div>
                </div>

                <div class="col-12">
                    <div class="text-end">
                        <a href="{{url('backend/insertion-manager/benefit/'.$benefit->id)}}" type="button" class="btn btn-outline-primary">
                            Bearbeiten <i class="fad fa-cog"></i>
                        </a>
                    </div>
                </div>


            </div>

        </div>
    </div>
</div>
