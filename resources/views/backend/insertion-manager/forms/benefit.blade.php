<form action="{{ url()->current() }}" id="" name="" method="post" enctype="multipart/form-data">
    {{ csrf_field() }}


    <div class="row">
        <div class="col-md-3">
            <div class="mb-3">

                <div class="border p-2">
                    <div id="icon-container">
                        @if($benefit)
                            <img src="{{ url($benefit->imagePath)}}" alt="" class="img-fluid mb-3 bg-danger">
                        @else
                            <div class="mb-3 py-5 border text-center">Noch kein Bild</div>
                        @endif
                    </div>


                    <label class="btn btn-light w-100 border" for="customFile"> Bild auswählen</label>
                    <input type="file" class="d-none" name="image" id="customFile"
                           accept="image/jpeg,image/png,image/gif,image/webp,image/svg+xml"/>
                </div>
            </div>
        </div>

        <div class="col-md-9">

            <div class="form-group mb-3">
                <label class="form-label" for="#benefit-key">Keyword</label>
                <input name="key" type="text" id="benefit-key" class="form-control"
                       value="{{($benefit) ? $benefit->key: ""}}"/>
            </div>

            <div class="form-group mb-3">
                <label class="form-label" for="#benefit-title">Titel</label>
                <input name="title" type="text" id="benefit-title" class="form-control"
                       value="{{($benefit) ? $benefit->title: ""}}"/>
            </div>

            <div class="form-group mb-3">
                <label class="form-label" for="#benefit-text">Text</label>
                <textarea name="text" class="form-control" id="benefit-text"
                          rows="4">{{($benefit) ? $benefit->text: ""}}</textarea>
            </div>

            <div class="d-flex justify-content-between">
                <a type="button" class="btn btn-outline-primary me-3" href="{{url('backend/insertion-manager')}}">
                    Zurück
                </a>

                <div class="d-flex justify-content-end">

                    @if($benefit)
                        <button type="button" class="btn btn-danger me-3" data-bs-toggle="modal"
                                data-bs-target="#benefit-delete-modal">
                            Löschen
                        </button>
                    @endif
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal"
                            data-bs-target="#benefit-save-modal">
                        Speichern
                    </button>
                </div>
            </div>


        </div>
    </div>

    <div class="modal fade" id="benefit-save-modal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Speichern</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center py-3">
                    <i class="far fa-save fa-4x text-primary"></i>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary me-3" data-bs-dismiss="modal">
                        Nein, Abbrechen
                    </button>
                    <button type="submit" class="btn btn-primary">
                        Ja, Speichern
                    </button>
                </div>
            </div>
        </div>
    </div>


</form>

@if($benefit)
    <div class="modal fade" id="benefit-delete-modal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Löschen</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center py-3">
                    <i class="far fa-trash fa-4x text-danger"></i>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary me-3" data-bs-dismiss="modal">
                        Nein, Abbrechen
                    </button>
                    <form action="{{ url()->current() }}/delete" id="" name="" method="post"
                          enctype="multipart/form-data">
                        {{ csrf_field() }}

                        @method('DELETE')

                        <button type="submit" class="btn btn-danger">
                            Ja, Löschen
                        </button>
                    </form>

                </div>
            </div>
        </div>
    </div>
@endif


<script>

    function phraseToKey(input) {
        input.value = input.value.replace(/\s+/g, '-');
    }


    document.getElementById('benefit-key').addEventListener('change', (e) => phraseToKey(e.target))
    document.getElementById('benefit-key').addEventListener('keyup', (e) => phraseToKey(e.target))

</script>
