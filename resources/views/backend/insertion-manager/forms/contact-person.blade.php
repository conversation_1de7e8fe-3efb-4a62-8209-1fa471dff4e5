<form action="{{ url()->current() }}" id="" name="" method="post" enctype="multipart/form-data">
    {{ csrf_field() }}


    <div class="row">
        <div class="col-md-3">
            <div class="mb-3">

                <div class="border p-2">
                    <div id="icon-container">
                        @if($person)
                            <img src="{{ url($person->imagePath)}}" alt="" class="img-fluid mb-3">
                        @else
                            <div class="mb-3 py-5 border text-center">Noch kein Bild</div>
                        @endif
                    </div>


                    <label class="btn btn-light w-100 border" for="customFile"> Bild auswählen</label>
                    <input type="file" class="d-none" name="image" id="customFile" accept="image/jpeg,image/png,image/gif,image/webp,image/svg+xml"/>
                </div>
            </div>
        </div>

        <div class="col-md-9">

            <div class="form-group mb-3">
                <label class="form-label fw-bold" for="#person-email">Email*</label>
                <input name="email" type="email" id="#person-email" class="form-control" value="{{($person) ? $person->email: ""}}"/>

            </div>


            <div class="form-group mb-3">
                <label class="form-label" for="#person-firstname">Name*</label>
                <input name="firstname" type="text" id="#person-firstname" class="form-control" value="{{($person) ? $person->firstname: ""}}"/>
            </div>

            <div class="form-group mb-3">
                <label class="form-label" for="person-lastname">Nachname*</label>
                <input name="lastname" class="form-control" id="person-lastname" value="{{($person) ? $person->lastname: ""}}">
            </div>

  {{--          <div class="form-group mb-3">
                <label class="form-label" for="person-linked_in">LinkedIn</label>
                <input name="linked_in" class="form-control" id="person-linked_in" value="{{($person) ? $person->linked_in: ""}}">
            </div>--}}

            <div class="form-group mb-3">
                <label class="form-label" for="person-phone">Phone</label>
                <input name="phone" class="form-control" id="person-phone" value="{{($person) ? $person->phone: ""}}">
            </div>

            <div class="mb-3 text-end">
                * Erforderliche Felder
            </div>

            <div class="d-flex justify-content-between">
                <a type="button" class="btn btn-outline-primary me-3" href="{{url('backend/insertion-manager')}}">
                    Zurück
                </a>

                <div class="d-flex justify-content-end">

                    @if($person)
                        <button type="button" class="btn btn-danger me-3" data-bs-toggle="modal" data-bs-target="#person-delete-modal">
                            Löschen
                        </button>
                    @endif
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#person-save-modal">
                        Speichern
                    </button>
                </div>
            </div>


        </div>
    </div>

    <div class="modal fade" id="person-save-modal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-firstname">Speichern</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center py-3">
                    <i class="far fa-save fa-4x text-primary"></i>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary me-3" data-bs-dismiss="modal">
                        Nein, Abbrechen
                    </button>
                    <button type="submit" class="btn btn-primary">
                        Ja, Speichern
                    </button>
                </div>
            </div>
        </div>
    </div>


</form>

@if($person)
    <div class="modal fade" id="person-delete-modal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-firstname">Löschen</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center py-3">
                    <i class="far fa-trash fa-4x text-danger"></i>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary me-3" data-bs-dismiss="modal">
                        Nein, Abbrechen
                    </button>
                    <form action="{{ url()->current() }}/delete" id="" name="" method="post" enctype="multipart/form-data">
                        {{ csrf_field() }}

                        @method('DELETE')

                        <button type="submit" class="btn btn-danger">
                            Ja, Löschen
                        </button>
                    </form>

                </div>
            </div>
        </div>
    </div>
@endif


