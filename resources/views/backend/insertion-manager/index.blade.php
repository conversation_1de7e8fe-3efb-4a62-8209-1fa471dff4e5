@extends('backend.master')
@section('content')

    <div class="row mb-5">
        <div class="col-12 mb-5">
            <div>
                <h2>Job-Inserate Verwaltung</h2>
            </div>
        </div>

        <div class="col-12">

            <div class="d-flex justify-content-between pb-2">
                <h3>Benefits</h3>
                <a href="{{url('backend/insertion-manager/benefit')}}" class="btn btn-primary ">+ Neu</a>
            </div>
            <div class="accordion sortable-list mb-5" id="benefit-accordion">
                @foreach($benefits as $benefit)
                    @include('backend.insertion-manager.components.benefit-accordion-item', ['benefit' => $benefit , 'parent' => "benefit-accordion"])
                @endforeach
            </div>



            <div class="d-flex justify-content-between pb-2">
                <h3>Ansprechpartner</h3>
                <a href="{{url('backend/insertion-manager/person')}}" class="btn btn-primary ">+ Neu</a>
            </div>
            <div class="accordion " id="contact-person-accordion">
                @foreach($contact_persons as $person)
                    @include('backend.insertion-manager.components.person', ['person' => $person])
                @endforeach

            </div>


        </div>
    </div>



    <script src="{{url('inc/sortableJS/sortableJS.js')}}"></script>


    <style>
        .accordion-button:not(.collapsed){
            color:unset !important;
        }
    </style>

    <script src="{{url('inc/sortableJS/sortableJS.js')}}"></script>
    <script src="{{url('inc/js/sort-handler/sort-handler.js')}}"></script>
    <script>


        const ajaxUrl = `${window.location.origin}/backend/ajax/insertion-manager/sort/benefit`

        new SortHandler('.sortable-list', '.draggable', '.draggable-handle', ajaxUrl)
    </script>

@endsection
