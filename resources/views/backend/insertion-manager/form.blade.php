@extends('backend.master')
@section('content')

    <div class="row mb-5">
        <div class="col-12">
            <div>
                <h2>{{$title}}</h2>
            </div>
        </div>

        <div class="col-12">

            <div>
                @include('backend.insertion-manager.forms.' . $form)
            </div>

        </div>
    </div>






    <script>
        const iconContainer = document.getElementById('icon-container');

        const iconContainerContent = iconContainer.innerHTML;

        const iconInput = document.querySelector('input[name="image"]')

        iconInput.addEventListener('change', (ev) => {
            if(ev.target.files?.length){

                const file = ev.target.files[0];

                if(!file.type.startsWith('image/')){
                    console.log('Error not image file selected')
                    return
                }

                const reader = new FileReader()

                reader.readAsDataURL(file);

                reader.onload = () => {

                    iconContainer.innerHTML = ''


                    const image = document.createElement('img')
                    image.src = reader.result
                    image.classList.add('img-fluid', 'bg-danger')
                    iconContainer.appendChild(image)

                    const btn = document.createElement('button')
                    btn.classList.add('btn', 'btn-outline-danger', 'w-100', 'my-1')
                    btn.innerText = 'Abbrechen'
                    iconContainer.appendChild(btn)


                    btn.addEventListener('click', () => {
                        iconContainer.innerHTML = iconContainerContent
                    })

                }


            }else{
                iconContainer.innerHTML = iconContainerContent
            }

        })

    </script>

@endsection
