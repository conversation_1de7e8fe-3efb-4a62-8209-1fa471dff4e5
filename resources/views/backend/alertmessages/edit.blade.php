@extends('backend.master')

@section('content')
	<div class="card">
		<div class="card-body">
			<div class="row mb-4">
				<div class="col-12">
					<h2 class="card-title">Hinweis-Text bearbeiten</h2>
				</div>
			</div>
			<form action="{{ url("backend/alertmessages/".$alertMessage->id) }}" id="messageForm" name="messageForm" method="post">
				{{ csrf_field() }}
				{{ method_field('put') }}
				<div class="row">
					<div class="col-12 col-md-3">
						<div class="form-group">
							<label for="title">Key
								<small>(kann nicht verändert werden)</small>
							</label>
							<input type="text" class="form-control mb-3 " value="{{ $alertMessage->key }}" disabled />
						</div>
					</div>
					<div class="col-12 col-md-9">
						<div class="form-group">
							<label for="description">Beschreibung </label>
							<input type="text" class="form-control mb-3 " id="description" name="description" value="{{ old("description", $alertMessage->description) }}" />
							{!!  ($errors->has("description")) ? "<small class='form-text text-danger'><span class='fas fa-exclamation-circle'></span> ".join('<br/>', $errors->get('description'))."</small>" : "" !!}
						</div>
					</div>
					<div class="col-12">
						<div class="form-group">
							<label for="title">Titel </label>
							<input type="text" class="form-control mb-3 " id="title" name="title" value="{{ old("title", $alertMessage->title) }}" />
							{!!  ($errors->has("title")) ? "<small class='form-text text-danger'><span class='fas fa-exclamation-circle'></span> ".join('<br/>', $errors->get('title'))."</small>" : "" !!}
						</div>
					</div>

					<div class="col-12">
						<div class="form-group">
							<label for="message">Text *</label>
							<textarea
								ckeditor="true"
								ckeditor_height="400"
								ckeditor_removeButtons="{{env('CKEDITOR_ELEMENTS_MIDI')}}"
								id="message" class="form-control mb-3 " name="message">{{  old('message', $alertMessage->message) }}
							</textarea>
							{!!  ($errors->has("message")) ? "<small class='form-text text-danger'><span class='fas fa-exclamation-circle'></span> ".join('<br/>', $errors->get('message'))."</small>" : "" !!}
						</div>
					</div>
					@if($alertMessage->placeholder)
						<div class="col-12">
							<div class="alert alert-info">
								<strong>Diese Platzhalter sind verfügbar:</strong><br>
								@foreach(explode(";", $alertMessage->placeholder) as $tmp)
									[{{ strtoupper($tmp) }}]
								@endforeach
							</div>
						</div>
					@endif
					<div class="col-12">
						<hr>
					</div>
					<div class="col-12 col-md-6">
						<a href="{{ url("backend/alertmessages") }}">
							<span class="btn btn-secondary">
								 <span class="fas fa-angle-double-left "></span> Zurück
							</span>
						</a>
					</div>
					<div class="col-12 col-md-6 text-end">
						<button type="submit" class="btn btn-success">
							<span class="fas fa-save"></span>
							Speichern
						</button>
					</div>
				</div>
			</form>
		</div>
	</div>
@endsection
