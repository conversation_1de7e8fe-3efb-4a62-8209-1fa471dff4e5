@extends('backend.master')

@section('content')
{{--	<div class="card">
		<div class="card-body">--}}
			<div class="row my-3">
				<div class="col-12">
					<h2 class="card-title">Hinweis-Texte</h2>
				</div>
				<div class="col-12 mt-4 p-0">
					<table class="table table-striped table-vertical-middle table-bordered" datatable="true" data-paging="false" data-order="[[ 0, &quot;asc&quot; ]]">
						<thead  class="table-dark">
						<tr >
							<th data-orderable="true">Key</th>
							<th data-orderable="true">Text</th>
							<th data-orderable="true">letzte Änderung</th>
							<th data-orderable="false">&nbsp;</th>
						</tr>
						</thead>
						<tbody>
						@foreach($messages as $tmp)
							<tr>
								<td class="{{$tmp->key}}">{{ $tmp->key }}</td>
								<td width="60%">
									{!! ($tmp->description) ? "<small><i>$tmp->description</i></small><br>" :'' !!}
									{!! ($tmp->title) ? "<b>$tmp->title</b><br>" :'' !!} {!! $tmp->message !!}
								</td>
								<td>{{ ($tmp->updated_at) ? $tmp->updated_at->format('Y-m-d H:i:s') : '' }}</td>
								<td>
									<a href="{{ url("backend/alertmessages/".$tmp->id."/edit") }}" title="Bearbeiten">
									<span class="btn btn-secondary">
										<span class="fas fa-cog"></span>
									</span>
									</a>
								</td>
							</tr>
						@endforeach
						</tbody>
					</table>
				</div>
			</div>
{{--		</div>
	</div>--}}


@endsection
