@extends('backend.master')

@section('content')
	<div class="card">
		<div class="card-body">
			<div class="row">
				<div class="col-12 col-md-10">
					<h2 class="card-title">Kategorie-Verwaltung</h2>
				</div>
				<div class="col-12 col-md-2 text-end">
					<a href="{{ url("backend/oesterreichreise/tags/create") }}">
						<span class="btn btn-success btn-block">
							<span class="far fa-plus"></span> Neue Kategorie hinzufügen
						</span>
					</a>
				</div>
				<div class="col-12 mt-4 ">
					<table class="table table-vertical-middle table-bordered" datatable="true" data-page-length="100">
						<thead>
						<tr>

							<th data-orderable="true">Bezeichnung</th>
							<th>&nbsp;</th>
						</tr>
						</thead>
						<tbody>
						@foreach($tags as $tmp)
							<tr {{($tmp->deleted_at) ? "class=text-muted" : ""}} shortlabelid="{{$tmp->id}}" bookmarkid="{{$tmp->bookmark_id }}">

								<td>{{ $tmp->desc }}</td>
								<td>
									<a href="{{ url("backend/oesterreichreise/tags/".$tmp->id."/edit") }}" title="Bearbeiten" class=" ">
										<span class="btn btn-secondary">
											<span class="fas fa-cog"></span>
										</span>
									</a>
									<button type="button" title="Löschen" confirmUrl="{{ url("backend/oesterreichreise/tags/".$tmp->id."/delete") }}" confirmTxt="Wirklich Löschen?<br><br>Verknüpfte Reise-Angebote bleiben bestehen" class="btn btn-danger ">
											<i class="fas fa-trash"></i>
									</button>
								</td>
							</tr>
						@endforeach
						</tbody>
					</table>
				</div>
			</div>
		</div>
	</div>
@endsection
