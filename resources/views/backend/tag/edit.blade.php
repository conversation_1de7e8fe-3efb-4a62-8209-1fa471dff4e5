@extends('backend.master')

@section('content')
	<div class="card">
		<div class="card-body">
			<div class="row mb-4">
				<div class="col-12">
					<h2 class="card-title"><PERSON><PERSON><PERSON> bearbeiten ({{$tag->id}})</h2>
				</div>
			</div>
			<form action="{{ url("backend/oesterreichreise/tags/".$tag->id) }}" id="tagForm" name="tagForm" method="post" enctype="multipart/form-data">
				{{ csrf_field() }}
				{{ method_field('put') }}
				<div class="table-warning mb-4 p-3">
					<div class="row">
						<div class="col-12 col-md-6">
							<div class="form-group">
								<label for="desc">Beschreibung *</label>
								<input type="text" id="desc" class="form-control mb-3 " name="desc" value="{{ old('desc', $tag->desc) }}">
								{!!  ($errors->has("desc")) ? "<small class='form-text text-danger'><span class='fas fa-exclamation-circle'></span> ".join('<br/>', $errors->get('desc'))."</small>" : "" !!}
							</div>
						</div>
					</div>
					<div class="row">
						<div class="col-12">
							<hr>
						</div>
						<div class="col-12 col-md-6">
							<a href="{{ url("backend/oesterreichreise/tags") }}">
							<span class="btn btn-secondary">
								 <span class="fas fa-angle-double-left "></span>&nbsp;Zurück
							</span>
							</a>
						</div>
						<div class="col-12 col-md-6 text-end">
							<button type="button" submit="true" loadicon="true" class="btn btn-success">
								<span class="fas fa-save"></span>
								Speichern
							</button>
						</div>
					</div>
				</div>
			</form>
		</div>
	</div>
@endsection
