@extends('backend.master')

@section('content')
	<div class="modal fade" id="linkBookmarkModal" tabindex="-1" role="dialog">
		<div class="modal-dialog" role="document">
			<input name="_method" type="hidden" value="">
			<form id="linkBookmarkForm" name="linkBookmarkForm" method="post">
				{{ csrf_field() }}
				<div class="modal-content">
					<div class="modal-header">
						<h5 class="modal-title">Zugehöriges Bookmark setzen</h5>
						<button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
					</div>
					<div class="modal-body">
						<div class="alert-wrapper"></div>
						<select class="custom-select" name="bookmark">
							<option value="">Bookmark auswählen</option>
							@foreach($bookmarks as $bookmark)
								<option bookmark="{{ $bookmark->id }}" value="{{ $bookmark->id }}">{{ $bookmark->description}}</option>
							@endforeach
						</select>
					</div>
					<div class="modal-footer justify-content-between">
						<button type="button" class="btn btn-danger " data-bs-dismiss="modal" onfocus="this.blur()"><i class="fas fa-times"></i>&nbsp;&nbsp;Abbrechen
						</button>
						<button class="btn btn-success" type="button" onfocus="this.blur()"><i class="fas fa-check"></i>&nbsp;&nbsp;Speichern
						</button>
						<input type="hidden" name="bookmark_id">
						<input type="hidden" name="shortlabel_id">
					</div>
				</div>
			</form>
		</div>
	</div>

	<div class="card">
		<div class="card-body">
			<div class="row">
				<div class="col-12 col-md-10">
					<h2 class="card-title">Kürzel-Verwaltung</h2>
				</div>
				<div class="col-12 mt-4 ">
					<table class="table table-vertical-middle table-bordered" datatable="true" data-page-length="100">
						<thead>
						<tr>
							<th width="5%">#</th>
							<th width="8%" data-orderable="true">Objekt</th>
							<th data-orderable="true">Kürzel</th>
							<th data-orderable="true">Bezeichnung</th>
							<th width="30%" data-orderable="true">Bookmark</th>
							<th>&nbsp;</th>
						</tr>
						</thead>
						<tbody>
						@foreach($shortlabels as $tmp)
							<tr {{($tmp->deleted_at) ? "class=text-muted" : ""}} shortlabelid="{{$tmp->id}}" bookmarkid="{{$tmp->bookmark_id }}">
								<th>{{$tmp->id}}</th>
								<td>{{ (!empty($tmp->object)) ? (($tmp->object == 1) ? "KRONE": 'KURIER') : '-' }}</td>
								<td>{{$tmp->shortlabel}}</td>
								<td>{{ $tmp->description }}</td>
								<td>
									<div class="form-group">
										<button type="button" class="btn btn-light w-100" triggerModal="true">
											@if(!$tmp->bookmark)
												<i class="far fa-exclamation-circle fa-lg me-2 text-danger"></i>Keinem Bookmark
											@else
												<b>{{$tmp->bookmark->description}}</b>
											@endif
										</button>
									</div>
								</td>
								<td>
									<a href="{{ url("backend/mediathek-admin/shortlabel/".$tmp->id."/edit") }}" title="Bearbeiten" class="mx-2">
										<span class="btn btn-secondary">
											<span class="fas fa-cog"></span>
										</span>
									</a>
								</td>
							</tr>
						@endforeach
						</tbody>
					</table>
				</div>
			</div>
		</div>
	</div>
@endsection
