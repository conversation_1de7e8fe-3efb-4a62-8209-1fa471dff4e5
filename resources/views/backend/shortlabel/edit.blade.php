@extends('backend.master')

@section('content')
	<div class="card">
		<div class="card-body">
			<div class="row mb-4">
				<div class="col-12">
					<h2 class="card-title"><PERSON><PERSON><PERSON><PERSON> bearbeiten ({{$shortlabel->id}})</h2>
				</div>
			</div>
			<form action="{{ url("backend/mediathek-admin/shortlabel/".$shortlabel->id) }}" id="shortlabelForm" name="shortlabelForm" method="post" enctype="multipart/form-data">
				{{ csrf_field() }}
				{{ method_field('put') }}
				<div class="table-warning mb-4 p-3">
					<div class="row">
						<div class="col-12"><h5><b>{{ (!empty($shortlabel->object) && $shortlabel->object == 1) ? 'KRONE':'KURIER' }}</b></h5>
							<hr>
						</div>

						<div class="col-12 col-md-6">
							<div class="form-group">
								<label for="shortlabel"><PERSON><PERSON><PERSON><PERSON> *</label>
								<input type="text" id="description" class="form-control mb-3  text-lowercase" name="shortlabel" value="{{ old('shortlabel', $shortlabel->shortlabel) }}">
								{!!  ($errors->has("shortlabel")) ? "<small class='form-text text-danger'><span class='fas fa-exclamation-circle'></span> ".join('<br/>', $errors->get('shortlabel'))."</small>" : "" !!}
							</div>
						</div>
						<div class="col-12 col-md-6">
							<div class="form-group">
								<label for="description">Beschreibung *</label>
								<input type="text" id="description" class="form-control mb-3 " name="description" value="{{ old('description', $shortlabel->description) }}">
								{!!  ($errors->has("description")) ? "<small class='form-text text-danger'><span class='fas fa-exclamation-circle'></span> ".join('<br/>', $errors->get('description'))."</small>" : "" !!}
							</div>
						</div>
					</div>
					<div class="row">
						<div class="col-12">
							<hr>
						</div>
						<div class="col-12 col-md-6">
							<a href="{{ url("backend/mediathek-admin/shortlabel") }}">
							<span class="btn btn-secondary">
								 <span class="fas fa-angle-double-left "></span>&nbsp;Zurück
							</span>
							</a>
						</div>
						<div class="col-12 col-md-6 text-end">
							<button type="button" submit="true" loadicon="true" class="btn btn-success">
								<span class="fas fa-save"></span>
								Speichern
							</button>
						</div>
					</div>
				</div>
			</form>
		</div>
	</div>
@endsection
