{{--
IN USE:
    backend.speakout.homepage.show

--}}

@php
    if(empty($data)){
        dd('Error Input needs $data');
    }
    $inputName = $data['inputName'];
    $label = $data['label'];
    $value = $data['value'];
    $inputType = $data['inputType'];
    $accept = !empty($data['accept']) ? $data['accept'] : '*' ;
@endphp


@if($inputType === "select")
    <div class="mb-3 w-100 form-field-container{{--JS--}}">
        @if($data['label'])
            <label class="form-label" for="select-{{$inputName}}">  {{$label}}</label>
        @endif
        <select id="select-{{$inputName}}"
                class="form-select form-field{{--JS--}}"
                @if(!empty($data['disabled'])) disabled @endif
                name="{{$inputName}}">
            @foreach($data['options'] as $option)
                @if(!is_array($option))
                    <option @if(old($inputName, $value) == $option) selected
                            @endif value="{{$option}}">{{$option}}</option>
                @else
                    <option @if(old($inputName, $value) == $option['value']) selected
                            @endif value="{{$option['value']}}">{{$option['viewValue']}}</option>
                @endif
            @endforeach
        </select>

        @error($inputName)
        <div class="text-danger form-error-hint{{--JS--}}">{{ $errors->first($inputName) }}</div>
        @enderror
    </div>

@elseif(in_array($inputType,["text", "email", "date", "datetime-local", "number"]))

    @php
        if($inputType === "date" && $value instanceof \Carbon\Carbon){

            $value = $value->format('Y-m-d');

        }
    @endphp


    <div class="mb-3 w-100 form-field-container{{--JS--}}">
        @if($label)
            <label for="input-{{$inputName}}" class="form-label ">{{$label}}</label>
        @endif
        <input
            type="{{$inputType}}"
            class="form-control form-field{{--JS--}}"
            name="{{$inputName}}"
            id="input-{{$inputName}}"
            value="{{old($inputName, $value)}}"
            @if(!empty($data['disabled'])) disabled @endif
            @if($inputType === "number")  step="1" @endif
        >
        @error($inputName)
        <div class="text-danger form-error-hint{{--JS--}}">{{ $errors->first($inputName) }}</div>
        @enderror
    </div>

@elseif($inputType == "checkbox")

    <div class="mb-3 w-100 form-field-container{{--JS--}}">
        <div class="form-check">
            <input class="form-check-input form-field{{--JS--}}" type="checkbox" id="checkbox-{{$inputName}}"
                   name="{{$inputName}}"
                   @if(!empty($data['disabled'])) disabled @endif>
            <label class="form-check-label" for="checkbox-{{$inputName}}">
                {!! $label !!}
            </label>
        </div>
        @error($inputName)
        <div class="text-danger form-error-hint{{--JS--}}">{{ $errors->first($inputName) }}</div>
        @enderror

    </div>
@elseif($inputType == "file")

    <div class="mb-3 w-100 form-field-container{{--JS--}}">

        <label class="form-check-label" for="file-{{$inputName}}">
            {!! $label !!}
        </label>
        <input class="form-control form-field{{--JS--}}" type="file"
               id="file-{{$inputName}}" name="{{$inputName}}"
               accept="{{$accept}}"
               @if(!empty($data['disabled'])) disabled @endif>


        @error($inputName)
        <div class="text-danger form-error-hint{{--JS--}}">{{ $errors->first($inputName) }}</div>
        @enderror

    </div>

@elseif($inputType == "textarea")

    <div class="mb-3 w-100 form-field-container{{--JS--}}">

        <label class="form-check-label" for="textarea-{{$inputName}}">
            {!! $label !!}
        </label>
        <textarea class="form-control form-field{{--JS--}}" type="textarea"
                  id="file-{{$inputName}}" name="{{$inputName}}" rows="7"
                  @if(!empty($data['disabled'])) disabled @endif>{{old($inputName, $value)}}</textarea>

        @error($inputName)
        <div class="text-danger form-error-hint{{--JS--}}">{{ $errors->first($inputName) }}</div>
        @enderror

    </div>
@elseif($inputType == "ckeditor")

    <div class="mb-3 w-100 form-field-container{{--JS--}}">

        <label class="form-label w-100">{!! $label !!}
            <textarea
                ckeditor="true"
                ckeditor_height="200"
                ckeditor_removeButtons="{{env('CKEDITOR_ELEMENTS_MIDI')}}"
                id="{{$inputName}}" class="form-control mb-3 " name="{{$inputName}}">{{old($inputName, $value)}}</textarea>
        </label>

        @error($inputName)
        <div class="text-danger form-error-hint{{--JS--}}">{{ $errors->first($inputName) }}</div>
        @enderror

    </div>
@else
    <div class="text-danger fw-bold py-3">
        INPUT TYPE "{{$inputType}}" NOT FOUND!!
    </div>

@endif


