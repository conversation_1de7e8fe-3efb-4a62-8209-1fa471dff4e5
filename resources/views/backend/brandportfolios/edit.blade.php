@extends('backend.master')

@section('content')

    {{--	@php
    --}}{{--	echo '<pre>';
            var_dump($brandportfolio); die();
        echo '</pre>';--}}{{--

        @endphp--}}
    <div class="card">
        <div class="card-body">
            <div class="row mb-4">
                <div class="col-12">
                    <h2 class="card-title">Marke bearbeiten</h2>
                </div>
            </div>
            <form action="{{ url("backend/brandportfolios/".$brandportfolio->id) }}" id="brandportfolioForm" name="brandportfolioForm" method="post"
                  enctype="multipart/form-data">
                {{ csrf_field() }}
                {{ method_field('put') }}
                <div class="row">
                    <div class="col-12 col-md-5">
                        <div class="form-group">
                            <label for="title">Titel *</label>
                            <input type="text" id="title" class="form-control mb-3 " name="title" value="{{ old('title', $brandportfolio->title) }}">
                            {!!  ($errors->has("title")) ? "<small class='form-text text-danger'><span class='fas fa-exclamation-circle'></span> ".join('<br/>', $errors->get('title'))."</small>" : "" !!}
                        </div>
                    </div>
                    <div class="col-12 col-md-7">
                        <div class="form-group">
                            <div class="form-group">
                                <label for="url">Url*</label>
                                <input type="text" id="url" class="form-control mb-3  " name="url" value="{{  old('url', $brandportfolio->url) }}">
                                {!!  ($errors->has("url")) ? "<small class='form-text text-danger'><span class='fas fa-exclamation-circle'></span> ".join('<br/>', $errors->get('url'))."</small>" : "" !!}
                            </div>
                        </div>
                    </div>
                    <div class="col-12">
                        <hr>
                        <div class="d-flexs flex-column">
                            <div class="dropdown">
                                <button class="btn btn-success dropdown-toggle" type="button" id="dropdownMenuButton1" data-bs-toggle="dropdown"
                                        aria-expanded="false">
                                    Tags auswählen
                                </button>
                                <div class="dropdown-menu" aria-labelledby="dropdownMenuButton1">

                                    @foreach($tags as $tag)
                                        <div class="dropdown-item">
                                            <div class="form-check form-check-inline">
                                                <input class="form-check-input tagsInput" name="tags['{{$tag->name}}']" type="checkbox"
                                                       id="check{{$tag->id}}" value="{{$tag->id}}">
                                                <label class="form-check-label font-weight-bold" for="check{{$tag->id}}"> {{$tag->title}}</label>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                        <hr>
                    </div>

                    @if($brandportfolio->path)
                        <div class="col-12 col-md-6">
                            <div class="form-group">
                                <label>Altes Logo</label>
                                <div class="card">
                                    <div class="card-body">
                                        <img class="w-100" src="{{ asset("/storage/app/".$brandportfolio->path)}}" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif

                    <div class="col-12 col-md-6">
                        <div class="form-group">
                            <label for="customFile_file">Logo hochladen</label>
                            <input type="file" class="form-control" id="customFile_file" name="file" accept="image/png, image/jpeg">
                            {!!  ($errors->has("file")) ? "<br><small class='form-text text-danger'><span class='fas fa-exclamation-circle'></span> ".join('<br/>', $errors->get('file'))."</small>" : "" !!}
                        </div>
                    </div>

                </div>
                <div class="row">
                    <div class="col-12">
                        <hr>
                    </div>
                    <div class="col-12 col-md-6">
                        <a href="{{ url("backend/brandportfolios") }}">
							<span class="btn btn-secondary">
								 <span class="fas fa-angle-double-left "></span> Zurück
							</span>
                        </a>
                    </div>
                    <div class="col-12 col-md-6 text-end">
                        <button type="button" submit="true" loadicon="true" class="btn btn-success">
                            <span class="fas fa-save"></span>
                            Speichern
                        </button>
                    </div>
                </div>
            </form>
        </div>

    </div>
    <script>
        let checkInpt = {{{$owned_tags}}};
        checkInpt.forEach(function(id){
            $('#check' + id).click();
        })

    </script>

@endsection
