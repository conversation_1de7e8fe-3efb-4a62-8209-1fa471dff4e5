@extends('backend.master')

@section('content')
    <div class="row my-4">
        <div class="col-12 d-flex justify-content-between">
            <h2 class="card-title">Markenportfolio</h2>
            <a class="btn btn-success btn-block" href="{{ url("backend/brandportfolios/create") }}">
                <span class="far fa-plus"></span>
                <PERSON><PERSON> hinzufügen
            </a>
        </div>

        <div class="col-12 mt-4 p-0">
            <table class="table table-striped table-vertical-middle" datatable="true" data-paging="false" data-order="[[ 0, &quot;asc&quot; ]]">
                <thead class="table-dark">
                <tr>
                    <th data-orderable="true">Titel</th>
                    <th data-orderable="false">Url</th>
                    <th data-orderable="false">Filename</th>
                    <th data-orderable="false">&nbsp;</th>
                </tr>
                </thead>
                <tbody>
                @foreach($brands as $tmp)
                    <tr>
                        <td class="fw-bold">{{ $tmp->title }}</td>
                        <td>{{ $tmp->url }}</td>
                        <td>{{ $tmp->filename }}</td>
                        <td>
                            <button type="button" class="btn btn-danger" confirmUrl="{{url("backend/brandportfolios/".$tmp->id."/delete")}}" confirmTxt="Marke &quot;{{ $tmp->title }}&quot; wirklich löschen?" title="Löschen">
                                <span class="fas fa-trash"></span>
                            </button>

                            <a class="btn btn-secondary" href="{{ url("backend/brandportfolios/".$tmp->id."/edit") }}" title="Bearbeiten">
                                <span class="fas fa-cog"></span>
                            </a>
                        </td>
                    </tr>
                @endforeach

                </tbody>
            </table>
        </div>
    </div>
@endsection
