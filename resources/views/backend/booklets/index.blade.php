@extends('backend.master')

@section('content')
	<div class="card">
		<div class="card-body">
			<div class="row">
				<div class="col-12 col-md-10">
					<h2 class="card-title">Katalog-Verwaltung</h2>
				</div>
				<div class="col-12 col-md-2 text-end">
					<a href="{{ url("backend/booklets/create") }}">
						<span class="btn btn-success btn-block">
							<span class="far fa-plus"></span> Katalog hinzufügen
						</span>
					</a>
				</div>
				<div class="col-12 mt-4">
					<table class="table table-striped table-vertical-middle table-bordered" datatable="true" data-page-length="100" data-order="[[ 2, &quot;asc&quot; ], [ 5, &quot;desc&quot; ]]">
						<thead>
						<tr>
							<th width="5%">#</th>
							<th data-orderable="true">Be<PERSON>ichnung</th>
							<th data-orderable="true">Ordner</th>
							<th data-orderable="true">Views</th>
							<th data-orderable="true">Online </th>
							<th data-orderable="true">Offline </th>
							<th data-orderable="true">Erstellt/Bearbeitet </th>
							<th data-orderable="false">&nbsp;</th>
						</tr>
						</thead>
						<tbody>
						@foreach($booklets as $tmp)
							<tr {{($tmp->deleted_at) ? "class=text-muted" : ""}}>
								<th>{{$tmp->id}}</th>
								<td>{{ $tmp->description }}</td>
								<td>{{ $tmp->directory }}</td>
								<td>{{ $tmp->views }}</td>
								<td>{{ $tmp->online_at }}</td>
								<td>{{ $tmp->offline_at }}</td>
								<td class="small" data-sort="{{$tmp->updated_at}}">
									{{ $tmp->created_at }}<br>
									{{ $tmp->updated_at }}
								</td>
								<td>
									<button type="button" class="btn btn-danger" confirmUrl="{{url("backend/booklets/".$tmp->id."/delete")}}" confirmTxt="Katalog &quot;{{ $tmp->description }}&quot; wirklich löschen?" title="Löschen">
										<span class="fas fa-trash"></span>
									</button>

									<span class="btn btn-info btn-clipboard" data-clipboard-text="{{ env("APP_URL")."/katalog/".$tmp->directory }}" title="In Zwischenablage kopieren">
										<span class="far fa-clipboard-check"></span>
									</span>

									<a href="{{ url("backend/booklets/".$tmp->id."/edit") }}" title="Bearbeiten">
										<span class="btn btn-secondary">
											<span class="fas fa-cog"></span>
										</span>
									</a>
								</td>
							</tr>
						@endforeach
						</tbody>
					</table>
				</div>
			</div>
		</div>
	</div>
@endsection
