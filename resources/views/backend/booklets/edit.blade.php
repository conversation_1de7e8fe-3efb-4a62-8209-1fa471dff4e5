@extends('backend.master')

@section('content')

	<div class="card">
		<div class="card-body">
			<div class="row mb-4">
				<div class="col-12">
					<h2 class="card-title">Katalog bearbeiten ({{$booklet->id}})</h2>
				</div>
			</div>
			<form action="{{ url("backend/booklets/".$booklet->id) }}" id="bookletForm" name="bookletForm" method="post" enctype="multipart/form-data">
				{{ csrf_field() }}
				{{ method_field('put') }}
				<div class="table-warning mb-4 p-3">
					<div class="row">
						<div class="col-12"><h5>Allgemeine Einstellungen</h5>
							<hr>
						</div>
						<div class="col-12 col-md-6">
							<div class="form-group">
								<label for="description">Beschreibung*</label>
								<input type="text" id="description" class="form-control mb-3 " name="description" value="{{ old('description', $booklet->description) }}">
								{!!  ($errors->has("description")) ? "<small class='form-text text-danger'><span class='fas fa-exclamation-circle'></span> ".join('<br/>', $errors->get('description'))."</small>" : "" !!}
							</div>
						</div>
						<div class="col-12 col-md-3">
							<div class="form-group">
								<label for="online_at">Online*</label>
								<input type="text" id="online_at" class="form-control mb-3  datepicker-datetime" name="online_at" value="{{  old('online_at',$booklet->online_at) }}">
								{!!  ($errors->has("online_at")) ? "<small class='form-text text-danger'><span class='fas fa-exclamation-circle'></span> ".join('<br/>', $errors->get('online_at'))."</small>" : "" !!}
							</div>
						</div>
						<div class="col-12 col-md-3">
							<div class="form-group">
								<label for="offline_at">Offline*</label>
								<input type="text" id="offline_at" class="form-control mb-3  datepicker-datetime" name="offline_at" value="{{  old('offline_at',$booklet->offline_at) }}">
								{!!  ($errors->has("offline_at")) ? "<small class='form-text text-danger'><span class='fas fa-exclamation-circle'></span> ".join('<br/>', $errors->get('offline_at'))."</small>" : "" !!}
							</div>
						</div>

						<div class="col-12 col-md-6">
							<div class="form-group">
								<label for="file">Katalog-Upload (*.pdf)</label>
								<div class="custom-file">
									<input type="file" class="custom-file-input" id="customFile" name="customFile" accept=".pdf" lang="de" />
									<label class="custom-file-label" for="customFile">Datei auswählen</label>
								</div>
								{!!  ($errors->has("customFile")) ? "<small class='form-text text-danger'><span class='fas fa-exclamation-circle'></span> ".join('<br/>', $errors->get('customFile'))."</small>" : "" !!}
							</div>
						</div>
						@if(is_file("storage/app/public/booklets/".$booklet->directory."/original.pdf"))
							<div class="col-12 col-md-6">
								<div class="form-group">
									<label>&nbsp;</label><br>
									<a href="{{ url("storage/app/public/booklets/".$booklet->directory."/original.pdf") }}" target="_blank">
										<span class="btn btn-secondary btn-lg">PDF anzeigen</span>
									</a>
								</div>
							</div>
						@endif
					</div>
					<div class="row">
						<div class="col-12">
							<hr>
						</div>
						<div class="col-12 col-md-6">
							<a href="{{ url("backend/booklets") }}">
							<span class="btn btn-secondary">
								 <span class="fas fa-angle-double-left "></span> Zurück
							</span>
							</a>
						</div>
						<div class="col-12 col-md-6 text-end">
							<button type="button" submit="true" loadicon="true" class="btn btn-success">
								<span class="fas fa-save"></span>
								Speichern
							</button>
						</div>
					</div>
				</div>
			</form>
		</div>
	</div>
@endsection
