@extends('backend.master')

@section('content')
{{--	<div class="card">
		<div class="card-body">--}}
			<div class="row my-4">
				<div class="col-12 ">
                    <div class="w-100 d-flex justify-content-between flex-wrap">
                        <h2 >AGB-Verwaltung</h2>
                        <a href="{{ url("backend/agbs/create") }}" class="">
						<span class="btn btn-success">
							<span class="far fa-plus"></span> AGB hinzufügen
						</span>
                        </a>
                    </div>
				</div>

				<div class="col-12 mt-4 p-0">
					<table class="table table-striped table-vertical-middle table-bordered" datatable="true" data-paging="false" data-order="[[ 0, &quot;asc&quot; ]]">
						<thead>
						<tr  class="table-dark">
							<th data-orderable="false" width="5%"></th>
							<th data-orderable="false">Titel & Beschreibung</th>
							<th data-orderable="false">Online</th>
							<th data-orderable="false">Offline</th>
							<th data-orderable="false">Bearbeitet</th>
							<th data-orderable="false">&nbsp;</th>
						</tr>
						</thead>
						<tbody class="agbsort">
						@foreach($agbs as $tmp)
							<tr id="{{ $tmp->id }}">
								<td data-sort="{{ $tmp->sort }}" class="text-center {{ (now() >= $tmp->online_at && now() <= $tmp->offline_at) ? 'table-success' : 'table-danger' }}">
									<span class="far fa-arrows-alt icon-move"></span>
								</td>
								<td>
									<b>{{ $tmp->title }}</b><br>
									<small>{{ $tmp->description }}</small>
								</td>
								<td>{{ $tmp->online_at }}</td>
								<td>{{ $tmp->offline_at }}</td>
								<td>{{ $tmp->updated_at }}</td>
								<td>
									<button type="button" class="btn btn-danger" confirmUrl="{{url("backend/agbs/".$tmp->id."/delete")}}" confirmTxt="AGB &quot;{{ $tmp->description }}&quot; wirklich löschen?" title="Löschen">
										<span class="fas fa-trash"></span>
									</button>
									<a href="{{ url("backend/agbs/".$tmp->id."/download") }}" title="Download">
										<span class="btn btn-warning">
											<span class="far fa-file"></span>
										</span>
									</a>
									<a href="{{ url("backend/agbs/".$tmp->id."/edit") }}" title="Bearbeiten">
										<span class="btn btn-secondary">
											<span class="fas fa-cog"></span>
										</span>
									</a>
								</td>
							</tr>
						@endforeach
						</tbody>
					</table>
				</div>
			</div>
	{{--	</div>
	</div>--}}
@endsection
