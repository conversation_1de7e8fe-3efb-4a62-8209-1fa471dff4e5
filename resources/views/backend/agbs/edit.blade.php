@extends('backend.master')

@section('content')
    <div class="card">
        <div class="card-body">
            <div class="row mb-4">
                <div class="col-12">
                    <h2 class="card-title">AGB bearbeiten</h2>
                </div>
            </div>
            <form action="{{ url("backend/agbs/".$agb->id) }}" id="agbForm" name="agbForm" method="post" enctype="multipart/form-data">
                {{ csrf_field() }}
                {{ method_field('put') }}
                <div class="row">
                    <div class="col-12 col-md-6">
                        <div class="form-group">
                            <label for="title">Titel *</label>
                            <input type="text" id="title" class="form-control mb-3 " name="title" value="{{ old('title', $agb->title) }}">
                            {!!  ($errors->has("title")) ? "<small class='form-text text-danger'><span class='fas fa-exclamation-circle'></span> ".join('<br/>', $errors->get('title'))."</small>" : "" !!}
                        </div>
                    </div>
                    <div class="col-12 col-md-3">
                        <div class="form-group">
                            <label for="online_at">Online *</label>
                            <input type="text" id="online_at" class="form-control mb-3  datepicker-datetime" name="online_at"
                                   value="{{  old('online_at', ((!empty($agb->online_at)) ? $agb->online_at->format("Y-m-d H:i:s") : '')) }}">
                            {!!  ($errors->has("online_at")) ? "<small class='form-text text-danger'><span class='fas fa-exclamation-circle'></span> ".join('<br/>', $errors->get('online_at'))."</small>" : "" !!}
                        </div>
                    </div>
                    <div class="col-12 col-md-3">
                        <div class="form-group">
                            <label for="offline_at">Offline *</label>
                            <input type="text" id="offline_at" class="form-control mb-3  datepicker-datetime" name="offline_at"
                                   value="{{  old('offline_at', $agb->offline_at) }}">
                            {!!  ($errors->has("offline_at")) ? "<small class='form-text text-danger'><span class='fas fa-exclamation-circle'></span> ".join('<br/>', $errors->get('offline_at'))."</small>" : "" !!}
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="form-group">
                            <label for="description">Text</label>
                            <textarea
                                ckeditor="true"
                                ckeditor_height="400"
                                ckeditor_removeButtons="{{env('CKEDITOR_ELEMENTS_MIDI')}}"
                                id="description" class="form-control mb-3 " name="description">{{ old('description', $agb->description) }}
							</textarea>
                            {!!  ($errors->has("description")) ? "<small class='form-text text-danger'><span class='fas fa-exclamation-circle'></span> ".join('<br/>', $errors->get('description'))."</small>" : "" !!}
                        </div>
                    </div>
                    <div class="col-12 col-md-6">
                        <div class="form-group mt-3">
                            <label for="fileInput">Dateiupload *</label>

                            <input type="file" class="form-control" id="fileInput" name="file" multiple accept=".pdf" lang="de"/>


                            {!!  ($errors->has("file")) ? "<br><small class='form-text text-danger'><span class='fas fa-exclamation-circle'></span> ".join('<br/>', $errors->get('file'))."</small>" : "" !!}
                        </div>
                    </div>
                    @if(!empty($agb->filename))
                        <div class="col-12 col-md-2">
                            <label>&nbsp;</label><br>
                            <a href="{{ url("backend/agbs/".$agb->id."/download") }}" onfocus="this.blur();" target="_blank" class="m-3 btn btn-info"  style="white-space:nowrap;">
                          <span class="far fa-file"></span> PDF anzeigen
                            </a>
                        </div>
                    @endif
                </div>
                <div class="row">
                    <div class="col-12">
                        <hr>
                    </div>
                    <div class="col-12 col-md-6">
                        <a href="{{ url("backend/agbs") }}">
							<span class="btn btn-secondary">
								 <span class="fas fa-angle-double-left "></span> Zurück
							</span>
						</a>
					</div>
					<div class="col-12 col-md-6 text-end">
						<button type="button" submit="true" loadicon="true" class="btn btn-success">
							<span class="fas fa-save"></span>
							Speichern
						</button>
					</div>
				</div>
			</form>
		</div>
	</div>
@endsection
