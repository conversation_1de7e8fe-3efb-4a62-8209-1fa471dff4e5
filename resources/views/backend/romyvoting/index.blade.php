@extends('backend.master')

@section('content')
    <div class="row">
        <div class="col my-5">
            <h1 class="card-title">ERGEBNISSE ROMY
                                   WAHL {{ (!empty(request()->segment(3))) ? request()->segment(3) : now()->format("Y") }}</h1>
        </div>
    </div>
    <div class="row mb-5">
        <div class="col  ">
            <div class="card h-100 border-success text-center">
                <div class="card-body py-4">
                    <h3>
                        <div class="fw-bold mb-2">Votings bereinigt</div>
                        {{number_format($votings_clean[0]->count ,0,",",".") }} </h3>
                </div>
            </div>

        </div>
        <div class="col  ">
            <div class="card h-100  ">
                <div class="card-body  py-4 text-center">
                    <h3>
                        <div class="fw-bold mb-2">Votings gesamt</div>
                        {{number_format($votings_raw[0]->count ,0,",",".") }} </h3>
                </div>
            </div>

        </div>


    </div>


    @foreach($kategorien_raw as $kategorie => $values)
        <div class="row">
            <div class="col mb-3">
                <h3>{{ $romyKategorien[$kategorie] }}</h3>
            </div>
        </div>


        <div class="row">
            <div class="col-12 mb-2">Bereinigt (max 5 Votings pro Session)</div>
            @foreach($kategorien_clean[$kategorie] as $star => $value)
                <div class="col-12 col-md mb-3">
                    <div class="card h-100 border-success">
                        <div class="card-body">
                            <div class="fw-bold">
                                {!! str_replace(";", "<br>", $romyAuswahlen[$kategorie][$star]["name"]) !!}
                            </div>
                        </div>
                        <div class="card-footer">
                            <b>{{ $value["prozent"] }}%</b>
                            <small>({{ number_format($value["anzahl"],0,",",".") }})</small>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>

        <div class="row">
            <div class="col-12 mb-2">Original (alles wird gezählt)</div>
            @foreach($values as $star => $value)
                <div class="col-12 col-md mb-3">
                    <div class="card h-100">
                        <div class="card-body">
                            <div class="fw-bold">
                                {!! str_replace(";", "<br>", $romyAuswahlen[$kategorie][$star]["name"]) !!}
                            </div>
                        </div>
                        <div class="card-footer">
                            <b>{{ $value["prozent"] }}%</b>
                            <small>({{ number_format($value["anzahl"],0,",",".") }})</small>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>




        <div class="row my-4">
            <div class="col">
                <br>
            </div>
        </div>
    @endforeach
@endsection
