@extends('backend.zeigprofilaward.master')

<script type="text/javascript" src="{{url('inc/js/zeigprofilaward/jquery-3.6.0.min.js')}}"></script>
<script type="text/javascript" src="{{url('inc/js/zeigprofilaward/zeigprofilaward_backend.js')}}"></script>

@section('content')
    <div class="row mb-5">
        <div class="col-12 mb-3">
            <h2>ERGEBNISSE Zeigprofil Award 2024</h2>
        </div>
        <div class="col-12">
            <div class="d-flex justify-content-between">
                <div>
                    <a class="btn btn-brown" href="{{url('backend/zeigprofilaward')}}">Zurück</a>
                </div>

            </div>
            <hr>
        </div>
        <div class="col-6">
            <div class="card h-100 border-success text-center">
                <div class="card-body py-4">
                    <h3>
                        <div class="fw-bold mb-2">Votings bereinigt</div>
                        {{number_format($votes_clean->count())}} </h3>
                </div>
            </div>

        </div>
        <div class="col-6">
            <div class="card h-100  ">
                <div class="card-body  py-4 text-center">
                    <h3>
                        <div class="fw-bold mb-2">Votings gesamt</div>
                        {{number_format($votes_total->count())}}
                    </h3>
                </div>
            </div>
        </div>
        @foreach($getCategories as $cat)
            @if($cat->submits->count())
                <div class="col-12 my-3">
                    <div class="row">
                        <div class="col-12">
                            <h3>{{$cat->title}}</h3>
                        </div>
                        <div class="col-12">
                            <div class="row mb-3">
                                <div class="col-12">
                                    <span>Bereinigt</span>
                                </div>
                                @foreach($cat->submits as $submit)
                                    <div class="col-2">
                                        <div class="card h-100 border-success">
                                            <div class="card-body">
                                                <div class="fw-bold">
                                                    {!! $submit->company !!}
                                                </div>
                                            </div>
                                            <div class="card-footer">
                                               {{-- {{dd($submit->votes)}}--}}
                                                <b>{{ ($submit->votes->count()) ? round(($submit->votes->count() / $categoryvotes[$cat->title]) * 100, 2) : 0 }}%</b>
                                                <small>({{$submit->votes->count()}})</small>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                            <div class="row">
                                <div class="col-12">
                                    <span>Original</span>
                                </div>
                                @foreach($cat->submits as $submit)
                                    <div class="col-2">
                                        <div class="card h-100 ">
                                            <div class="card-body">
                                                <div class="fw-bold">
                                                    {!! $submit->company !!}
                                                </div>
                                            </div>
                                            <div class="card-footer">
                                                <b>{{ ($submit->votes->count()) ? round(($submit->votes()->withTrashed()->count() / $categoryvotesclean[$cat->title]) * 100, 2) : 0 }}%</b>
                                                <small>({{$submit->votes()->withTrashed()->count()}})</small>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>

                        </div>

                    </div>
                </div>
            @endif
        @endforeach

    </div>
@endsection
