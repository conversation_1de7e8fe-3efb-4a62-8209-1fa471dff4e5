@extends('backend.master')

@section('content')
    <div class="row my-4">
        <div class="col-12">
            <h2 class="mb-3">Zeig Profil Award - Homepage verwalten</h2>
        </div>
        <div class="col-12">
            <div class="d-flex justify-content-between">
                <div>
                    <a class="btn btn-brown" href="{{url('backend/zeigprofilaward')}}">Zurück</a>
                </div>
            </div>
            <hr>
            <form action="{{ url()->current() }}" method="post" enctype="multipart/form-data" class="">
                {{ csrf_field() }}
                @method('POST')
                @foreach($formFields as $index => $data)
                    <div class="col-lg-8    mb-3">
                        @if(!empty($data['currentPreview']))
                            <!-- Modal -->
                            <div class="modal fade" id="modal-preview-{{$index}}" tabindex="-1"
                                 aria-labelledby="modal-preview-{{$index}}Label" aria-hidden="true">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            <img src="{{$data['currentPreview']}}" alt="" class="img-fluid mb-3">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif

                        <div class="d-flex align-items-center w-100">
                            @php
                                if(!empty($data['currentPreview'])){
                                    $data['label'] .= '
                                        <a href="#" type="button"
                                        class="link-success ms-2"
                                        data-bs-toggle="modal"
                                        data-bs-target="#modal-preview-'.$index.'">
                                             Aktuelles Bild ansehen
                                        </a>
                                     ';
                                }
                            @endphp

                            @include('backend.components.input', ['data' => $data])
                        </div>
                    </div>
                @endforeach
                <div class="text-end col-lg-12 border-top pt-3">
                    <button type="submit" class="btn btn-brown">Speichern</button>
                </div>
            </form>
        </div>
    </div>
@endsection
