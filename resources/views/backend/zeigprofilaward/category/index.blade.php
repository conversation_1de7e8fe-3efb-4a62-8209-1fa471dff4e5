@extends('backend.master')

@section('content')
    <div class="row my-4">
        <div class="col-12">
            <h2 class="mb-3">Zeig Profil Award - Kate<PERSON><PERSON> verwalten</h2>
        </div>
        <div class="col-12">
            <div class="d-flex justify-content-between">
                <div>
                    <a class="btn btn-brown" href="{{url('backend/zeigprofilaward')}}">Zurück</a>
                </div>
            </div>
            <hr>
            <form action="{{ url()->current() }}" method="post" enctype="multipart/form-data">
                {{ csrf_field() }}
                @method('POST')

                <div class="row">
                    @foreach($allFormFields as  $formFields)
                        <div class="col-lg-6 mb-3">
                            <div class="border p-3">
                                @foreach($formFields as $index => $data)
                                    <div class="mb-3">
                                        @if(!empty($data['currentPreview']))
                                            <div class="w-100 text-center">
                                                <img src="{{$data['currentPreview']}}" alt="" class="mb-3" style="max-height: 15rem">
                                            </div>
                                        @endif
                                        <div class="d-flex align-items-center w-100">
                                            @include('backend.components.input', ['data' => $data])
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endforeach
                </div>
                <div class="text-end col-lg-12   pt-3">
                    <button type="submit" class="btn btn-brown">Speichern</button>
                </div>
            </form>
        </div>
    </div>
@endsection
