@extends('backend.zeigprofilaward.master')
@section('content')
    <div class="row">
        <div class="col-12 mb-3">
            <h2>Zeig Profil Award - Partnerunternehmen anlegen/bearbeiten</h2>
        </div>
        <div class="col-12">
            <div class="d-flex justify-content-between">
                <div>
                    <a class="btn btn-brown" href="{{url('backend/zeigprofilaward/partner')}}">Zurück</a>
                </div>
            </div>
            <hr>
            <form action="{{ url("backend/zeigprofilaward/partner/".$partner->id) }}" id="" name="" method="post"
                  enctype="multipart/form-data">
                @method('POST')
                {{ csrf_field() }}

                <div class="mb-3">
                    @if($partner->logoFullPath)
                        <div class="d-flex align-items-center">
                            <img src="{{url($partner->logoFullPath)}}" alt="" style="max-height: 15rem; max-width:50%">
                            <div class="ps-3" style="flex:1">
                                <label for="logo">Logo austauschen</label>
                                <input type="file" class="form-control" id="logo" name="logo" accept="image/*">
                            </div>
                        </div>
                    @else
                        <label for="logo">Logo</label>
                        <input type="file" class="form-control" id="logo" name="logo" accept="image/*">
                    @endif
                </div>
                <div class="mb-3">
                    <label for="name">Name</label>
                    <input type="text" class="form-control w-100" id="name" name="name"
                           value="{{ old('name', $partner->name) }}">
                </div>
                <div class="mb-3">
                    <label for="logoAlt">Logo Alt</label>
                    <input type="text" class="form-control w-100" id="logoAlt" name="logoAlt"
                           value="{{ old('logoAlt', $partner->logoAlt) }}">
                </div>
                <div class="mb-3">
                    <label for="link">Link</label>
                    <input type="text" class="form-control w-100" id="link" name="link"
                           value="{{ old('link', $partner->link) }}">
                </div>
                <div class="d-flex justify-content-end">
                    <button type="submit" class="btn btn-brown">Speichern</button>
                </div>
            </form>
        </div>
    </div>
@endsection
