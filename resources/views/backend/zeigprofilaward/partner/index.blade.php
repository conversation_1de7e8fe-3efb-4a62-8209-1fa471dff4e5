@extends('backend.zeigprofilaward.master')

@section('content')
    <div class="row mb-5">
        <div class="col-12 mb-3">
            <h2>Zeig Profil Award - Partner<PERSON><PERSON><PERSON><PERSON> ver<PERSON></h2>
        </div>
        <div class="col-12">
            <div class="d-flex justify-content-between">
                <div>
                    <a class="btn btn-brown" href="{{url('backend/zeigprofilaward')}}">Zurück</a>
                </div>
                <form action="{{ url("backend/zeigprofilaward/partner") }}" method="post">
                    {{ csrf_field() }}
                    <div class="form-group">
                        <button type="submit" class="btn btn-brown">
                            <i class="fa-solid fa-plus"></i> NEU
                        </button>
                    </div>
                </form>
            </div>
            <hr>
            <div class="sortable-js">
                @foreach($partners as $partner)
                    <div class="modal fade" id="delete-partner-{{$partner->id}}-modal"
                         aria-hidden="true">
                        <div class="modal-dialog" role="document">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title"><i class="fa-solid fa-trash"></i></h5>
                                    <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                                        <span aria-hidden="true">&times;</span>
                                    </button>
                                </div>
                                <div class="modal-body">
                                    <p>Wirklich löschen?</p>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Abbrechen</button>

                                    <form action="{{ url("backend/zeigprofilaward/partner/".$partner->id."/delete") }}" method="post">
                                        {{ csrf_field() }}
                                        <input name="_method" type="hidden" value="delete">
                                        <button type="submit" class="btn btn-danger ms-3"><i class="fa-solid fa-trash"></i> Löschen</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div data-sortable-id="{{$partner->id}}" class="d-flex align-items-center border-bottom p-3 mb-3 {{ ($partner->active==1) ? "" : "bg-light" }}" title="{{ ($partner->active==1) ? "Logo ist eingeblendet" : "Logo ist ausgeblendet" }}">
                        <div class="handle p-2 ">
                            <i class="fa-solid fa-up-down-left-right fs-2"></i>
                        </div>
                        <div class="ms-2 " style="max-width: 20rem;">
                            <img src="{{$partner->logoFullPath}}" alt="{{$partner->logoAlt}}" class="mw-100">
                        </div>
                        <div class="ms-3">
                            <table class="fs-5">
                                <tr>
                                    <td class="fw-bold pe-2">Name</td>
                                    <td>{{$partner->name}}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold pe-2">Alt</td>
                                    <td>{{$partner->logoAlt}}</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold pe-2">Link</td>
                                    <td>{{$partner->link}}</td>
                                </tr>
                            </table>
                        </div>
                        <div style="flex: 1" class="d-flex justify-content-end">

                            @if($partner->active==1)
                                <a href="{{url('backend/zeigprofilaward/partner/'.$partner->id.'/active/'.$partner->active)}}" class="btn btn-primary me-3" title="Ausblenden">
                                    <i class="fa-solid fa-eye-slash"></i>
                                </a>
                            @else
                                <a href="{{url('backend/zeigprofilaward/partner/'.$partner->id.'/active/'.$partner->active)}}" class="btn btn-info text-white me-3" title="Einblenden">
                                    <i class="fa-solid fa-eye"></i>
                                </a>
                            @endif

                            <a href="{{url('backend/zeigprofilaward/partner/' . $partner->id)}}" class="btn btn-brown" title="Bearbeiten">
                                <i class="fa-regular fa-pen-to-square"></i>
                            </a>
                            <button type="button" class="btn btn-danger ms-3" data-bs-toggle="modal" data-bs-target="#delete-partner-{{$partner->id}}-modal" title="Löschen">
                                <i class="fa-solid fa-trash"></i>
                            </button>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>

    <style>
        .handle {
            cursor: grab;
        }

        .handle:hover {
            color: #07ED89;
        }
    </style>

    <script src="{{url('inc/sortableJS/sortableJS.js')}}"></script>

    <script>
        const endpoint = `{{url('backend/ajax/zeigprofilaward/partner/sort')}}`;

        [...document.getElementsByClassName('sortable-js')].forEach(el => {

            Sortable.create(el, {
                ghostClass : 'ghost',
                direction : 'vertical',
                handle : '.handle',
                onEnd : () => {
                    /*
                    *  array of datasets [{sort: '0', id: '12'}]
                    */
                    const newOrder = Array.from(el.querySelectorAll(`[data-sortable-id]`))
                                          .map((element, index) => {
                                              return {
                                                  id : element.dataset.sortableId,
                                                  sort : index
                                              }
                                          })


                    fetch(endpoint,
                          {
                              method : 'POST',
                              headers : {
                                  'X-CSRF-TOKEN' : "{{ csrf_token() }}",
                                  'Content-Type' : 'application/json'
                              },
                              body : JSON.stringify({ orderArray : newOrder }),

                          }
                    )
                        .then(response => response.json())
                        .then(json => console.log(json))
                        .catch((e) => {
                            // On Error reload the Page
                            console.log(e)
                            //location.reload()
                        });


                }
            })
        })
    </script>
@endsection
