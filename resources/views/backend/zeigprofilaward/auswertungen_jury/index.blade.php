@extends('backend.zeigprofilaward.master')

<script type="text/javascript" src="{{url('inc/js/zeigprofilaward/jquery-3.6.0.min.js')}}"></script>
<script type="text/javascript" src="{{url('inc/js/zeigprofilaward/zeigprofilaward_backend.js')}}"></script>

@section('content')
	<div class="row mb-5">
		<div class="col-12 mb-3">
			<h2>ERGEBNISSE Zeigprofil Award 2024</h2>
		</div>
		<div class="col-12">
			<div class="d-flex justify-content-between">
				<div>
					<a class="btn btn-brown" href="{{url('backend/zeigprofilaward')}}">Zurück</a>
				</div>

			</div>
			<hr>
		</div>
		<div class="col-6">
			<div class="card h-100  ">
				<div class="card-body  py-4 text-center">
					<h3>
						<div class="fw-bold mb-2">Votings gesamt</div>
						{{number_format($votes_total->count())}}
					</h3>
				</div>
			</div>
		</div>
		@foreach($getCategories as $cat)
			@if($cat->jurySubmits->count())
				<div class="col-12 my-3">
					<div class="row">
						<div class="col-12">
							<h3>{{$cat->title}}</h3>
						</div>
						<div class="col-12">
							<div class="row">
								<div class="col-12">
									<span>Original</span>
								</div>
								@foreach($cat->jurySubmits as $submit)

									<div class="col-2">
										<div class="card h-100 ">
											<div class="card-body">
												<div class="fw-bold">
													{!! $submit->company !!}
												</div>
											</div>
											<div class="card-footer">
												@if($submit->votesJury->count() > 0 && $categoryvotes[$cat->title])

													<b>{{ round(($submit->votesJury->where('category_id', '!=',6)->count() / $categoryvotes[$cat->title]) * 100, 2) }}%</b>
												@else
													<b>0%</b>
												@endif
												<small>({{$submit->votesJury->where('category_id', '!=',6)->count()}})</small>
											</div>
											@foreach($submit->votesJury->where('category_id', '!=',6) as $vote)
												<span class="badge bg-dark m-1">
                                                        {{\App\Models\ZeigProfilAward\ZeigProfilAwardJury::where('hash', $vote->hash)->first()->vorname}}
													{{\App\Models\ZeigProfilAward\ZeigProfilAwardJury::where('hash', $vote->hash)->first()->nachname}}
                                                    </span>
											@endforeach
										</div>

									</div>
								@endforeach
							</div>

						</div>

					</div>
				</div>

			@else
				<div class="col-12 my-3">
					<div class="row">
						<div class="col-12">
							<h3>{{$cat->title}}</h3>
						</div>
						<div class="col-12">

							<div class="row">
								<div class="col-12">
									<span>Original</span>
								</div>
								@foreach(\App\Models\ZeigProfilAward\ZeigProfilAwardSubmit::where('epu_voting_freigabe', 1)->get() as $submit)
									<div class="col-2">
										<div class="card h-100 ">
											<div class="card-body">
												<div class="fw-bold">
													{!! $submit->company !!}
												</div>
											</div>
											<div class="card-footer">
												@if(($submit->votesJury->count() > 0) && $categoryvotes[$cat->title])
													<b>{{ round(($submit->votesJury->where('category_id', 6)->count() / $categoryvotes[$cat->title]) * 100, 2) }}%</b>
												@else
													<b>0%</b>

												@endif
												<small>({{$submit->votesJury->where('category_id', 6)->count()}})</small>
											</div>

											@foreach($submit->votesJury->where('category_id',6) as $vote)
												<span class="badge bg-dark m-1">
                                                        {{\App\Models\ZeigProfilAward\ZeigProfilAwardJury::where('hash', $vote->hash)->first()->vorname}}
													{{\App\Models\ZeigProfilAward\ZeigProfilAwardJury::where('hash', $vote->hash)->first()->nachname}}
                                                    </span>
											@endforeach
										</div>
									</div>
								@endforeach
							</div>

						</div>

					</div>
				</div>
			@endif
		@endforeach

    </div>
@endsection
