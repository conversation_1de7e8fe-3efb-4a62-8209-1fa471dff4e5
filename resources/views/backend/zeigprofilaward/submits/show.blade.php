@extends('backend.zeigprofilaward.master')

@section('content')

    <div class="row">
        <div class="col-12 mb-3">
            <h2>Zeig Profil Award - Einreichung
                <span class="text-brown3 ms-2">#{{$submit->id}}</span>
            </h2>
        </div>
        <div class="col-12">
            <div class="d-flex justify-content-start">
                <a class="btn btn-brown me-2" href="{{url('backend/zeigprofilaward')}}">Zurück</a>
                <a class="btn btn-brown me-2" href="{{url('backend/zeigprofilaward/submits')}}">Zurück zu den Einreichungen</a>
            </div>
            <hr>
        </div>


        <div class="col-12 mb-3">
            <div class="card">
                <div class="card-body">

                    <div class="row">
                        <div class="col-12">
                            <h4 class="text-brown3">
                                {{$submit->company}}
                            </h4>
                        </div>
                        {{-- INFOS FIRMA --}}

                        <div class="col-12 col-md-8 p-2">
                            <form action="{{url('/').'/backend/zeigprofilaward/submits/'.$submit->id.'/update'}}" method="POST" enctype="multipart/form-data">
                                @csrf
                                <div class="row mb-2">
                                    @include("zeigprofilaward.partials.textfield", ["option" => ["col" => "col-md-12 mb-3", "type" => "text", "name" => "firstname", "placeholder" => "Vorname", "value"=>$submit->firstname , "required" => false]])
                                </div>

                                <div class="row mb-2">
                                    @include("zeigprofilaward.partials.textfield", ["option" => ["col" => "col-md-12 mb-3", "type" => "text", "name" => "lastname", "value" => $submit->lastname, "placeholder"=>"Nachname", "required" => false]])
                                </div>

                                <div class="row mb-2">
                                    @include("zeigprofilaward.partials.textfield", ["option" => ["col" => "col-md-12 mb-3", "type" => "email", "name" => "email", "placeholder"=>"E-mail", "value" => $submit->email, "required" => false]])
                                </div>
                                <div class="row mb-3">
                                    @include("zeigprofilaward.partials.textfield", ["option" => ["col" => "col-md-12", "type" => "text", "name" => "company", "value" => $submit->company,"placeholder"=>"Firma", "required" => false]])
                                </div>
                                <div class="row mb-2">

                                    @if(!empty($submit->founding_date))
                                        @include("zeigprofilaward.partials.textfield", ["option" => ["col" => "col-md-4 mb-3", "type" => "date", "name" => "founding_date", "placeholder"=>"Gründungsdatum" , "value" => $submit->founding_date, "required" => false]])
                                        @include("zeigprofilaward.partials.textfield", ["option" => ["col" => "col-md-8 mb-3", "type" => "text", "name" => "founding_place", "placeholder" => "Gründungsort", "value" => $submit->founding_place,  "required" => false]])
                                    @else
                                        @include("zeigprofilaward.partials.textfield", ["option" => ["col" => "col-md-4 mb-3", "type" => "date", "name" => "founding_date", "value" => "Gründungsdatum","placeholder" => "Gründungsdatum", "required" => false]])
                                        @include("zeigprofilaward.partials.textfield", ["option" => ["col" => "col-md-8 mb-3", "type" => "text", "name" => "founding_place", "value" => $submit->founding_place, "placeholder" => "Gründungsort","required" => false]])
                                    @endif
                                </div>
                                <div class="row mb-2">
                                    <div class="col-md-6 col-12">
                                        <div class="row">
                                            <div class="col-12 col-md-12 d-flex align-items-center ">
                                                <label class="fw-bold">Katgegorie</label>
                                            </div>
                                            <div class="col-12 col-md-12 mb-3">
                                                <div class="form-floating">
                                                    <select class="form-select" id="category" name="category">
                                                        <option value="">--- Bitte wählen ---</option>
                                                        @foreach($categories as $category)
                                                            <option value="{{$category->id}}" {{ ($category->id == $categories_arr[$submit->category]["id"]) ? "selected" : "" }}>{{$category->title}}</option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                   {{-- <div class="col-md-6 col-12">
                                        <div class="row mb-3">
                                            <div class="col-12 col-md-12 d-flex align-items-center ">
                                                <label class="fw-bold">EPU Ja/Nein</label>
                                            </div>
                                            <div class="col-12 col-md-12 text-start">
                                                <div class="form-floating">
                                                    <select class="form-select" id="epu" name="epu">
                                                        <option value="">--- Bitte wählen ---</option>
                                                        <option value="J" {{ ($submit->epu == "J") ? "selected" : "" }}>Ja</option>
                                                        <option value="N" {{ ($submit->epu == "N") ? "selected" : "" }}>Nein</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>--}}
                                </div>
                                <div class="row mb-2">
                                    @include("zeigprofilaward.partials.textfield", ["option" => ["col" => "col-md-12 mb-3", "type" => "text", "name" => "mission_statement","placeholder"=>"Unternehmensphilosophie" ,"value" => $submit->mission_statement, "required" => false]])
                                </div>

                                <div class="row mb-2">
                                    @include("zeigprofilaward.partials.textfield", ["option" => ["col" => "col-md-12 mb-3", "type" => "text", "name" => "claim","placeholder"=>"Claim/Slogan", "value" => $submit->claim, "required" => false]])
                                </div>
                                <div class="row mb-2">
                                    @include("zeigprofilaward.partials.textfield", ["option" => ["col" => "col-md-12 mb-3", "type" => "text", "name" => "project_goal", "placeholder" => "Unternehmensidee/-ziel","value"=>$submit->project_goal, "required" => true]])
                                </div>

                               {{-- <div class="row mb-2">
                                    @include("zeigprofilaward.partials.textfield", ["option" => ["col" => "col-md-12 mb-3", "type" => "text", "name" => "credentials", "value" => $submit->credentials, "placeholder"=>"Credentials/Referenzen","required" => false]])
                                </div>--}}
                                <div class="row">
                                    <div class="col-12 mb-3">
                                        <label for="company_logo" class="form-label"> Unternehmenslogo*</label>
                                        <input type="file" class="form-control" id="company_logo" name="company_logo" />
                                    </div>

                                    <div class="col-12 mb-3">
                                        <label for="company_picture" class="form-label">Maßnahmen und Umsetzung</label>
                                        <input type="file" class="form-control" id="company_picture" name="company_picture">
                                    </div>

                                    <div class="col-12 mb-3">
                                        <label for="company_skizze" class="form-label">Referenzen*
                                            <small>(PDF, max. 10MB)</small>
                                        </label>
                                        <input type="file" accept="application/pdf" class="form-control" id="company_skizze" name="company_skizze">
                                    </div>

                                    <div class="col-12 mb-3">
                                        <label for="project_picture" class="form-label">Unternehmenspräsentation</label>
                                        <input type="file" class="form-control" id="project_picture" name="project_picture">
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-12">
                                        <button type="submit" class="btn btn-success float-end">Speichern</button>
                                    </div>
                                </div>
                            </form>
                        </div>

                        {{-- INFOS EINREICHER --}}
                        @if(!empty($files) && $files != false)
                            <div class="col-12 col-md-3  p-2">
                                <div class="row">
                                    <div class="col-12">
                                        <label class="fw-bold">Hochgeladene Dateien</label>
                                    </div>
                                    <div class="col-12">
                                        <ul class="list-group ">
                                            @foreach($files  as $file)
                                                @continue($file == "." || $file == "..")
                                                <li class="list-group-item">
                                                    @if(is_file(storage_path("app/public/".$filespath.DIRECTORY_SEPARATOR.$file)))
                                                        <a href="{{ url("storage/app/public/".$filespath.DIRECTORY_SEPARATOR.$file) }}" target="_blank">
                                                            <i class="fa-solid fa-file-arrow-down text-primary me-2"></i>{{$file}}
                                                        </a>
                                                    @else
                                                        <span class="text-danger">
															<i class="fa-solid fa-file-slash"></i>
															{{$file}}
														</span>
                                                    @endif
                                                </li>
                                            @endforeach
                                        </ul>
                                        <hr>
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

    </div>
@endsection
