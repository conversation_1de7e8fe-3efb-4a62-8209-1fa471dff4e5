@extends('backend.zeigprofilaward.master')

<script type="text/javascript" src="{{url('inc/js/zeigprofilaward/jquery-3.6.0.min.js')}}"></script>
<script type="text/javascript" src="{{url('inc/js/zeigprofilaward/zeigprofilaward_backend.js')}}"></script>

@section('content')
	<div class="row mb-5">
		<div class="col-12 mb-3">
			<h2>Zeig Profil Award - Einreichungen</h2>
		</div>
		<div class="col-12">
			<div class="d-flex justify-content-between">
				<div>
					<a class="btn btn-brown" href="{{url('backend/zeigprofilaward')}}">Zurück</a>
				</div>
                <div>
                    <a class="btn btn-success" href="{{route('zeigprofil.export.excel')}}"><i class="fa-solid fa-file-xls"></i> E-Mail-Liste </a>
                </div>
			</div>
			<hr>
		</div>
		<div class="col-12">
			<table class="table table-vertical-middle table-bordered" datatable="true" data-paging="false" data-order="[[ 0, &quot;asc&quot; ]]">
				<thead class="table-dark">
				<tr>
					<th data-orderable="true">#</th>
					<th data-orderable="true">Name</th>
					<th data-orderable="true">Firmenname <br>
						<span class="fst-italic small">(Gründung)</span>
					</th>
					<th data-orderable="true">Kategorie</th>
					<th data-orderable="false" style="max-width: 20px;">&nbsp;</th>
					<th data-orderable="false">Freigabe <br>
                        <span class="fst-italic small">(Public Voting)</span>
                    </th>
                    <th data-orderable="false">Freigabe <br>
                        <span class="fst-italic small">(Jury Voting)</span>
                    </th>
                    <th data-orderable="false">Freigabe <br>
                        <span class="fst-italic small">(EPU Voting)</span>
                    </th>
				</tr>
				</thead>
				<tbody>
				@foreach($submits_coll as $submit)
					<tr>
						<td>{{$submit->id}}</td>
						<td data-sort="{{$submit->lastname}}">{{strtoupper($submit->lastname)}} {{ $submit->firstname }}
							<br>
							<span class="badge text-bg-light p-1 pb-0">
								<a href="mailto:{{$submit->email}}" title="E-Mail verfassen">{{$submit->email}}</a>
							</span>
						</td>
						<td data-sort="{{$submit->company}}">
							<strong>{{$submit->company}}</strong>
							@if($submit->epu == "J")
								<span class="badge text-bg-warning p-1 pb-0" is-epu title="Ein-Personen-Unternehmen (EPU)">EPU</span>
							@endif
							<span class="fst-italic small d-block">
								<span>{{\Illuminate\Support\Carbon::parse($submit->founding_date)->format("d.m.Y")}}</span>, <span>{{$submit->founding_place}}</span>
							</span>
						</td>
						<td>
							{{array_key_exists($submit->category, $categories_arr) ? $categories_arr[$submit->category]["title"] : '-'}}
						</td>
						<td>
							 <a href="{{url("backend/zeigprofilaward/submits/".$submit->id)}}" class="btn btn-secondary btn-sm" target="_blank">
								 <i class="fa-solid fa-magnifying-glass"></i>
							 </a>
						</td>
                        <td>
                            <div class="form-check form-switch d-flex justify-content-center ">
                                <input class="form-check-input" {{($submit->public_voting_freigabe == 1) ? 'checked="true"' : ''}} type="checkbox" role="switch" id="publicVoting_{{$submit->id}}" data-submit="{{$submit->id}}">
                            </div>
                        </td>
                        <td>
                            <div class="form-check form-switch d-flex justify-content-center">
                                <input class="form-check-input" {{($submit->jury_voting_freigabe == 1) ? 'checked="true"' : ''}} type="checkbox" role="switch" id="juryVoting_{{$submit->id}}" data-submit="{{$submit->id}}">
                            </div>
                        </td>
                        <td>
                            <div class="form-check form-switch d-flex justify-content-center">
                                <input class="form-check-input" {{($submit->epu_voting_freigabe == 1) ? 'checked="true"' : ''}} type="checkbox" role="switch" id="epuVoting_{{$submit->id}}" data-submit="{{$submit->id}}">
                            </div>
                        </td>
					</tr>
				@endforeach
				</tbody>
			</table>
		</div>
	</div>
@endsection
