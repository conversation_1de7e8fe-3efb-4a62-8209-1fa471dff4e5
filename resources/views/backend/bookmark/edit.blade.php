@extends('backend.master')

@section('content')
	<div class="card">
		<div class="card-body">
			<div class="row mb-4">
				<div class="col-12">
					<h2 class="card-title">Bookmark bearbeiten</h2>
				</div>
			</div>
			<form action="{{ url("backend/bookmark") }}" id="bookmarkForm" name="bookmarkForm" method="post">
				{{ csrf_field() }}
				<div class="table-warning mb-4 p-3">
					<div class="row">
						<div class="col-12 col-md-6">
							<div class="form-group">
								<label for="description">Bezeichnung *</label>
								<input type="text" id="description" class="form-control mb-3 " name="description" value="{{ old('description', $bookmark->description) }}">
								{!!  ($errors->has("description")) ? "<small class='form-text text-danger'><span class='fas fa-exclamation-circle'></span> ".join('<br/>', $errors->get('description'))."</small>" : "" !!}
							</div>
						</div>
					</div>
					<div class="row">
						<div class="col-12 col-md-6">
							<div class="form-group">
								<label for="parent">Übergeordnetes Element *</label>
								<select name="parent" id="parent" class="custom-select" select2="true">
									<option value="0" {{ (old("parent", $bookmark->parent) == "0") ? "selected" : ''  }}></option>
									{{\App\Facades\MPHelper::printTree($filter)}}
								</select>

								{!!  ($errors->has("parent")) ? "<small class='form-text text-danger'><span class='fas fa-exclamation-circle'></span> ".join('<br/>', $errors->get('parent'))."</small>" : "" !!}
							</div>
						</div>
					</div>

					<div class="row">
						<div class="col-12">
							<hr>
						</div>

						<div class="col-12  text-end">
							<button type="button" submit="true" loadicon="true" class="btn btn-success">
								<span class="fas fa-save"></span>
								Speichern
							</button>
						</div>
					</div>
				</div>
			</form>
		</div>
	</div>
@endsection
