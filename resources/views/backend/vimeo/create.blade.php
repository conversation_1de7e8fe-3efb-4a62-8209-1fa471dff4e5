@extends('backend.master')
@section('content')

    {{--Add Domain Modal--}}
    <div class="modal fade" id="addDomainModal" tabindex="-1" aria-labelledby="addDomainModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addDomainModalLabel">Domain hinzufügen</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <input type="text" class="form-control" name="newDomain" id="new-domain">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">Abb<PERSON><PERSON>nen</button>
                    <button type="button" class="btn btn-brown" data-bs-dismiss="modal"
                            onclick="addDomain( document.getElementById('new-domain').value);document.getElementById('new-domain').value=''; ">Domain hinzufügen
                    </button>
                </div>
            </div>
        </div>
    </div>



    <div class="row">
        <div class="col-12 my-3" id="upload-form">
            @if($ready_to_upload)
                <form action="{{ $upload_link }}" method="POST" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="input-file" class="form-label">Video</label>
                        <input type="file" name="file_data" id="input-file" class="form-control" accept="video/*" required>
                    </div>
                    <div class="mb-3">
                        <button type="submit" class="btn btn-brown" onclick="onSubmit()">Hochladen</button>
                    </div>
                </form>
            @else
                <form action="{{ $initialize_path }}" method="post" enctype="multipart/form-data">
                    {{ csrf_field() }}
                    <div class="mb-3">
                        <label for="input-title" class="form-label">Titel</label>
                        <input id="input-title" type="text" class="form-control" name="title">
                    </div>
                    <div class="mb-3">
                        <label for="input-desc" class="form-label">Beschreibung</label>
                        <textarea id="input-desc" class="form-control" name="description"></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="select-preset" class="form-label">Preset auswählen</label>
                        <select class="form-select"
                                id="select-preset"
                                aria-label=" select preset"
                                name="preset">
                            <option selected value="">Keine Voreinstellung</option>
                            @foreach($presets as $preset)
                                <option value="{{$preset->id}}">
                                    {{$preset->name}}
                                </option>
                            @endforeach
                        </select>
                    </div>


                    <div class="mb-3">
                        <label for="whitelist">
                            White List

                            <br> <small>*Wo kann dieses Video eingebunden werden?</small>
                        </label>
                        <input type="hidden" name="whitelist" id="input-white-list">

                        <div class="input-group mb-3">
                            <div class="form-control " data-badges="#input-white-list">
                                {{--js injected--}}
                            </div>
                            <span class="input-group-text" data-bs-toggle="modal" data-bs-target="#addDomainModal">
                              <i class="far fa-plus " style="cursor:pointer"></i>
                            </span>
                        </div>


                    </div>

                    <div class="mb-3">
                        <button type="submit" class="btn btn-brown">Speichern</button>
                    </div>
                </form>
            @endif
        </div>
        <div class="col-12 d-none my-3" id="upload-spinner">
            <div class="d-block">
                <div class="text-center">
                    <i class="fad fa-spinner-third  fa-spin " style="font-size:8rem; color: #c7a28d"></i>
                </div>
                <div class="text-center mt-3">
                    Schließen Sie diesen Tab bitte nicht, während das Video hochgeladen wird.
                </div>
            </div>
        </div>
    </div>

    <script src="{{url('inc/js/vimeo/functions.js')}}"></script>

    <script>

        addDomain(`{{url('')}}`)

        function onSubmit(){
            document.getElementById('upload-form').classList.add('d-none');
            document.getElementById('upload-spinner').classList.remove('d-none')
        }
    </script>
@endsection
