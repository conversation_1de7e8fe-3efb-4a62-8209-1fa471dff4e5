@extends('backend.master')
@section('content')




    @if(session()->has('message'))
        <div class="alert alert-success mt-2" onclick="this.style.display= 'none'">
            {{ session()->get('message') }}
        </div>
    @endif
    <div class="row">
        <div class="col-12">
            <div class="text-end">
                <div class="form-group">
                    <a href="{{$create_link}}" class="btn btn-brown">
                        Hochladen
                    </a>
                </div>
            </div>
        </div>
        <div class="col-12 pt-3">
            <div data-vimeo-player></div>
            <div data-vimeo-player-actions class="p-3 border text-end d-none">
                <button type="button" class="btn btn-brown" onclick="watchVideo(false)">Close</button>
            </div>
        </div>
    </div>
    <div class="row pt-3" data-main-row>
        @foreach($videos as $index => $vid)
            <div class="col-md-6 p-2" data-video="{{json_encode($vid)}}">
                @include('backend.vimeo.partials.video-card',
                                           [
                                            'index' => $index,
                                            'video' => $vid
                                           ])
            </div>
        @endforeach
    </div>
    <footer class="py-5 border-top mt-3">
        <small>@vimeo api</small>
    </footer>
    <script>
        class Main {
            videosApiJson = `{{$videos_json}}`;
            allVideoObjects = []
            container = null;
            allVideos = [];
            vimeoPlayer;
            vimeoPlayerActions;
            apiTimeout;

            constructor(){
                this.container = document.querySelector('[data-main-row]')
                this.allVideos = this.getVideoObjects()
                this.vimeoPlayer = document.querySelector('[data-vimeo-player]')
                this.vimeoPlayerActions = document.querySelector('[data-vimeo-player-actions]')
            }

            watch(videoId){
                this.vimeoPlayer.innerHTML = ''
                this.vimeoPlayerActions.classList.add('d-none')
                if(!videoId) return;

                const baseLink = 'https://player.vimeo.com/video/' + videoId
                const container = document.createElement("div");
                container.className = 'ratio ratio-16x9';
                const player = document.createElement("iframe");
                player.src = baseLink;
                player.allowFullscreen = true;
                container.appendChild(player)
                this.vimeoPlayer.appendChild(container)
                this.vimeoPlayerActions.classList.remove('d-none')
                window.scrollTo({
                                    top : 0,
                                    behavior : 'smooth'
                                });
            }

            fetchAll = () => {
                return fetch(this.videosApiJson)
                    .then(res => res.json())
                    .then(videos => videos)
            }

            getVideoObjects(){
                const nodeList = (this.container) ? [...this.container.querySelectorAll('[data-video]')] : [];
                return nodeList.map(node => {
                    return {
                        domNode : node,
                        video : (node.dataset?.video) ? JSON.parse(node.dataset.video) : {}
                    }
                })
            }

            getInProgress(){
                return this.allVideos.filter((x) => x.video.status === 'in_progress')
            }



            init(){
                console.log("=>(index.blade.php:72) this.allVideos", this.allVideos);
            }
        }

        const actions = new Main();
        actions.init()

        function watchVideo(id){
            actions.watch(id)
        }

    </script>
    <style>
        .accordion-button:not(.collapsed){
            color:inherit;
            background-color:inherit;
        }
        .accordion-button:focus{
            border:1px solid rgba(0, 0, 0, .125);
        }


    </style>
@endsection
