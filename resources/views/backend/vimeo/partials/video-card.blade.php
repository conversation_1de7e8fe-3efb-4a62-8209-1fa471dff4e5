<div class="card border">
    <div class="w-100">
        <img src="{{$video->image}}" class="img-fluid" alt="...">
    </div>
    <div class="card-body">
        <div class="card-title fs-3 ">
            {{$video->name}}
        </div>
        <div class="accordion" id="accordion-{{$index}}">
            <div class="accordion-item ">
                <h2 class="accordion-header" >
                    <button class="accordion-button collapsed"
                            type="button"
                            data-bs-toggle="collapse"
                            data-bs-target="#collapse-{{$index}}"
                            aria-expanded="true"
                            aria-controls="collapse-{{$index}}">
                        Einzelheiten
                    </button>
                </h2>
                <div id="collapse-{{$index}}"
                     class="accordion-collapse collapse "
                     data-bs-parent="#accordion-{{$index}}">
                    <div class="accordion-body">
                        <p>{{$video->description}}</p>
                        <hr>
                        <p>ID: {{$video->id}}</p>
                        <p>status: {{$video->status}}</p>
                        <p>created: {{$video->created_at}}</p>
                      {{--  <p>end_screen: {{$video->end_screen}}</p>--}}
                        <p>
                            einbettbar auf:
                            @foreach($video->getEmbedDomains() as $domain)
                                <span class="badge rounded-pill  bg-brown3 me-2 fw-normal">
                                    {{$domain}}
                                </span>
                            @endforeach
                        </p>
                        <p>preset:
                            @foreach($presets as $preset)
                                @if($preset->uri === $video->embed_uri)
                                    <span class="badge rounded-pill  bg-brown3 me-2 fw-normal">{{$preset->name}}</span>
                                @endif
                            @endforeach
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="card-footer bg-white d-flex justify-content-end">
        <a class="ms-2 btn btn-brown" href="{{url()->current() . '/edit/'. $video->id}}">
            Bearbeiten
        </a>
        <button type="button" class="ms-2 btn btn-brown" onclick="watchVideo(`{{$video->id}}`)">
            Watch
        </button>
        <button type="button" class="ms-2 btn btn-brown" data-bs-toggle="modal" data-bs-target="#dialog-{{$index}}">
            Delete
        </button>
    </div>
</div>

@include('backend.vimeo.partials.video-delete-modal', ['target' => "dialog-$index", 'video' => $video])

<!-- Button trigger modal -->

