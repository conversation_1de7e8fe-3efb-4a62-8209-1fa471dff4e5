@extends('backend.master')
@section('content')

    {{--Add Domain Modal--}}
    <div class="modal fade" id="addDomainModal" tabindex="-1" aria-labelledby="addDomainModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addDomainModalLabel">Domain hinzufügen</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <input type="text" class="form-control" name="newDomain" id="new-domain">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">Abb<PERSON><PERSON>nen</button>
                    <button type="button" class="btn btn-brown" data-bs-dismiss="modal"
                            onclick="addDomain( document.getElementById('new-domain').value);document.getElementById('new-domain').value=''; ">Domain hinzufügen
                    </button>
                </div>
            </div>
        </div>
    </div>



    <h2>TUS-VimeoTUS</h2>

    <form action="{{ $create_post}}" id="" name="" method="post" enctype="multipart/form-data" data-vimeo-form>
        {{ csrf_field() }}
        <input name="_method" type="hidden" value="">
        <div class="mb-3">
            <label class="form-label" for="name">name </label>
            <input class="form-control" id="name" data-vimeo-input="name">
        </div>
        <div class="mb-3">
            <label class="form-label" for="description">description </label>
            <textarea class="form-control" id="description" data-vimeo-input="description">
            </textarea>
        </div>


        <div class="mb-3">
            <label for="select-preset" class="form-label">Preset auswählen</label>
            <select class="form-select"
                    id="select-preset"
                    aria-label=" select preset"
                    data-vimeo-input="preset">
                <option selected value="">Keine Voreinstellung</option>
                @foreach($presets as $preset)
                    <option value="{{$preset->id}}">
                        {{$preset->name}}
                    </option>
                @endforeach
            </select>
        </div>

        <div class="mb-3">
            <label for="whitelist">
                White List
                <br> <small>*Wo kann dieses Video eingebunden werden?</small>
            </label>
            <input type="hidden"  id="input-white-list"  data-vimeo-input="whitelist">

            <div class="input-group mb-3">
                <div class="form-control " data-badges="#input-white-list">
                    {{--js injected--}}
                </div>
                <span class="input-group-text" data-bs-toggle="modal" data-bs-target="#addDomainModal">
                              <i class="far fa-plus " style="cursor:pointer"></i>
                            </span>
            </div>
        </div>


        <div class="mb-3">
            <label for="formFileMultiple" class="form-label">Video</label>
            <input name="video" class="form-control" type="file" id="formFileMultiple" data-vimeo-file>
        </div>
        <div class="mb-3">
            <button type="submit" class="btn btn-outline-secondary">Save</button>
        </div>
    </form>

    <div class="row">
        <div class="col-12">
            <div class="text-center d-none" id="before-sending">
                <div>
                    Wird geladet...
                </div>
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>
            <div class="text-center d-none" id="progress-bar">
                <div id="percentage">
                    0%
                </div>
                <div>
                    <div class="progress">
                        <div id="bar" class="progress-bar progress-bar-striped bg-success" role="progressbar" aria-valuemin="0" aria-valuemax="100"
                             style="width: 0"></div>
                    </div>
                </div>
            </div>
            <div class="text-center d-none" id="successfully-uploaded">
                <div id="percentage">
                    Erfolgreich gespeichert!
                </div>
            </div>
        </div>
    </div>

    <script src={{url("inc/tus-js-client/dist/tus.min.js")}}></script>
    <script src={{url("inc/js/vimeo/vimeoTUS.js")}}></script>


    <script src="{{url('inc/js/vimeo/functions.js')}}"></script>



    <script>

        addDomain(`{{url('')}}`)
        /*
        * indicators
        */
        const beforeSending = document.querySelector('#before-sending');
        const progressBar = document.querySelector('#progress-bar');
        const successfullyUploaded = document.querySelector('#successfully-uploaded');


        const form = document.querySelector(`form[data-vimeo-form]`);
        const vimeoTUS = new VimeoTUS(form)

        vimeoTUS.onStart = () => {
            console.log('Onstart')
            form.classList.add('d-none')
            beforeSending.classList.remove('d-none')
        }

        vimeoTUS.onProgress = (bytesUploaded, bytesTotal) => {

            beforeSending.classList.add('d-none')
            progressBar.classList.remove('d-none')

            let percentage = (bytesUploaded / bytesTotal * 100).toFixed(2)

            console.log('uploading', percentage + '%' )

            progressBar.querySelector('#percentage').textContent = percentage + '%'
            progressBar.querySelector('#bar').style.width = percentage + '%'

        }
        vimeoTUS.onSuccess = (upload) => {
            progressBar.classList.add('d-none')
            successfullyUploaded.classList.remove('d-none')
            console.log('upload', upload)
        }


    </script>

@endsection
