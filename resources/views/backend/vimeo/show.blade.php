@extends('backend.master')
@section('content')





    {{--Add Domain Modal--}}
    <div class="modal fade" id="addDomainModal" tabindex="-1" aria-labelledby="addDomainModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addDomainModalLabel">Domain hinzufügen</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <input type="text" class="form-control" name="newDomain" id="new-domain">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">Abb<PERSON><PERSON>nen</button>
                    <button type="button" class="btn btn-brown" data-bs-dismiss="modal"
                            onclick="addDomain( document.getElementById('new-domain').value);document.getElementById('new-domain').value=''; ">Domain hinzufügen
                    </button>
                </div>
            </div>
        </div>
    </div>



    <div class="row  my-3 shadow pt-3">
        <div class="col-12 ">
            <div class="ratio ratio-16x9">
                <iframe

                    name="iframe-{{Str::random(10)}}"
                    src="{{$video->player_url}}" frameborder="0"></iframe>
            </div>
            <div class="p-3 border text- ">
                <p>ID: {{$video->id}}</p>
                <p>Status: {{$video->status}}</p>
                <p>Online: <a href="{{$video->manage_link}}" class="link-danger" target="_blank " referrerpolicy="no-referrer">vimeo.com</a> </p>
                <p class="m-0">Created: {{$video->created_at}}</p>

            </div>
        </div>
        <div class="col-12">
            <form action="{{ $edit }}" id="" name="" method="post" class="pt-3">
                {{ csrf_field() }}
                <input type="hidden" name="current_preset" value="{{$video->preset_id}}">
                <div class="mb-3">
                    <label for="name">Titel</label>
                    <input type="text" class="form-control" id="name" name="name" value="{{$video->name}}">
                </div>
                <div class="mb-3">
                    <label for="description">Beschreibung</label>
                    <textarea name="description" id="description" class="form-control" maxlength="4500" rows="8">{{$video->description}}</textarea>
                </div>
                <div class="mb-3">
                    <label for="preset">Voreinstellung</label>
                    <select class="form-select" aria-label="Default select example" id="preset" name="preset">
                        <option value="" selected>No preset</option>
                        @foreach($presets as $preset)
                            @if($preset->uri === $video->embed_uri)
                                <option selected class="fw-bold" value="{{$preset->id}}">{{$preset->name}}</option>
                            @else
                                <option value="{{$preset->id}}">{{$preset->name}}</option>
                            @endif
                        @endforeach
                    </select>
                </div>


                <div class="mb-3">
                    <label for="whitelist">
                        White List
                        <br> <small>*Wo kann dieses Video eingebunden werden?</small>
                    </label>
                    <input type="hidden" name="whitelist" id="input-white-list" value="{{$whiteListString}}">



                    <div class="input-group mb-3">
                        <div class="form-control h-100" data-badges="#input-white-list">
                            @foreach($whiteList as $domain)
                                <span class="badge rounded-pill  bg-brown3 me-2 fw-normal fs-6">
                                    {{$domain}}   <i class="far fa-times-circle " style="cursor:pointer" onclick="removeDomain(`{{$domain}}`)"></i>
                                </span>
                            @endforeach
                        </div>
                        <span class="input-group-text" data-bs-toggle="modal" data-bs-target="#addDomainModal">
                              <i class="far fa-plus " style="cursor:pointer"></i>
                            </span>
                    </div>


                </div>

                <div class="d-flex justify-content-between mb-3">
                    <div>
                        <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">Delete</button>
                    </div>
                    <div class="text-end">
                        <a href="../" class="btn btn-brown">Zurück</a>
                        <button type="submit" class="btn btn-brown">Speichern</button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    @include('backend.vimeo.partials.video-delete-modal', [ 'target' => 'deleteModal', 'video' => $video])

    <script src="{{url('inc/js/vimeo/functions.js')}}"></script>

@endsection
