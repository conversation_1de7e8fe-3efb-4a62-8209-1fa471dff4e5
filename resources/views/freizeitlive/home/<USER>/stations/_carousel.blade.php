
<div class="swiper  " id="stations-carousel">
    <div class="swiper-wrapper hoverable-slider">

        @foreach($stations as $station)


            <div class="swiper-slide">
                <div data-aos="fade-left" data-aos-delay="{{ $loop->index * 150 }}">
                    @include('freizeitlive.partials.card-station', ["item" => $station, "classes" => 'slide'])
                </div>
            </div>


        @endforeach

    </div>
    <div class="pt-5">
        <div class="swiper-scrollbar d-md-block d-none" style="height: 0.5rem"></div>
        <div class="swiper-pagination d-md-none d-block"></div>
    </div>

</div>


<script>
    new Swiper('#stations-carousel', {
        slidesPerView: 4,
        spaceBetween: 30,
        scrollbar: {
            el: '.swiper-scrollbar',
            draggable: true,
        },
        pagination: {
            el: '.swiper-pagination',
            clickable: true,
        },
        breakpoints: {

            0: {
                slidesPerView: 1,
            },

            768: {
                slidesPerView: 2,
                scrollbar: {
                    el: '.swiper-scrollbar',
                    draggable: true,
                }
            },
            992: {
                slidesPerView: 4,
            },

        }
    });
</script>





{{--




<div class="splide" id="stations-carousel">
    <div class="splide__track">
        <ul class="splide__list">
            @foreach($stations as $station)
                <li class="splide__slide" title="&copy; {{  $station->image_one_cr }}">
                    <div class="splide__slide__container">

                        <div data-aos="fade-left" data-aos-delay="{{ $loop->index * 100 }}">
                            <div class="position-relative text-center slide" >


                                <a href="{{ url("post/$station->id/$station->seo_url") }}" target="_blank"
                                   class="stretched-link "> </a>
                                <p class="mb-0 fw-bold text-nowrap overflow-hidden text-truncate  ">{{ $station->title }}</p>
                                <p class="truncate-2-lines">{{ $station->desc_short }}</p>


                                <img src="{{ $station->imageOneFullPath }}"
                                     class="img-fluid"
                                     alt="{{ $station->image_one_alt }} | &copy; {{ $station->image_one_cr }}"/>

                                @if($type = $station->postType)
                                    <div class="position-absolute bottom-0 p-2 start-0">
                                        <div
                                            class="badge rounded-0 fw-normal"
                                            style="background-color: {{$type->color_2}}">{{$type->name }}</div>
                                    </div>

                                @endif

                            </div>
                        </div>


                    </div>
                </li>
            @endforeach
        </ul>

        <div class="carousel-progress-container">
            <div class="carousel-progress mt-3">
                <div class="carousel-progress-bar"></div>
            </div>
        </div>

    </div>
</div>

<script>


    const stationsCarousel = new Splide('#stations-carousel', {
        // type: 'loop',
        perPage: 5,
        perMove: 1,
        pagination: false,
        arrows: false,
        gap: "1rem",
        breakpoints: {
            1024: {
                perPage: 3, // Tablets
            },
            640: {
                perPage: 1, // Phones
            },
        },
    });

    const stationsCarouselBar = stationsCarousel.root.querySelector('.carousel-progress-bar');

    stationsCarousel.on('mounted move', function () {
        let end = stationsCarousel.Components.Controller.getEnd() + 1;
        let rate = Math.min((stationsCarousel.index + 1) / end, 1);
        stationsCarouselBar.style.width = String(100 * rate) + '%';
    });


    stationsCarousel.mount();

</script>
--}}
