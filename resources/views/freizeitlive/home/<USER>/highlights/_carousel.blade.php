
<div class="swiper  " id="highlights-carousel">
    <div class="swiper-wrapper hoverable-slider">

        @foreach($news as $index => $item)
            <div class="swiper-slide">
                <div data-aos="fade-left" data-aos-delay="{{ $loop->index * 150 }}">
                    @include('freizeitlive.partials.card-highlight', ["classes" => "slide", 'item' => $item])
                </div>
            </div>
        @endforeach

    </div>
    <div class="pt-5">
        <div class="swiper-scrollbar d-md-block d-none" style="height: 0.5rem"></div>
        <div class="swiper-pagination d-md-none d-block"></div>
    </div>

</div>


<script>
    new Swiper('#highlights-carousel', {
        slidesPerView: 3,
        scrollbar: {
            el: '.swiper-scrollbar',
            draggable: true,
        },
        pagination: {
            el: '.swiper-pagination',
            clickable: true,
        },
        breakpoints: {
            // Mobile
            0: {
                slidesPerView: 1,
                scrollbar: {
                    el: null,
                }
            },
            // Tablet and up
            768: {
                slidesPerView: 2,
                scrollbar: {
                    el: '.swiper-scrollbar',
                    draggable: true,
                }
            },
            992: {
                slidesPerView: 3,
            },


        }
    });
</script>


