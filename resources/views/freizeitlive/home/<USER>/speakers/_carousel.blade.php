






<div class="swiper  " id="speakers-carousel">
    <div class="swiper-wrapper hoverable-slider">

        @foreach($speakers as $index => $speaker)


            <div class="swiper-slide">
                <div data-aos="fade-left" data-aos-delay="{{ $loop->index * 150 }}">
                    @include('freizeitlive.partials.card-speaker', ["classes" => "slide", "item" => $speaker])
                </div>
            </div>


        @endforeach

    </div>
    <div class="pt-5">
        <div class="swiper-scrollbar d-md-block d-none" style="height: 0.5rem"></div>
        <div class="swiper-pagination d-md-none d-block"></div>
    </div>

</div>


<script>
    new Swiper('#speakers-carousel', {
        slidesPerView: 4,
        spaceBetween: 30,
        scrollbar: {
            el: '.swiper-scrollbar',
            draggable: true,
        },
        pagination: {
            el: '.swiper-pagination',
            clickable: true,
        },
        breakpoints: {

            0: {
                slidesPerView: 1,
            },

            768: {
                slidesPerView: 2,
                scrollbar: {
                    el: '.swiper-scrollbar',
                    draggable: true,
                }
            },
            992: {
                slidesPerView: 4,
            },

        }
    });
</script>



