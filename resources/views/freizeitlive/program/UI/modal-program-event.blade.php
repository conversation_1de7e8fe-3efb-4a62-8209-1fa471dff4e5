<div class="modal modal-xl fade" id="{{ $mID }}" data-bs-backdrop="static"
     tabindex="-1" aria-hidden="true">
    <div class="modal-dialog {{--modal-dialog-scrollable--}} modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-body position-relative">
                <div class="position-absolute top-0 end-0 p-3">
                    <button type="button" class="btn-close  " data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                @if($program instanceof \App\Models\Freizeitlive\FreizeitliveProgram)
                    <section class="mb-4">
                        <h2 class="display-6 my-4">
                            {{$program->title}}
                        </h2>

                        @if(!empty($program->imagesArray))
                                <div class="swiper program-slider d-block d-lg-none">
                                    <div class="swiper-wrapper">
                                        @foreach($program->imagesArray as $path)
                                            @if($path)
                                                <div class="swiper-slide text-center">
                                                    <img src="{{$path}}" class="d-block w-100" alt="">
                                                </div>
                                            @endif
                                        @endforeach
                                    </div>
                                    <div class="pt-5">
                                        <div class="swiper-pagination"></div>
                                    </div>
                                </div>
                        @endif

                        <div class="row">
                            <div class="col-12 {{ (!empty($program->imagesArray)) ? "col-lg-8" : "" }}">
                                <h5 class="mb-3">
                                    {{$program->description}}
                                </h5>
                                <p class="mb-0">
                                    {!! $program->text !!}
                                </p>
                            </div>
                            @if(!empty($program->imagesArray))
                                <div class="col-12 col-lg-4 d-none d-lg-block">
                                    <div class="swiper program-slider">
                                        <div class="swiper-wrapper">
                                            @foreach($program->imagesArray as $path)
                                                @if($path)
                                                    <div class="swiper-slide text-center">
                                                        <img src="{{$path}}" class="d-block w-100" alt="">
                                                    </div>
                                                @endif
                                            @endforeach
                                        </div>
                                        <div class="pt-5">
                                            <div class="swiper-pagination"></div>
                                        </div>
                                    </div>
                                </div>
                            @endif
                        </div>

                    </section>

                    @if($program->speakers()->count())
                        <div class="d-flex justify-content-lg-start flex-column flex-lg-row mb-3">
                            @foreach($program->speakers as $s)
                                <a class="mb-lg-0 @if(!$loop->last) mb-3 @endif me-3 borders border-dark d-flex program-speaker d-block text-danger text-decoration-none" target="_blank" href="{{url('speaker/' . $s->name_slug)}}">
                                    <div>
                                        <img src="{{$s->imageFullPath}}" alt="Pic: {{$s->name}}" width="70px">
                                    </div>
                                    <section class="ps-2 d-flex align-items-center">
                                        <h5>
                                            {{$s->name}}
                                        </h5>

                                        <small class="d-lg-block" style="max-width: 180px;">
                                            {{$s->function}}
                                        </small>
                                    </section>
                                </a>
                            @endforeach
                        </div>
                    @endif

                    @if($program->sponsors()->count())
                        {{--                        <h5 class="">Sponsor:Innen & Partner:Innen</h5>--}}
                        <div class="d-flex justify-content-between justify-content-lg-end flex-wrap mb-2 ">
                            @foreach($program->sponsors as $sponsor)
                                <a style="width: 5rem;height: 5rem" target="_blank"
                                   href="{{$sponsor->link}}"
                                   class="  d-flex justify-content-center border align-items-center   p-1 mb-2 me-lg-3 program-sponsor">
                                    <img src="{{$sponsor->logoFullPath}}" alt="Sponsor logo" class="img-fluid">
                                </a>
                            @endforeach
                        </div>
                    @endif

                    <aside class="border-top border-red pt-3 ">
                        <div class="row">
                            <div class="col-lg-8">
                                <div class="d-flex flex-lg-row flex-column text-red">
                                    <div class="mb-3 mb-lg-0">
                                        <div class="d-flex align-items-center fs-5 ">
                                            <i class="fa-duotone fa-location-dot fa-2x me-2 "></i>
                                            <span class="fw-bold ">
                                                {{$program->roomName}}
                                            </span>
                                        </div>
                                    </div>
                                    <div class="ms-0 ms-lg-5 mb-3 mb-lg-0">
                                        <div class="d-flex align-items-center fs-5  ">
                                            <i class="fa-duotone fa-watch fa-2x me-2 "></i>
                                            <span class="fw-bold ">
                                                {{$program->timeRangeString}}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-4">
                                <div class=" d-flex justify-content-start justify-content-lg-end">

                                    <div class="dropdown">
                                        <button class="btn btn-outline-red  fs-4" type="button"
                                                data-bs-toggle="dropdown" aria-expanded="false">
                                            Teilen <i class="fa-duotone fa-share-nodes"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            @foreach($program->getSharingLinks() as $social)
                                                <li class="border-bottom">
                                                    <a class="dropdown-item d-flex align-items-center"
                                                       href="{{$social['link']}}" target="_blank">
                                                        <i style="color: {{$social['color']}}"
                                                           class="fa-2x {{$social['icon']}}"></i>
                                                        <span class="ms-2">{{$social['text']}}</span>
                                                    </a>
                                                </li>
                                            @endforeach
                                            <li>
                                                <span class="dropdown-item d-flex align-items-center"
                                                      style="cursor: pointer"
                                                      onclick="copyTextToClipboard('{{url("programm/$program->id")}}')">
                                                    <i class="text-dark fa-2x fa-duotone fa-copy"></i>
                                                    <span class="ms-2">Link kopieren</span>
                                                </span>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </aside>
                @endif
            </div>
        </div>
    </div>
</div>
