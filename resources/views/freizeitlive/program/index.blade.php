@extends('freizeitlive.master')

@section('title')
    Programm | freizeitlive.kurier.at
@endsection

@section('content')
    <div class="container">
        @include('freizeitlive.partials.breadcrumbs', [
            "breadcrumbs" => [["url" => "#", "text" => "Programm"]]
        ])
    </div>

    @if(!empty($programs) && count($programs))
        @foreach($programs as $program)
            @include('freizeitlive.program.UI.modal-program-event', [
                "mID" => "program-modal-$program->id",
                "data" => $program
            ])
        @endforeach

        <section class="container ">
            <nav class="text-center">
                @foreach($rooms as $room)
                    <a class="ms-lg-3  btn btn-outline-red me-3 now text-nowrap d-block d-lg-inline-block mb-3" data-room-selector="{{ $room['id'] }}">
                        {{ $room['name'] }}
                    </a>
                @endforeach
                <div class="text-red d-lg-inline-block d-none">
                    |
                </div>

                <a class="ms-lg-3  btn btn-outline-red me-3 now text-nowrap d-block d-lg-inline-block mb-3" href="{{url('/#erlebnisstationen')}}">
                    Erlebnisstationen
                    <i class="fa-solid fa-arrow-up-right-from-square"></i>
                </a>
            </nav>

            <div class="row">
                <div class="col-12 pt-3">
                    <div class="alert alert-info">
                        Änderungen vorbehalten - Programm wird laufend aktualisiert!
                    </div>
                </div>
            </div>

            <div class="overflow-hidden">
                @foreach(config('freizeitlive.program.eventdates') as $ed)
                    @php($ed_c = \Carbon\Carbon::parse($ed))
                    @continue($programs->where("eventdate", $ed_c)->isEmpty())

                    <h3 class="text-muted">{{ $loop->index + 1 }}. Tag</h3>
                    <hr>

                    @foreach($programs->where("eventdate", $ed_c) as $program)
                        <section data-room="{{ $program->room_id }}" class="shadow mb-3 bg-white program-card d-flex py-3 data-room">
                            <aside class="program-card--left-panel border-end border-red border-2 px-3 d-none d-lg-block">
                                <div class="d-flex flex-column">
                                    @if($program->eventdate)
                                        <span class="d-block text-secondary text-nowrap">
                                            {{ $program->eventdate->locale('de')->dayName }}, {{ $program->eventdate->format("d.m.") }}
                                        </span>
                                    @endif
                                    <h5 class="text-nowrap">{{ $program->timeRangeString }}</h5>
                                    <p>
                                        <span class="badge text-bg-red text-white text-nowrap">{{ $program->roomName }}</span>
                                        <span class="d-block mt-2 text-nowrap">{{ $program->roomSub }}</span>
                                    </p>
                                </div>
                            </aside>

                            <article class="program-card--right-panel px-3 w-100">
                                <aside class="d-lg-none">
                                    @if($program->eventdate)
                                        <span class="d-block text-secondary text-nowrap">
                                            {{ $program->eventdate->locale('de')->dayName }}, {{ $program->eventdate->format("d.m.") }}
                                        </span>
                                    @endif
                                    <p class="h3 text-nowrap">{{ $program->timeRangeString }}</p>
                                    <p>
                                        <span class="badge text-bg-red text-white text-nowrap">{{ $program->roomName }}</span>
                                        <span class="d-block mt-2 text-nowrap">{{ $program->roomSub }}</span>
                                    </p>
                                </aside>

                                @if($program->image_1 || $program->image_2)
                                    <div class="row">
                                        <div class="col-lg-8 col-xl-9">
                                            <h3 class="mb-3">{{ $program->title }}</h3>
                                            <p>{{ $program->description }}</p>
                                            @if($program->speakers->count())
                                                <div class="d-flex justify-content-lg-start flex-column flex-lg-row pt-3">
                                                    @foreach($program->speakers as $s)
                                                        <a href="{{ url('speaker/'.$s->id.'/') }}"
                                                           target="_blank"
                                                           class="mb-lg-0 @if(!$loop->last) mb-3 @endif me-3 borders border-dark d-flex program-speaker text-danger text-decoration-none">
                                                            <img src="{{ $s->imageFullPath }}" alt="Pic: {{ $s->name }}"
                                                                 width="70px">
                                                            <section class="ps-2 d-flex align-items-center">
                                                                <div>
                                                                    <h5>{{ $s->name }}</h5>
                                                                    <small class="d-lg-block" style="max-width: 180px;">
                                                                        {{ $s->function }}
                                                                    </small>
                                                                </div>
                                                            </section>
                                                        </a>
                                                    @endforeach
                                                </div>
                                            @endif
                                            <div class="text-start my-4">
                                                <hr>
                                                <div class="btn btn-outline-red fw-bold"
                                                     data-program-id="{{ $program->id }}"
                                                     data-bs-toggle="modal"
                                                     data-bs-target="#program-modal-{{ $program->id }}">
                                                    Weitere Infos <i class="fa-duotone fa-circle-info"></i>
                                                </div>
                                            </div>
                                            <div class="swiper program-slider d-block d-lg-none">
                                                <div class="swiper-wrapper  ">
                                                    @foreach($program->imagesArray as $path)
                                                        @if($path)
                                                            <div class="swiper-slide text-center">
                                                                <img src="{{$path}}" class="d-block w-100" alt="">
                                                            </div>
                                                        @endif
                                                    @endforeach
                                                </div>
                                                <div class="pt-5">
                                                    <div class="swiper-pagination"></div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-lg-4 col-xl-3 d-none d-lg-block">
                                            <div class="swiper program-slider">
                                                <div class="swiper-wrapper  ">
                                                    @foreach($program->imagesArray as $path)
                                                        @if($path)
                                                            <div class="swiper-slide text-center">
                                                                <img src="{{$path}}" class="d-block w-100" alt="">
                                                            </div>
                                                        @endif
                                                    @endforeach
                                                </div>
                                                <div class="pt-5">
                                                    <div class="swiper-pagination"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @else
                                    <h3 class="mb-3">{{ $program->title }}</h3>
                                    <p>{{ $program->description }}</p>
                                    @if($program->speakers->count())
                                        <div class="d-flex justify-content-lg-start flex-column flex-lg-row pt-3">
                                            @foreach($program->speakers as $s)
                                                <a href="{{ url('speaker/'.$s->id.'/') }}"
                                                   target="_blank"
                                                   class="mb-lg-0 @if(!$loop->last) mb-3 @endif me-3 borders border-dark d-flex program-speaker text-danger text-decoration-none">
                                                    <img src="{{ $s->imageFullPath }}" alt="Pic: {{ $s->name }}"
                                                         width="70px">
                                                    <section class="ps-2 d-flex align-items-center">
                                                        <h5>{{ $s->name }}</h5>
                                                        <small class="d-lg-block" style="max-width: 180px;">
                                                            {{ $s->function }}
                                                        </small>
                                                    </section>
                                                </a>
                                            @endforeach
                                        </div>
                                    @endif
                                    <div class="text-start my-4">
                                        <hr>
                                        <div class="btn btn-outline-red fw-bold"
                                             data-program-id="{{ $program->id }}"
                                             data-bs-toggle="modal"
                                             data-bs-target="#program-modal-{{ $program->id }}">
                                            Weitere Infos <i class="fa-duotone fa-circle-info"></i>
                                        </div>
                                    </div>
                                @endif
                            </article>
                        </section>
                    @endforeach
                    <br><br>
                @endforeach
            </div>
        </section>

        <script>
            const roomSelectors = document.querySelectorAll('[data-room-selector]');
            const dataRoom = document.querySelectorAll('[data-room]');
            const sliders = [...document.querySelectorAll('.program-slider')];


            sliders.forEach(slider => {
                new Swiper(slider, {
                    loop : true,
                    slidesPerView : 1,
                    autoplay : {
                        delay : 6000,
                        disableOnInteraction : false,
                    },
                    pagination : {
                        el : '.swiper-pagination',
                        clickable : true,
                    },
                });
            })

            function filterProgram(key){
                const isAll = !key;

                dataRoom.forEach(el => {
                    el.classList.remove('data-room--slide');
                    el.classList.add('d-none');
                });

                setTimeout(() => {
                    dataRoom.forEach(el => {
                        if(isAll || el.dataset.room === key){
                            el.classList.remove('d-none');
                            el.classList.add('data-room--slide');
                        }
                    });
                }, 0);
            }

            let roomHasBeenSelected = false;

            roomSelectors.forEach(button => {
                button.onclick = () => {
                    roomSelectors.forEach(btn => {
                        btn.classList.toggle('btn-red', btn === button);
                        btn.classList.toggle('btn-outline-red', btn !== button);
                    });
                    filterProgram(button.dataset.roomSelector);
                };

                if(!roomHasBeenSelected){
                    button.click();
                    roomHasBeenSelected = true;
                }

            });

            @if($selectedProgram instanceof \App\Models\Freizeitlive\FreizeitliveProgram)
            const selectedProgram = `{{ $selectedProgram->id }}`;
            const trigger = document.querySelector(`[data-program-id="${selectedProgram}"]`);
            if(trigger) trigger.click();

            document.body.addEventListener('hidden.bs.modal', () => {
                const updatedUrl = new URL(window.location.href.replace(`programm/${selectedProgram}`, 'programm'));
                history.pushState({}, '', updatedUrl);
            });
            @endif

            function copyTextToClipboard(text){
                if(navigator.clipboard && navigator.clipboard.writeText){
                    // Use modern Clipboard API
                    navigator.clipboard.writeText(text)
                             .then(() => {
                                 console.log('Text copied successfully using Clipboard API');
                             })
                             .catch(err => {
                                 console.error('Clipboard API failed ', err);

                             });
                }
            }


        </script>
    @else
        <div class="container pt-5">
            <div class="row">
                <div class="col-12 mt-5">
                    <div class="alert alert-info">Derzeit kein Programm verfügbar</div>
                </div>
            </div>
        </div>
    @endif
@endsection


