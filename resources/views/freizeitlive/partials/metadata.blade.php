

@php

    use App\Http\Controllers\Freizeitlive\FreizeitMetadata;
    $metadata = (isset($metadata) && $metadata instanceof FreizeitMetadata) ? $metadata : new FreizeitMetadata();
@endphp

    <!-- Open Graph -->
<meta property="og:url" content="{{ url()->current() }}"/>
<meta property="og:type" content="{{ FreizeitMetadata::TYPE }}"/>
<meta property="og:title" content="{{ $metadata->title }}"/>
<meta property="og:description" content="{{ $metadata->description }}"/>
<meta property="og:image" content="{{ $metadata->image }}"/>
<meta property="og:image:type" content="{{ $metadata->imageType }}"/>

<meta property="og:site_name" content="{{ FreizeitMetadata::SITE_NAME }}"/>
<meta property="og:locale" content="{{ FreizeitMetadata::LOCALE }}"/>

<!-- Twitter Card -->
<meta name="twitter:card" content="{{ FreizeitMetadata::TWITTER_CARD }}"/>
<meta name="twitter:title" content="{{ $metadata->title }}"/>
<meta name="twitter:description" content="{{ $metadata->description }}"/>
<meta name="twitter:image" content="{{ $metadata->image }}"/>

<link rel="canonical" href="{{ url()->current() }}"/>
