
<div id="highlight-article-carousel" class="swiper">
    <div class="swiper-wrapper">
        @for($x = 0 ; $x < count($src); $x++)
            <div class="swiper-slide">
                <div>
                    @include('freizeitlive.partials.image', [
                        "src" => $src[$x],
                        'alt' => $alt[$x],
                        "cr"  => $cr[$x],
                        'color' => !empty($color) ? $color : null
                    ])
                </div>
            </div>
        @endfor
    </div>

    <div class="pt-5">
        <div class="swiper-pagination"></div>
    </div>


</div>


<script>
    new Swiper('#highlight-article-carousel', {
        loop: true,
        slidesPerView: 1,
        autoplay: {
            delay: 4000,
            disableOnInteraction: false,
        },
        pagination: {
            el: '.swiper-pagination',
            clickable: true,
        },
    });
</script>


