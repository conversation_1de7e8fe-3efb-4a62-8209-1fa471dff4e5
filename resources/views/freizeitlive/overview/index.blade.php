@php
use App\Models\Freizeitlive\FreizeitlivePost;
use App\Models\Freizeitlive\FreizeitliveSpeaker;
use App\Models\Freizeitlive\FreizeitliveNews;

@endphp

@extends('freizeitlive.master')

@section('title')
    {{$title}}
@endsection

@section('content')



        <div class="container  " id="overview_page">

            @include('freizeitlive.partials.breadcrumbs')

            <header class="mb-5 py-3 text-center">
                <h2 class="h1">{{$title}}</h2>
            </header>


            <section class="row row-cols-xxl-5 row-cols-xl-4 row-cols-lg-3 row-cols-sm-2 row-cols-1 ">
                @foreach($items as $item)
                    <div class="col mb-5">

                        @if($item instanceof FreizeitlivePost)
                            @include('freizeitlive.partials.card-station', ["item" => $item, "classes" => "hoverable-card"])
                        @elseif($item instanceof FreizeitliveSpeaker)
                            @include('freizeitlive.partials.card-speaker', ["item" => $item, "classes" => "hoverable-card"])
                        @elseif($item instanceof FreizeitliveNews)
                            @include('freizeitlive.partials.card-highlight', ["item" => $item, "classes" => "hoverable-card"])
                        @endif

                    </div>
                @endforeach
            </section>



            @if(isset($title_2) && isset($items_2))

                <header class="mb-5 py-3 text-center">
                    <h2 class="h1">{{$title_2}}</h2>
                </header>


                <section class="row row-cols-xxl-5 row-cols-xl-4 row-cols-lg-3 row-cols-sm-2 row-cols-1 ">
                    @foreach($items_2 as $item)
                        <div class="col mb-5">

                            @if($item instanceof FreizeitlivePost)
                                @include('freizeitlive.partials.card-station', ["item" => $item, "classes" => "hoverable-card"])
                            @elseif($item instanceof FreizeitliveSpeaker)
                                @include('freizeitlive.partials.card-speaker', ["item" => $item, "classes" => "hoverable-card"])
                            @elseif($item instanceof FreizeitliveNews)
                                @include('freizeitlive.partials.card-highlight', ["item" => $item, "classes" => "hoverable-card"])
                            @endif

                        </div>
                    @endforeach
                </section>


            @endif

        </div>



@endsection
