@extends('freizeitlive.master')

@section('title')
    {{$station->titleText}}
@endsection

@section('content')

    <div class="container mb-4">

        @include('freizeitlive.partials.breadcrumbs', ["breadcrumbs" => $breadcrumbs])

        <header class="mb-3 py-3">
            <h1 itemprop="headline mb-3">{{$station->titleText}}</h1>
            <h2 class="mb-4 ">
                {{$station->desc_short}}
            </h2>
        </header>

        <article itemprop="articleBody" class="row">
            <div class="col-md-4">

                @include('freizeitlive.partials.images-slider', [
                    "src" => [$station->imageOneFullPath, $station->imageTwoFullPath],
                    'alt' => [$station->image_one_alt,$station->image_two_alt],
                    "cr"  => [$station->image_one_cr, $station->image_two_cr],
                    'color' =>$station->postType ? $station->postType->color : null
                ])



            </div>
            <div class="col-md-8">

                <p class="pt-md-0 pt-3">
                    {!! $station->desc_long !!}
                </p>
            </div>


        </article>


    </div>

@endsection
