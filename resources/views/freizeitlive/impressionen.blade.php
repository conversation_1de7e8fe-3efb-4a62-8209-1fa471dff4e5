@extends('freizeitlive.master')

@section('title')
    Impressionen | freizeitlive.kurier.at
@endsection

@section('content')
    <div class="container section-margin">

        @include('freizeitlive.partials.breadcrumbs',["breadcrumbs" => [
             [
             "url" => "#",
             "text" => "Impressionen",
             ]
     ]])


        <div class="row">
            <div class="col-12 mb-5">
                <div class="h1">
                    <span class="custom-title__counter custom-title__counter--empty pos-relative"></span>
                    Impressionen
                </div>
            </div>
            <div class="col-12 mb-5">
                <h3 class="mb-3">Freizeitlive 2024 - Video</h3>
                <script type="didomi/html"
                        data-vendor="c:vimeo-dzaWb9AU"
                        data-alert="true"
                        data-alert-color="success"
                        data-alert-classes="text-center fs-5"
                >
                <div class="ratio" style="--bs-aspect-ratio: 50%;">
                    <iframe src="https://player.vimeo.com/video/1060370997" allowfullscreen title="MediaPrint"></iframe>
                </div>
                </script>
            </div>

            @if(!empty($images2024))
                <div class="col-12 my-5">
                    <h3>Freizeitlive 2024 - Galerie</h3>
                    <div id="main-slider" class="splide my-4" role="group">
                        <div class="splide__track">
                            <ul class="splide__list">
                                @foreach($images2024 as $image)
                                    <li class="splide__slide">
                                        <img src="{{ url("storage/app/public/freizeitlive/impressionen/2024/".$image) }}" alt="Impressionen 2024 {{$loop->iteration}}/{{count($images2024)}}" class="d-block w-100" />
                                    </li>
                                @endforeach
                            </ul>
                        </div>
                    </div>

                    <div id="thumbnail-slider" class="splide" role="group">
                        <div class="splide__track">
                            <ul class="splide__list">
                                @foreach($images2024 as $image)
                                    <li class="splide__slide">
                                        <img src="{{ url("storage/app/public/freizeitlive/impressionen/2024/".$image) }}" alt="Impressionen 2024 {{$loop->iteration}}/{{count($images2024)}}" />
                                    </li>
                                @endforeach
                            </ul>
                        </div>
                    </div>

                </div>
            @endif
        </div>
    </div>

    <script>
        document.addEventListener("DOMContentLoaded", function () {
            var main = new Splide('#main-slider', {
                rewind : true,
                type : 'fade',
                pagination : false,
                arrows : true,
                cover : true,
                padding : {
                    left : 0,
                    right : 0
                },
                lazyLoad : 'nearby',
            });

            var thumbnails = new Splide('#thumbnail-slider', {
                rewind : true,
                fixedWidth : 90,
                fixedHeight : 60,
                isNavigation : true,
                gap : 5,
                focus : 'left',
                pagination : false,
                cover : true,
                lazyLoad : 'nearby',
                dragMinThreshold : {
                    mouse : 4,
                    touch : 10,
                }
            });

            main.sync(thumbnails);
            main.mount();
            thumbnails.mount();
        })



    </script>
@endsection
