@extends('freizeitvoting.master')

@section('title')
    Gasthaus nominieren
@endsection

@section('content')
    <div class="row small ">
        <div class="col-12">
            <div class="px-3 px-lg-0">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url("/") }}"><i class="fas fa-home-alt"></i></a></li>
                    <li class="breadcrumb-item active" aria-current="page">@yield('title')</li>
                </ol>
            </nav>
        </div>
        </div>
    </div>
    <div class="row">
        @if(Session::has('message'))
            <div class="col-12">
                <div class="alert alert-success">
                    <h3 class="alert-heading mb-0">Vielen Dank fürs Mitmachen!</h3>
                    Deine Nominierung wurde erfolgreich gespeichert!
                </div>
                <a href="{{ url("gasthaus-nominieren") }}" class="small">
                    <PERSON><PERSON><PERSON> hier wenn du ein weiteres Gasthaus nominieren möchtest
                </a>
            </div>
        @else

            <div class="col-12 mb-5">
                @include("freizeitvoting.partials.nominierung", ["button" => false])
            </div>
            <div class="col-12 col-xl-8">
                <form action="{{ url("gasthaus-nominieren") }}" id="gasthausNominierenForm" name="gasthausNominierenForm" method="post">
                    {{ csrf_field() }}
                    <div class="row">
                        <div class="col-12">
                            @include("messages.error")
                        </div>

                        @if(count($gasthaeuser))
                            <div class="col-12 mb-4">
                                <div class="alert alert-secondary">
                                    <div class="font-ptsans">Bereits nominierte Gasthäuser</div>
                                    <select class="select2" name="freizeit_gasthaus_id" id="freizeit_gasthaus_id">
                                        <option value=""></option>
                                        @foreach($gasthaeuser as $bundesland => $gasthaus)
                                            <optgroup label="{{ $bundesland }}">{{ $bundesland }}</optgroup>
                                            @foreach($gasthaus as $tmp)
                                                <option value="{{ $tmp["identifier_id"] }}" {{ (old("freizeit_gasthaus_id") == $tmp["identifier_id"]) ? "selected" : "" }}>{{ $tmp["title"] }}
                                                    ({{ $tmp["plz"] }} {{ $tmp["ort"] }}, {{ $tmp["bundesland"] }})
                                                </option>
                                            @endforeach
                                        @endforeach
                                    </select>
                                    <small>Sollte sich dein Lieblingsgasthaus in unserer Liste befinden, ist eine
                                        Nachnominierung nicht notwendig.</small>
                                </div>
                            </div>
                        @endif

                        <div class="col-12 mb-4">
                            <label for="title" class="form-label text-black font-ptsans">Name des Gasthauses</label>
                            <input type="text" name="title" id="title" class="form-control" value="{{ old("title") }}">
                        </div>
                        <div class="col-12 mb-4">
                            <label for="plz" class="form-label text-black font-ptsans">PLZ des Gasthauses</label>
                            <input type="text" name="plz" id="plz" class="form-control" value="{{ old("plz") }}">
                        </div>
                        <div class="col-12 mb-4">
                            <label for="ort" class="form-label text-black font-ptsans">Ort des Gasthauses</label>
                            <input type="text" name="ort" id="ort" class="form-control" value="{{ old("ort") }}">
                        </div>

                        <div class="col-12 mb-5">
                            <button goodcaptcha="true" class="btn btn-dark btn-block btn-send">Nominierung absenden</button>
                        </div>
                    </div>
                </form>
            </div>
        @endif
    </div>
@endsection
