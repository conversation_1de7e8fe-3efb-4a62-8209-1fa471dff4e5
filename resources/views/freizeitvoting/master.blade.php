<!DOCTYPE html>
<html lang="de">
<head>
    <title>@yield('title') | voting.freizeit.at</title>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="_token" content="{!! csrf_token() !!}"/>
    <meta http-equiv="Cache-control" content="no-cache">

    <meta name="theme-color" content="#11100B"/>
    <link rel="shortcut icon" href="{{ url("img/freizeitvoting/favicon.ico?v1") }}"/>

    {{-- FACEBOOK SHARE --}}
    <meta property="og:url" content="https://voting.freizeit.at"/>
    <meta property="og:type" content="article"/>
    <meta property="og:title" content="Freizeit Voting 2025"/>
    <meta property="og:description" content="<PERSON><PERSON><PERSON><PERSON> Sie Ihre Lieblings-Gasthaus"/>
    <meta property="og:image" content="{{ url("img/freizeitvoting/logo-freizeit.svg") }}"/>

    <!-- CSS-START -->
    <link rel="stylesheet" href="{{ url("inc/fontawesome-pro-6.5.2-min/dist/all.min.css") }}">
    <script src="{{ url("inc/fontawesome-pro-6.5.2-min/dist/all.min.js") }}"  type="text/javascript"></script>

    <link rel="stylesheet" href="{{url('inc/bootstrap-5.2.2/dist/css/bootstrap.min.css')}}">
    <script src="{{url('inc/bootstrap-5.2.2/dist/js/bootstrap.bundle.min.js')}}"></script>
    <link rel="stylesheet" type="text/css" href='{{ url("inc/select2-4.0.12/dist/css/select2.min.css") }}'/>
    <link rel="stylesheet" type="text/css" href='{{ url("/inc/css/freizeitvoting_fonts.css") }}?v1'/>
    <link rel="stylesheet" type="text/css" href='{{ url("/inc/css/freizeitvoting.css") }}?v3'/>

    <!-- JS-START -->
    <script src="{{url('inc/jquery/jquery.js')}}"></script>
    <script type="text/javascript" src='{{ url("inc/select2-4.0.12/dist/js/select2.min.js") }}'></script>
    <script src="{{ asset('https://w19.captcha.at/sdk/sdk.js?v='.now()->format('YmdH'))}}" type="text/javascript"></script>
    <script src="{{url('inc/js/goodcaptcha.js')}}" type="text/javascript" ></script>

    @include('freizeitvoting.didomi')

</head>

<body>

<div class="container-fluid">

    <div class="row my-4">
        <div class="col-12 text-center">
            <a href="{{ (empty(request()->segment(1))) ? "https://freizeit.at/" : url("/") }}">
                <img src="{{ url("img/freizeitvoting/freizeit-logo.png") }}?v2" class="header-logo"/><br>
            </a>
        </div>
    </div>

    @yield('content')

    <div class="row footer-wrapper">
        <div class="col-12">
            <div >
                <nav class="nav justify-content-center align-items-center my-3">
                    <a class="nav-link pr-1 pr-md-3" href="https://freizeit.at/info/impressum-freizeitat/401785646" target="_blank">Impressum</a>
                    /
                    <a class="nav-link px-1 px-md-3" href="https://freizeit.at/info/datenschutzrichtlinie/401785943" target="_blank">Datenschutzrichtlinie</a>
                    /
                    <a class="nav-link px-1 px-md-3" href="https://freizeit.at/info/netiquette/401785946" target="_blank">Netiquette</a>
                    /
                    <a class="nav-link px-1 px-md-3" href="https://freizeit.at/info/anb/401785649" target="_blank">ANB</a>
                    /
                    <a class="nav-link px-1 px-md-3" href="{{ url("cookiepolicy") }}" target="_blank">Cookie
                                                                                                      Richtlinien</a>
                    /
                    <a class="nav-link pl-1 pl-md-3" href="javascript:Didomi.preferences.show()">Cookie Einstellungen</a>
                </nav>
            </div>
        </div>
    </div>
</div>

<script>
    var baseurl = "{{url('/')}}";
    var mobile = false;

    $(function () {
        if ($("#is-xs").css("display") != "none") {
            mobile = true;
        }

        @if(Session::has('message'))
        $(window).on('load', function () {
            $('html, body').animate({scrollTop: ($(".alert-success").offset().top - 30)});
        });
        @endif
        @if (count($errors) > 0)
        $(window).on('load', function () {
            $('html, body').animate({scrollTop: ($(".alert-danger").offset().top - 30)});
        });
        @endif
    });
</script>

<!-- ALLGEMEINES JS -->

<script type="text/javascript" src='{{ url("inc/js/freizeitvoting.js") }}?v1'></script>

@yield("viewscripts")

</body>
</html>


