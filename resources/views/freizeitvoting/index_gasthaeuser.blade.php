@extends('freizeitvoting.master')

@section('title')
    Beste Gasthäuser
@endsection

@section('content')
    <div class="row small">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{{ url("/") }}"><i class="fas fa-home-alt"></i></a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">@yield('title')</li>
                </ol>
            </nav>
        </div>
    </div>
    <div class="row">
        @if(Session::has('message'))
            <div class="col-12 mb-5">
                <h1 class="alert-heading">Vielen Dank fürs Mitmachen!</h1>
                Das Voting läuft noch bis 30. September 2025. Das Gesamtergebnis wird im November 2025 veröffentlicht.<br>
                <br>
                <div class="alert alert-success">
                    <h3 class="alert-heading">Als Dankeschön für dein Voting</h3>
                    Erhältst du 4 Wochen KURIER Digital-Abo Premium gratis (Der Bezug endet nach 4 Wochen automatisch)*<br>
                    <a href="https://angebote.kurier.at/gasthausvoting?utm_source=freizeit&utm_medium=referral&utm_campaign=Gasthausvoting&utm_content=abowerbung" class="btn btn-success mt-3">Zum 4 Wochen KURIER Digital-Abo Premium Test</a>
                    <br>
                    <br>
                    <small>* Du wirst auf angebote.kurier.at weitergeleitet.</small>
                </div>
                <a href="{{ url("gasthaeuser") }}" class="small">
                    Klicke hier wenn du ein weiteres Gasthaus bewerten möchtest
                </a>
            </div>
        @else
            <div class="col-12 mb-5">
                <div class="card h-100 bg-beige rounded-0">
                    <div class="d-lg-none">
                        <img src="{{ url("img/freizeitvoting/wirtshaus.JPG") }}" class="d-block w-100">
                    </div>
                    <div class="card-body p-0">
                        <div class="row">
                            <div class="d-none d-lg-block col-lg-6">
                                <div class="bg-img-wirtshaus"></div>
                            </div>
                            <div class="col-12 col-lg-6 py-4 py-lg-5 px-5">
                                <h1 class="card-title">So wird gevotet</h1>
                                <p class="card-text">
                                    Stöbere in unserer Kartei, die nach Bundesländern geordnet ist und wähle jene Gasthäuser, die du kennst.
                                    Vergib deine Punkte in den vorgegebenen Kategorien Essen, Service, Getränke und Ambiente.
                                    Insgesamt können pro Gasthaus 100 mögliche Punkte auf die vier Kategorien aufgeteilt werden.
                                    Du kannst pro Tag ein Voting abgeben, jedoch in Summe so viele Gasthäuser bewerten, wie du möchtest.
                                    <br>
                                    <br>
                                    Das Voting läuft bis zum 30. September 2025.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 col-md-8">
                <form action="{{ url("gasthaeuser") }}" id="gasthaeuserForm" name="gasthaeuserForm" method="post">
                    {{ csrf_field() }}
                    <div class="row">
                        <div class="col-12">
                            @include("messages.error")
                        </div>

                        <div class="col-12">
                            <div class="font-ptsans">Wählen Sie ein Gasthaus</div>
                            <select class="select2 w-100" name="freizeit_gasthaus_id" id="freizeit_gasthaus_id">
                                <option value=""></option>
                                @foreach($gasthaeuser as $bundesland => $gasthaus)
                                    <optgroup label="{{ $bundesland }}">{{ $bundesland }}</optgroup>
                                    @foreach($gasthaus as $tmp)
                                        <option value="{{ $tmp["identifier_id"] }}" {{ (old("freizeit_gasthaus_id") == $tmp["identifier_id"]) ? "selected" : "" }}>{{ $tmp["title"] }}
                                            ({{ $tmp["plz"] }} {{ $tmp["ort"] }}, {{ $tmp["bundesland"] }})
                                        </option>
                                    @endforeach
                                @endforeach
                            </select>
                        </div>

                        <div class="col-12 mt-4 mb-3 font-ptsans">
                            Bewerten Sie das ausgewählte Gasthaus nach folgenden Kriterien:
                        </div>
                        <div class="col-12 mb-4">
                            <label for="kategorie1" class="form-label">Essen
                                <small>(0 - 50)</small>
                            </label>
                            <div class="range">
                                <div class="thumb-value color-tangerine fw-bold">{{ old("kategorie1", 0) }}</div>
                                <input type="range" class="form-range" value="{{ old("kategorie1", 0) }}" min="0" max="50" step="1" id="kategorie1" name="kategorie1" />
                            </div>
                        </div>
                        <div class="col-12 mb-4">
                            <label for="kategorie2" class="form-label">Service
                                <small>(0 - 10)</small>
                            </label>
                            <div class="range">
                                <div class="thumb-value color-tangerine fw-bold">{{ old("kategorie2", 0) }}</div>
                                <input type="range" class="form-range" value="{{ old("kategorie2", 0) }}" min="0" max="10" step="1" id="kategorie2" name="kategorie2" />
                            </div>
                        </div>
                        <div class="col-12 mb-4">
                            <label for="kategorie3" class="form-label">Getränke
                                <small>(0 - 15)</small>
                            </label>
                            <div class="range">
                                <div class="thumb-value color-tangerine fw-bold">{{ old("kategorie3", 0) }}</div>
                                <input type="range" class="form-range" value="{{ old("kategorie3", 0) }}" min="0" max="15" step="1" id="kategorie3" name="kategorie3" />
                            </div>
                        </div>
                        <div class="col-12 mb-4">
                            <label for="kategorie4" class="form-label">Ambiente
                                <small>(0 - 25)</small>
                            </label>
                            <div class="range">
                                <div class="thumb-value color-tangerine fw-bold">{{ old("kategorie4", 0) }}</div>
                                <input type="range" class="form-range" value="{{ old("kategorie4", 0) }}" min="0" max="25" step="1" id="kategorie4" name="kategorie4" />
                            </div>
                        </div>

                        <div class="col-12 my-5">
                            <button goodcaptcha="true" class="btn btn-dark btn-block btn-send">Bewertung absenden</button>
                        </div>
                    </div>
                </form>
            </div>
        @endif
    </div>
@endsection

