@extends('freizeitvoting.master')

@section('title')
    Beste Gerichte
@endsection

@section('content')
    <div class="row small">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{{ url("/") }}"><i class="fas fa-home-alt"></i></a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">@yield('title')</li>
                </ol>
            </nav>
        </div>
    </div>

    @if(Session::has('message'))
        <div class="row">
            <div class="col-12">
                <h3 class="alert-heading mb-0">Vielen Dank fürs Mitmachen!</h3>
                Dein Voting wurde erfolgreich gespeichert!<br>
                <br>
                <div class="alert alert-success">
                    <h3 class="alert-heading">Als Dankeschön für dein Voting</h3>
                    30 Tage freizeit-Plus zum Kennenlernen (endet automatisch)*<br>
                    <a href="https://freizeit.at/aufgetischt?utm_source=voting&utm_medium=referral&utm_campaign=aufgetischt&utm_content=abowerbung" class="btn btn-success mt-3">Zum freizeit-Plus Test</a>
                    <br>
                    <br>
                    <small>* Du wirst auf freizeit.at weitergeleitet.</small>
                </div>
            </div>
        </div>
    @else
        <div class="row">
            <div class="col-12 mb-5 px-lg-3">
                <div class="card h-100 bg-blue rounded-0">
                    <div class="d-lg-none">
                        <img src="{{ url("img/freizeitvoting/gerichte.JPG?v1") }}" class="d-block w-100">
                    </div>
                    <div class="card-body py-0 px-2">
                        <div class="row">
                            <div class="d-none d-lg-block col-lg-6 bg-img-gerichte">
                                <div class="bg-blue"></div>
                            </div>
                            <div class="col-12 col-lg-6 py-4 py-lg-5 px-5">
                                <h1 class="card-title">Beste Gerichte:<br class="d-none d-lg-block"> So stimmst du ab</h1>
                                <p class="card-text">
                                    Wähle das Gericht im Fenster „Kategorie“ und suche dann aus der Liste jenes Gasthaus, von dem du meinst,
                                    es gibt genau an dieser Adresse das beste Exemplar davon auf dem Teller. Die Redaktion hat hier eine Vorauswahl
                                    von Lokalen getroffen, es ist pro Person und Tag ein Voting möglich. Mehrfachnennungen sind nicht möglich.
                                    Du kannst gerne alle Kategorien bewerten.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <form action="{{ url("beste-gerichte") }}" id="besteGerichteForm" name="besteGerichteForm" class="w-100" method="post">
            {{ csrf_field() }}
            <div class="row">
                <div class="col-12 mb-3">
                    <h1>Wo gibt es ...</h1>
                </div>

                <div class="col-12">
                    @include("messages.error")
                </div>

                <div class="col-12 col-md-6 col-xl-4 mb-5">
                    <div class="card h-100">
                        <img src="{{ url("img/freizeitvoting/gerichte/gericht_apfelstudel.jpg?v1") }}" class="d-block w-100" />
                        <div class="card-body">
                            <div class="font-ptsans mb-2">... den besten Apfelstrudel?</div>
                            <select class="select2" name="kategorie1" id="kategorie1">
                                <option value=""></option>
                                @foreach($kategorien[1] as $key => $value)
                                    <option value="{{ $value["id"] }}" {{ (old("kategorie1") == $value["id"]) ? "selected" : "" }}>{{ $value["title"] }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                </div>

                <div class="col-12 col-md-6 col-xl-4 mb-5">
                    <div class="card h-100">
                        <img src="{{ url("img/freizeitvoting/gerichte/gericht_schnitzel.jpg?v1") }}" class="d-block w-100" />
                        <div class="card-body">
                            <div class="font-ptsans mb-2">... das knusprigste Schnitzel?</div>
                            <select class="select2" name="kategorie2" id="kategorie2">
                                <option value=""></option>
                                @foreach($kategorien[2] as $key => $value)
                                    <option value="{{ $value["id"] }}" {{ (old("kategorie2") == $value["id"]) ? "selected" : "" }}>{{ $value["title"] }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                </div>

                <div class="col-12 col-md-6 col-xl-4 mb-5">
                    <div class="card h-100">
                        <img src="{{ url("img/freizeitvoting/gerichte/gericht_cordon.jpg?v1") }}" class="d-block w-100" />
                        <div class="card-body">
                            <div class="font-ptsans mb-2">... das saftigste Cordon Bleu?</div>
                            <select class="select2" name="kategorie3" id="kategorie3">
                                <option value=""></option>
                                @foreach($kategorien[3] as $key => $value)
                                    <option value="{{ $value["id"] }}" {{ (old("kategorie3") == $value["id"]) ? "selected" : "" }}>{{ $value["title"] }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                </div>

                <div class="col-12 col-md-6 col-xl-4 mb-5">
                    <div class="card h-100">
                        <img src="{{ url("img/freizeitvoting/gerichte/gericht_zwiebelrostbraten.jpg?v1") }}" class="d-block w-100" />
                        <div class="card-body">
                            <div class="font-ptsans mb-2">... den zartesten Zwiebelrostbraten?</div>
                            <select class="select2" name="kategorie4" id="kategorie4">
                                <option value=""></option>
                                @foreach($kategorien[4] as $key => $value)
                                    <option value="{{ $value["id"] }}" {{ (old("kategorie4") == $value["id"]) ? "selected" : "" }}>{{ $value["title"] }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                </div>

                <div class="col-12 col-md-6 col-xl-4 mb-5">
                    <div class="card h-100">
                        <img src="{{ url("img/freizeitvoting/gerichte/gericht_gulasch.jpg?v1") }}" class="d-block w-100" />
                        <div class="card-body">
                            <div class="font-ptsans mb-2">... das gschmackigste Gulasch?</div>
                            <select class="select2" name="kategorie5" id="kategorie5">
                                <option value=""></option>
                                @foreach($kategorien[5] as $key => $value)
                                    <option value="{{ $value["id"] }}" {{ (old("kategorie5") == $value["id"]) ? "selected" : "" }}>{{ $value["title"] }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                </div>

                <div class="col-12 mb-5">
                    <button goodcaptcha="true" class="btn btn-dark btn-block btn-send">Bewertung absenden</button>
                </div>
            </div>
        </form>
    @endif
@endsection
