@extends('freizeitvoting.master')

@section('title')
    Voting
@endsection

@section('content')
    <div class="row row-cols-1 mt-4">
        @if($beendet)
            <div class="col">
                <div class="bg-beige p-4">
                    <h1 class="alert-heading">Das Voting ist beendet!</h1>
                    Im Anschluss folgt eine Abstimmung durch ein Expertengremium der Redaktion.<br>
                    Die Gesamt-Ergebnisse werden im Anschluss ausgewertet und im November 2025 präsentiert.
                </div>
            </div>
        @else
            @if($testing && in_array(request()->ip(), config("freizeitvoting.ips")))
                <div class="col mb-3">
                    @include("freizeitvoting.partials.nominierung", ["button" => true])
                </div>
                <div class="col mb-5">
                    @include("freizeitvoting.partials.wirtshaus")
                </div>
            @else
                @if($nominierungActive)
                    <div class="col mb-3">
                        @include("freizeitvoting.partials.nominierung", ["button" => true])
                    </div>
                @else
                    @if($votingActive)
                        <div class="col mb-5">
                            @include("freizeitvoting.partials.wirtshaus")
                        </div>
                    @else
                        <div class="col-12">
                            <div class="alert alert-info">
                                Voting noch nicht verfügbar
                            </div>
                        </div>
                    @endif
                @endif
            @endif
        @endif
    </div>
@endsection
