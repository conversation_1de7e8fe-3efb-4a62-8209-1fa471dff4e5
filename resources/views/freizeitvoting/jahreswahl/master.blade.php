<!DOCTYPE html>
<html lang="de">
<head>
    <title><PERSON><PERSON><PERSON> bewerten</title>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="_token" content="{!! csrf_token() !!}"/>
    <meta http-equiv="Cache-control" content="no-cache">

    <meta name="theme-color" content="#11100B"/>
    <link rel="shortcut icon" href="{{ url("img/freizeitvoting/favicon.ico?v1") }}"/>

    <!-- CSS-START -->
    <link rel="stylesheet" href="{{ url("inc/fontawesome-pro-6.5.2-min/dist/all.min.css") }}">
    <script src="{{ url("inc/fontawesome-pro-6.5.2-min/dist/all.min.js") }}"  type="text/javascript"></script>

    <link rel="stylesheet" href="{{url('inc/bootstrap-5.2.2/dist/css/bootstrap.min.css')}}">
    <script src="{{url('inc/bootstrap-5.2.2/dist/js/bootstrap.bundle.min.js')}}"></script>
    <link rel="stylesheet" type="text/css" href='{{ url("inc/select2-4.0.12/dist/css/select2.min.css") }}'/>
    <link rel="stylesheet" type="text/css" href='{{ url("/inc/css/freizeitvoting_fonts.css") }}?v1'/>
    <link rel="stylesheet" type="text/css" href='{{ url("/inc/css/freizeitvoting_jahreswahl.css") }}?v2'/>

    <!-- JS-START -->
    <script src="{{url('inc/jquery/jquery.js')}}"></script>
    <script type="text/javascript" src='{{ url("inc/select2-4.0.12/dist/js/select2.min.js") }}'></script>
    <script type="text/javascript" src='{{ url("inc/js/captcha.js") }}'></script>

</head>

<body>

<div class="container-fluid">

    @yield('content')

</div>

<script>
    function triggerPostMessage(){window.parent.postMessage({height:document.body.scrollHeight,width:document.body.scrollWidth},'*')}
    window.addEventListener('load',()=>{triggerPostMessage()});
    window.addEventListener('resize',()=>{triggerPostMessage()});
</script>

<script>
    var baseurl = "{{url('/')}}";
    var mobile = false;

    $(function () {
        if ($("#is-xs").css("display") != "none") {
            mobile = true;
        }

        @if(Session::has('message'))
        $(window).on('load', function () {
            $('html, body').animate({scrollTop: ($(".alert-success").offset().top - 30)});
        });
        @endif
        @if (count($errors) > 0)
        $(window).on('load', function () {
            $('html, body').animate({scrollTop: ($(".alert-danger").offset().top - 30)});
        });
        @endif
    });
</script>

<!-- ALLGEMEINES JS -->

<script type="text/javascript" src='{{ url("inc/js/freizeitvoting.js") }}?v1'></script>

@yield("viewscripts")

</body>
</html>

