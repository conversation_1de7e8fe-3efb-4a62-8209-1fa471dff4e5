@extends('freizeitvoting.jahreswahl.master')

@section('content')
    <div class="row">
        @if(Session::has('message'))
            <div class="col-12">
                <div class="alert alert-success">
                    <h1 class="alert-heading mb-0"><PERSON><PERSON><PERSON> Dank fürs Mitmachen!</h1>
                </div>
            </div>
        @else
            <div class="col-12">
                <form action="{{ url("jahreswahl/gasthaus/".$freizeit_gasthaus_id) }}" id="gasthaeuserForm" name="gasthaeuserForm" method="post">
                    {{ csrf_field() }}
                    <div class="row">
                        <div class="col-12">
                            @include("messages.error")
                        </div>
                        <div class="col-12 col-md-6 mb-3">
                            <label for="kategorie1" class="form-label">Essen <small>(0 - 50)</small></label>
                            <div class="range">
                                <div class="thumb-value color-tangerine font-weight-bold"></div>
                                <input type="range" class="form-range" value="{{ old("kategorie1", 0) }}" min="0" max="50" step="1" id="kategorie1" name="kategorie1"/>
                            </div>
                        </div>
                        <div class="col-12 col-md-6 mb-3">
                            <label for="kategorie2" class="form-label">Service <small>(0 - 10)</small></label>
                            <div class="range">
                                <div class="thumb-value color-tangerine font-weight-bold"></div>
                                <input type="range" class="form-range" value="{{ old("kategorie2", 0) }}" min="0" max="10" step="1" id="kategorie2" name="kategorie2"/>
                            </div>
                        </div>
                        <div class="col-12 col-md-6 mb-3">
                            <label for="kategorie3" class="form-label">Getränke <small>(0 - 15)</small></label>
                            <div class="range">
                                <div class="thumb-value color-tangerine font-weight-bold"></div>
                                <input type="range" class="form-range" value="{{ old("kategorie3", 0) }}" min="0" max="15" step="1" id="kategorie3" name="kategorie3"/>
                            </div>
                        </div>
                        <div class="col-12 col-md-6 mb-4">
                            <label for="kategorie4" class="form-label">Ambiente <small>(0 - 25)</small></label>
                            <div class="range">
                                <div class="thumb-value color-tangerine font-weight-bold"></div>
                                <input type="range" class="form-range" value="{{ old("kategorie4", 0) }}" min="0" max="25" step="1" id="kategorie4" name="kategorie4"/>
                            </div>
                        </div>

                        <div class="col-12 mb-3">
                            <div class="captcha">
                                <span>{!! captcha_img("flat_freizeit") !!}</span>
                                <button type="button" class="btn btn-dark" id="captchaReload" config="flat_freizeit">
                                    &#x21bb;
                                </button>
                            </div>
                        </div>
                        <div class="col-12">
                            <input type="text" name="captcha" id="captcha" class="form-control" placeholder="Sicherheitscode eingeben">
                        </div>

                        <div class="col-12 mt-5">
                            <button class="btn btn-dark btn-block btn-send">Bewertung absenden</button>
                        </div>
                    </div>
                </form>
            </div>
        @endif
    </div>
@endsection
