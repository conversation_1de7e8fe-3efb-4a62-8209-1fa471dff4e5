<div class="modal modal-xl fade modal-category" id="modal-category-{{$category->id }}" data-bs-backdrop="static" tabindex="-1" aria-hidden="true">
	<div class="modal-dialog modal-dialog-centered">
		<div class="modal-content">
			<div class="modal-body p-4 p-lg-5">
				<div class="text-end mb-3 mb-lg-0">
					<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
				</div>

				<div class="row">
					<div class="col-lg-2 d-none d-lg-flex justify-content-start align-items-start">
						{{--<img src="{{ $category->imageFullPath }}" class="d-block w-100" />--}}
                        @php
                            $overlays = ['architektur', 'demokratisch', 'digital', 'innovation', 'tradition', 'gesundheit'];
                            $overlayMatch = collect($overlays)->first(function($overlay) use ($category) {
                                return str_contains(strtolower($category->title), $overlay);
                            });
                        @endphp
                        @if($overlayMatch)
                            <img src="{{ asset('img/zeigprofilaward/overlay/'.$overlayMatch. '.png') }}" class="d-block w-100 mb-3">
                        @endif
					</div>
					<div class="col-12 col-lg-10 d-flex justify-content-start align-items-end">
						<div>
							<div class="h1 mb-0">Kategorie {{$category->title}}</div>
                            <div class="category-subtitle">
                                {!! $category->subtitle
                                    ? $category->subtitle
                                    : '&nbsp;' !!}
                            </div>
						</div>
					</div>
					<div class="col-12">
						<div class="category-text">{{$category->text}}</div>
						<div class="text-center">
							@include("zeigprofilaward.partials.buttons", ["category" => $category])
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
