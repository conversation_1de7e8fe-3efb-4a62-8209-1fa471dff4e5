<div class="category-wrapper">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12 mb-4">
                <div class="h1">Jury Voting</div>
            </div>
            <div class="col-12 mb-4">
                <span class="h2">{{$name}}</span>
            </div>
        </div>
        <div class="row">
            <div class="col-12">
                @foreach($categories as $category)
                    @if($category->id == 6 && $phase != 3)
                        @continue
                    @endif
                    <div class="card category-card mb-5 bg-black" id="{{$category->id}}">
                        <div class="card-body">
                            <form action="#" method="post" data-category="{{$category->id}}" id="juryform_{{$category->id}}">
                                <div class="row">
                                    <div class="col-12 ">
                                        <h1>
                                            <img src="{{ $category->imageFullPath }}" class="d-inline-block rounded-circle me-3" width="80rem" />{{$category->title}}
                                        </h1>
                                        <p class="text-white">{!! $category->subtitle !!}</p>
                                        <p class="text-white">{!! $category->text !!}</p>
                                    </div>
                                    <div class="col-12" id="vote_{{$category->id}}">
                                        @if($hasVote = $category->juryvotes->contains(function($item) use ($hash) {
                                            return $item->hash === $hash; // Exakte Übereinstimmung
                                        }))
                                                <?php
                                                $jurysubmit = $category->juryvotes->filter(function($item) use ($hash){
                                                    return $item->hash === $hash;
                                                });
                                                ?>
                                            <div class="row mb-5 mt-3">
                                                <div class="col-12 text-white">
                                                    <div class="alert alert-success w-100">
                                                        Ich habe für dieses Projekt gestimmt <br>
                                                        <div class="badge bg-dark">{{\App\Models\ZeigProfilAward\ZeigProfilAwardSubmit::find($jurysubmit->first()->submit_id)->company}}</div><br>
                                                        <button id="toggleRow_{{$category->id}}" class="btn btn-secondary mt-3">Ich möchte für ein anderes Projekt stimmen</button>
                                                    </div>
                                                </div>
                                            </div>
                                        @endif


                                      {{--  <div class="row {{($hasVote) ? 'd-none' : 'd-flex'}} " id="detailsRow_{{$category->id}}">
                                            @if($category->id == 6)
                                                @foreach($epu as $submit)
                                                        <?php
                                                        $directory = storage_path('app/public/zeigprofilaward/submits/'.$submit->id.'/');
                                                        if(\Illuminate\Support\Facades\File::exists($directory)){
                                                            $files = \Illuminate\Support\Facades\File::files($directory);

                                                            $company_picture = "";
                                                            $company_logo = "";
                                                            $project_picture = "";
                                                            $project_skizze = "";

                                                            foreach($files as $file){
                                                                switch($file->getRelativePathname()){
                                                                    case (strpos($file->getRelativePathname(), 'company_picture')):
                                                                        $company_picture = $file->getRelativePathname();
                                                                        break;
                                                                    case  (strpos($file->getRelativePathname(), 'companylogo')):
                                                                        $company_logo = $file->getRelativePathname();
                                                                        break;
                                                                    case  (strpos($file->getRelativePathname(), 'project_picture')):
                                                                        $project_picture = $file->getRelativePathname();
                                                                        break;
                                                                    case  (strpos($file->getRelativePathname(), 'company_skizze')):
                                                                        $project_skizze = $file->getRelativePathname();
                                                                        break;
                                                                }
                                                            }
                                                        }
                                                        ?>
                                                    <div class="col-12 col-lg-6 voting-col">
                                                        <div class="card h-100 voting-card">
                                                            <div class="card-header">
                                                                <input class="vote-radio" type="radio" id="vote_{{$submit->id}}" name="vote" value="{{$submit->id}}">
                                                                <input type="hidden" value="{{$hash}}" name="hash">
                                                            </div>
                                                            <div class="card-body d-flex align-items-center justify-content-center" data-toggle="modal" data-target="#projektmodal" id="modalbutton_{{$submit->id}}"
                                                                 data-firma="{{$submit->company}}"
                                                                 data-id="{{$submit->id}}"
                                                                 data-vorname="{{$submit->firstname}}"
                                                                 data-nachname="{{$submit->lastname}}"
                                                                 data-statement="{{$submit->mission_statement}}"
                                                                 data-claim="{{$submit->claim}}"
                                                                 data-goal="{{$submit->project_goal}}"
                                                                 data-file="{{$submit->fileshash}}"
                                                                 data-logo="{{(!empty($company_logo)) ? $company_logo : ""}}"
                                                                 data-company="{{(!empty($company_picture)) ? $company_picture : ""}}"
                                                                 data-project="{{(!empty($project_picture)) ? $project_picture : ""}}"
                                                                 data-skizze="{{(!empty($project_skizze)) ? $project_skizze : ""}}"
                                                                 data-phase="{{$phase}}"
                                                            >
                                                                <img class="d-md-block d-none w-50" src="{{ url('/').'/storage/app/public/zeigprofilaward/submits/'.$submit->id.'/'.((!empty($company_logo)) ? $company_logo : "")}}">
                                                                <img class="d-md-none d-block w-50" src="{{ url('/').'/storage/app/public/zeigprofilaward/submits/'.$submit->id.'/'.((!empty($company_logo)) ? $company_logo : "")}}">
                                                            </div>
                                                        </div>
                                                        <div class="voting-title pt-2">
                                                            {{$submit->mission_statement}}
                                                        </div>
                                                    </div>
                                                @endforeach
                                            @else
                                                @foreach($category->jurySubmits as $submit)
                                                        <?php
                                                        $directory = storage_path('app/public/zeigprofilaward/submits/'.$submit->id.'/');
                                                        if(\Illuminate\Support\Facades\File::exists($directory)){
                                                            $files = \Illuminate\Support\Facades\File::files($directory);

                                                            $company_picture = "";
                                                            $company_logo = "";
                                                            $project_picture = "";
                                                            $project_skizze = "";

                                                            foreach($files as $file){
                                                                switch($file->getRelativePathname()){
                                                                    case (strpos($file->getRelativePathname(), 'company_picture')):
                                                                        $company_picture = $file->getRelativePathname();
                                                                        break;
                                                                    case  (strpos($file->getRelativePathname(), 'companylogo')):
                                                                        $company_logo = $file->getRelativePathname();
                                                                        break;
                                                                    case  (strpos($file->getRelativePathname(), 'project_picture')):
                                                                        $project_picture = $file->getRelativePathname();
                                                                        break;
                                                                    case  (strpos($file->getRelativePathname(), 'company_skizze')):
                                                                        $project_skizze = $file->getRelativePathname();
                                                                        break;
                                                                }
                                                            }
                                                        }
                                                        ?>
                                                    <div class="col-12 col-lg-6 voting-col">
                                                        <div class="card h-100 voting-card">
                                                            <div class="card-header">
                                                                <input class="vote-radio" type="radio" id="vote_{{$submit->id}}" name="vote" value="{{$submit->id}}">
                                                                <input type="hidden" value="{{$hash}}" name="hash">
                                                            </div>
                                                            <div class="card-body d-flex align-items-center justify-content-center" data-toggle="modal" data-target="#projektmodal" id="modalbutton_{{$submit->id}}"
                                                                 data-firma="{{$submit->company}}"
                                                                 data-id="{{$submit->id}}"
                                                                 data-vorname="{{$submit->firstname}}"
                                                                 data-nachname="{{$submit->lastname}}"
                                                                 data-statement="{{$submit->mission_statement}}"
                                                                 data-claim="{{$submit->claim}}"
                                                                 data-goal="{{$submit->project_goal}}"
                                                                 data-file="{{$submit->fileshash}}"
                                                                 data-logo="{{(!empty($company_logo)) ? $company_logo : ""}}"
                                                                 data-company="{{(!empty($company_picture)) ? $company_picture : ""}}"
                                                                 data-project="{{(!empty($project_picture)) ? $project_picture : ""}}"
                                                                 data-skizze="{{(!empty($project_skizze)) ? $project_skizze : ""}}"
                                                                 data-phase="{{$phase}}"
                                                            >
                                                                <img class="d-md-block d-none w-50" src="{{ url('/').'/storage/app/public/zeigprofilaward/submits/'.$submit->id.'/'.((!empty($company_logo)) ? $company_logo : "")}}">
                                                                <img class="d-md-none d-block w-50" src="{{ url('/').'/storage/app/public/zeigprofilaward/submits/'.$submit->id.'/'.((!empty($company_logo)) ? $company_logo : "")}}">
                                                            </div>
                                                        </div>
                                                        <div class="voting-title pt-2">
                                                            {{$submit->mission_statement}}
                                                        </div>
                                                    </div>
                                                @endforeach
                                            @endif

                                        </div>--}}
                                        <div class="row">
                                            <div class="col-12 text-center">
                                                <button goodcaptcha="true" class="btn btn-danger voteButton btn-lg" id="voteButton_{{$category->id}}" type="button">Jetzt voten!</button>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>

    </div>
</div>


<div class="modal modal-xl" tabindex="-1" id="projektmodal" data-bs-backdrop="static">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header d-flex align-items-start">
                <div class="row d-flex align-items-center">
                    <div class="col-2 " id="logo">
                    </div>
                    <div class="col-10 ">
                        <div class="row">
                            <div class="col-12" id="firmenname">

                            </div>
                            <div class="col-12 mb-3 ">
                                <small>
                                    <span id="claim"></span>
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
                <button type="button" class="btn-close float-end " data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-12">
                        <div class="row ">
                            <div class="col-lg-6 col-12 mb-3">
                                <div class="row">
                                    <div class="col-12 mb-2">
                                        <span id="statement"></span>
                                    </div>
                                    <div class="col-12">
                                        <div class="row">
                                            <div class="col-12 text-start">
                                                <strong>Ziel</strong>
                                            </div>
                                            <div class="col-12">
                                                <span id="goal"></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6 col-12 d-flex align-items-center mb-3">
                                <div class="row">
                                    <div class="col-lg-6 col-12 mb-2" id="project_picture">

                                    </div>
                                    <div class="col-lg-6 col-12 mb-2" id="company_picture">

                                    </div>
                                </div>

                            </div>
                        </div>
                        <div class="col-12 mt-3">
                            <strong>Name:</strong>
                            <span id="name"></span>
                        </div>
                    </div>
                    <div class="col-12 mb-3" id="download">
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>
