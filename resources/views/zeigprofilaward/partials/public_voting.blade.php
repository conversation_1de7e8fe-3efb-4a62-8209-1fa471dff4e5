<div class="category-wrapper">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12 mb-4">
                <div class="h1">Voting</div>
            </div>
        </div>
        <div class="row">
            <div class="col-12">
                @foreach($categories as $category)
                    {{--@if($category->id == 6 && $phase != 3)
                        @continue
                    @endif--}}
                    <div class="card category-card mb-5 bg-black" id="{{$category->id}}">
                        <div class="card-body">
                            <form action="#" method="post" data-category="{{$category->id}}" id="form_{{$category->id}}">
                                <div class="row">
                                    <div class="col-12 ">
                                        <h1>
                                            <img src="{{ $category->imageFullPath }}" class="d-inline-block rounded-circle me-3" width="80rem" />{{str_replace(' -', '', $category->title)}}
                                        </h1>
                                        <p class="text-white">{!! $category->subtitle !!}</p>
                                        <p class="text-white">{!! $category->text !!}</p>
                                    </div>
                                    <div class="col-12" id="vote_{{$category->id}}">
                                        <div class="row">
                                            @foreach($category->submits as $submit)
                                                    <?php
                                                    $directory = storage_path('app/public/zeigprofilaward/submits/'.$submit->id.'/');

                                                    if(\Illuminate\Support\Facades\File::exists($directory)){
                                                        $files = \Illuminate\Support\Facades\File::files($directory);

                                                        $company_picture = "";
                                                        $company_logo = "";
                                                        $project_picture = "";
                                                        $project_skizze = "";



                                                        foreach($files as $file){
                                                            switch($file->getRelativePathname()){
                                                                case (strpos($file->getRelativePathname(), 'umsetzung')):
                                                                    $company_picture = $file->getRelativePathname();
                                                                    break;
                                                                case  (strpos($file->getRelativePathname(), 'companylogo')):
                                                                    $company_logo = $file->getRelativePathname();

                                                                    break;
                                                                case  (strpos($file->getRelativePathname(), 'unternehmenspraesentation')):
                                                                    $project_picture = $file->getRelativePathname();
                                                                    break;
                                                                case  (strpos($file->getRelativePathname(), 'referenzen')):
                                                                    $project_skizze = $file->getRelativePathname();
                                                                    break;
                                                            }
                                                        }
                                                    }
                                                    ?>

                                            <style>
                                                /*.image-container {
                                                    height: 250px; !* oder was optisch gut aussieht *!
                                                    padding: 0;
                                                    overflow: hidden;
                                                    display: flex;
                                                    align-items: center;
                                                    justify-content: center;
                                                }*/


                                                .voting-card {
                                                    background-color: black !important;
                                                    border: 1px solid white !important;
                                                    border-radius: 15px !important;
                                                    border-top-right-radius: 0 !important;
                                                }

                                                .voting-card .card-header {
                                                    background-color: transparent !important;
                                                    border-bottom: none !important;
                                                    border-radius: 15px 0 0 0 !important;
                                                }

                                                .voting-card .card-body {
                                                    padding: 20px !important;
                                                    border-radius: 0 0 15px 15px !important;
                                                    height: calc(100% - 60px) !important; /* Abzug für Header-Höhe */
                                                    /*display: flex !important;*/
                                                    align-items: stretch !important;
                                                }

                                                .voting-card .card-body img {
                                                    border-radius: 10px !important;
                                                    border-top-right-radius: 0 !important;
                                                    background-color: white !important;
                                                    width: 100% !important;
                                                    height: 100% !important;
                                                    /*object-fit: cover !important;*/
                                                    /*object-position: center !important;*/
                                                }

                                                .voting-image {
                                                    width: 100%;
                                                    height: 100%;
                                                    object-fit: cover;
                                                    object-position: center;
                                                }

                                                #download {
                                                    display: flex;
                                                    flex-wrap: wrap;
                                                    gap: 20px;
                                                    margin-top: 20px;
                                                }

                                                .download-item {
                                                    flex: 1 1 calc(33.333% - 20px); /* 3 gleich breite Spalten */
                                                    box-sizing: border-box;
                                                    border: 1px solid #ccc;
                                                    padding: 10px;
                                                    background: #f9f9f9;
                                                }
                                                .download-item embed {
                                                    width: 100%;
                                                    height: 300px;
                                                    border: 1px solid #ccc;
                                                }
                                            </style>
                                                <div class="col-12 col-lg-4 voting-col mt-3">
                                                    <div class="card h-100 voting-card">
                                                        <div class="card-header">
                                                            <input class="vote-radio" type="radio" id="vote_{{$submit->id}}" name="vote" value="{{$submit->id}}">
                                                        </div>

                                                        <div class="card-body image-container" data-toggle="modal" data-target="#projektmodal"
                                                             id="modalbutton_{{$submit->id}}"
                                                             data-firma="{{$submit->company}}"
                                                             data-id="{{$submit->id}}"
                                                             data-vorname="{{$submit->firstname}}"
                                                             data-nachname="{{$submit->lastname}}"
                                                             data-statement="{{$submit->mission_statement}}"
                                                             data-claim="{{$submit->claim}}"
                                                             data-goal="{{$submit->project_goal}}"
                                                             data-file="{{$submit->fileshash}}"
                                                             data-logo="{{(!empty($company_logo)) ? $company_logo : ""}}"
                                                             data-company="{{(!empty($company_picture)) ? $company_picture : ""}}"
                                                             data-project="{{(!empty($project_picture)) ? $project_picture : ""}}"
                                                             data-skizze="{{(!empty($project_skizze)) ? $project_skizze : ""}}"
                                                        >
                                                            <img class="<!--img-fluid voting-image--> w-100" src="{{ url('/').'/storage/app/public/zeigprofilaward/submits/'.$submit->id.'/'.((!empty($company_logo)) ? $company_logo : '') }}" alt="Logo">
                                                        </div>
                                                    </div>
                                                    <div class="voting-title pt-2">
                                                        {{$submit->mission_statement}}
                                                    </div>
                                                </div>

                                               {{-- <div class="col-12 col-lg-4 voting-col">
                                                    <div class="card h-100 voting-card">
                                                        <div class="card-header">
                                                            <input class="vote-radio" type="radio" id="vote_{{$submit->id}}" name="vote" value="{{$submit->id}}">
                                                        </div>
                                                        <div class="card-body d-flex align-items-center justify-content-center" data-toggle="modal" data-target="#projektmodal" id="modalbutton_{{$submit->id}}"
                                                             data-firma="{{$submit->company}}"
                                                             data-id="{{$submit->id}}"
                                                             data-vorname="{{$submit->firstname}}"
                                                             data-nachname="{{$submit->lastname}}"
                                                             data-statement="{{$submit->mission_statement}}"
                                                             data-claim="{{$submit->claim}}"
                                                             data-goal="{{$submit->project_goal}}"
                                                             data-file="{{$submit->fileshash}}"
                                                             data-logo="{{(!empty($company_logo)) ? $company_logo : ""}}"
                                                             data-company="{{(!empty($company_picture)) ? $company_picture : ""}}"
                                                             data-project="{{(!empty($project_picture)) ? $project_picture : ""}}"
                                                             data-skizze="{{(!empty($project_skizze)) ? $project_skizze : ""}}"
                                                        >



                                                            <img class="d-md-block d-none w-100" src="{{ url('/').'/storage/app/public/zeigprofilaward/dev/submits/'.$submit->id.'/'.((!empty($company_logo)) ? $company_logo : "")}}">
                                                            <img class="d-md-none d-block w-100" src="{{ url('/').'/storage/app/public/zeigprofilaward/dev/submits/'.$submit->id.'/'.((!empty($company_logo)) ? $company_logo : "")}}">
                                                        </div>
                                                    </div>
                                                    <div class="voting-title pt-2">
                                                        {{$submit->mission_statement}}
                                                    </div>
                                                </div>--}}
                                            @endforeach
                                        </div>
                                        <div class="row">
                                            <div class="col-12 text-center">
                                                <button goodcaptcha="true" class="btn btn-danger voteButton btn-lg" id="voteButton_{{$category->id}}" type="button">Jetzt voten!</button>
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>

    </div>
</div>


<div class="modal modal-xl" tabindex="-1" id="projektmodal" data-bs-backdrop="static">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header d-flex align-items-start">
                <div class="row d-flex align-items-center">
                    <div class="col-2 " id="logo">
                    </div>
                    <div class="col-10 ">
                        <div class="row">
                            <div class="col-12" id="firmenname">

                            </div>
                            <div class="col-12 mb-3 ">
                                <small>
                                    <span id="claim"></span>
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
                <button type="button" class="btn-close float-end " data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-12">
                        <div class="row ">
                            <div class="col-12">
                                <div class="col-12 mb-2">
                                    <span id="statement" c></span>
                                </div>
                                <div class="col-12">
                                    <div class="row">
                                        <div class="col-12 text-start">
                                            <strong>Ziel</strong>
                                        </div>
                                        <div class="col-12">
                                            <span id="goal"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-4 d-flex align-items-center">
                                <div class="col-12 mb-2" id="project_picture">

                                </div>
                            </div>
                        </div>
                        <div class="col-12 mt-3">
                            <strong>Name:</strong>
                            <span id="name"></span>
                        </div>
                    </div>
                    <div class="col-12 mb-3" id="download">
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>
