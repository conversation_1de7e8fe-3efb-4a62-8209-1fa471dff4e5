@if(!empty($option))
    <div class="col-12 {{ $option["col"] }}">
        <div class="mb-3">
            <label for="{{ $option['name'] }}" class="form-label">
                {{ $option['placeholder'] }}{{ $option['required'] ? '*' : '' }}
            </label>

            <div class="position-relative">
                <input
                    type="{{ $option['type'] }}"
                    class="form-control pe-5"
                    id="{{ $option['name'] }}"
                    name="{{ $option['name'] }}"
                    value="{{ isset($option['value']) ? $option['value'] : old($option['name']) }}"
                    placeholder="{{ $option['info'] ?? $option['placeholder'] }}"
                    @if($option['type'] === 'date') max="{{ \Carbon\Carbon::now()->subDay()->format('Y-m-d') }}" @endif
                    @if(isset($option['limit30'])) oninput="limitWords(this, 30)" @endif
                    @if(isset($option['limit300'])) oninput="limitWords(this, 300)" @endif
                />

                {{-- Modal-Button, wenn vorhanden --}}
                @if(!empty($option['modal']))
                    <button type="button"
                            class="btn btn-outline-secondary position-absolute top-50 end-0 translate-middle-y me-2 py-0 px-2"
                            data-bs-toggle="modal"
                            data-bs-target="#modal_{{ $option['name'] }}"
                            style="font-size: 0.85rem;"
                    >
                        <i class="fas fa-info-circle"></i>
                    </button>
                @endif
            </div>

            <small id="{{ $option['name'] }}_warning" class="text-danger d-none"></small>
        </div>
    </div>

    {{-- Modal-Inhalt --}}
    @if(!empty($option['modal']))
        <div class="modal fade" id="modal_{{ $option['name'] }}" tabindex="-1" aria-labelledby="modalLabel_{{ $option['name'] }}" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered modal-md">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="modalLabel_{{ $option['name'] }}">{!!  $option['modal_header']!!}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Schließen"></button>
                    </div>
                    <div class="modal-body">
                        {!! nl2br($option['modal']) !!}
                    </div>
                </div>
            </div>
        </div>
    @endif
@endif

<script>
    function limitWords(input, maxWords){
        const warning = document.getElementById(input.id + "_warning");
        const words = input.value.trim().split(/\s+/).filter(word => word.length > 0);

        if(words.length > maxWords){
            input.value = words.slice(0, maxWords).join(" ");
        }

        if(words.length >= maxWords){
            warning.classList.remove("d-none");
            warning.textContent = "Maximal " + maxWords + " Wörter erlaubt.";
        }else{
            warning.classList.add("d-none");
            warning.textContent = "";
        }
    }
</script>
