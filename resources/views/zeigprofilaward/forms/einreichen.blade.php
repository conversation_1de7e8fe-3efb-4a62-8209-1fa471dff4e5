@extends('zeigprofilaward.master')

@section('content')
    <div class="program-wrapper">
        <div class="container-fluid">
            @if(today()->gte('2025-06-21') && today()->lte('2025-09-13'))
                <div class="row">
                    <div class="col-12">
                        <h1>Einreichformular</h1>
                    </div>
                    <hr>
                    <form action="{{ url("einreichen") }}" id="einreichenForm" name="einreichenForm" method="post" enctype="multipart/form-data">
                        {{ csrf_field() }}


                        @if(Session::has('message'))
                            <div class="alert alert-success">
                                {!! Session::get('message') !!}
                            </div>

                            <script>
                                fbq('track', 'Lead');
                            </script>
                        @else

                            @include("messages.error")
                            <div class="col-12 mb-3">

                                <div class="card">
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-12 col-md-8">
                                                <div class="row">
                                                    @include("zeigprofilaward.partials.textfield", ["option" => ["col" => "col-md-12 mb-3", "type" => "text", "name" => "firstname", "placeholder" => "Vorname", "required" => false]])
                                                    @include("zeigprofilaward.partials.textfield", ["option" => ["col" => "col-md-12 mb-3", "type" => "text", "name" => "lastname", "placeholder" => "Nachname", "required" => false]])
                                                    @include("zeigprofilaward.partials.textfield", ["option" => ["col" => "col-md-12 mb-3", "type" => "email", "name" => "email", "placeholder" => "E-Mail Adresse", "required" => true]])
                                                    @include("zeigprofilaward.partials.textfield", ["option" => ["col" => "col-md-12", "type" => "text", "name" => "company", "placeholder" => "Firmenname", "required" => true]])

                                                </div>

                                            </div>

                                        </div>

                                    </div>

                                </div>

                            </div>
                            <div class="col-12">
                                <div class="card mb-3">
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-12 col-md-8">
                                                <div class="row">
                                                    <div class="col-12 col-md-12 mb-3">
                                                        <div class="form-floating">
                                                            <select class="form-select" id="category" name="category">
                                                                <option value="">--- Bitte wählen ---</option>
                                                                @foreach($categories as $category)
                                                                    <option value="{{$category->id}}" {{ (old("category") == $category->id || $selectedCat == $category->id) ? "selected" : "" }}>{{$category->title}}</option>
                                                                @endforeach
                                                            </select>
                                                            <label for="category">Kategorie*</label>
                                                        </div>
                                                    </div>

                                                    {{-- <div class="col-12 col-md-6 mb-3">
                                                         <div class="form-floating">
                                                             <select class="form-select" id="epu" name="epu">
                                                                 <option value="">--- Bitte wählen ---</option>
                                                                 <option value="J" {{ (old("epu") == "J") ? "selected" : "" }}>Ja</option>
                                                                 <option value="N" {{ (old("epu") == "N") ? "selected" : "" }}>Nein</option>
                                                             </select>
                                                             <label for="epu">Ein-Personen-Unternehmen (EPU)*</label>
                                                         </div>
                                                     </div>--}}


                                                    @include("zeigprofilaward.partials.textfield", ["option" => ["col" => "col-md-6 mb-3", "type" => "date", "name" => "founding_date", "placeholder" => "Gründungsdatum", "required" => true]])
                                                    @include("zeigprofilaward.partials.textfield", ["option" => ["col" => "col-md-6 mb-3", "type" => "text", "name" => "founding_place", "placeholder" => "Gründungsort", "required" => true]])
                                                    @include("zeigprofilaward.partials.textfield", ["option" => ["col" => "col-md-6 mb-3", "type" => "text", "name" => "claim", "placeholder" => "Claim/Slogan", "required" => false]])
                                                    @include("zeigprofilaward.partials.textfield", ["option" => ["col" => "col-md-6 mb-3", "type" => "text", "name" => "mission_statement", "placeholder" => "Unternehmensphilosophie", "required" => true, "limit30" => true, "info" => "Maximal 30 Wörter", "modal" => "Bitte um eine Zweckerklärung für dein Unternehmen und dein Werte-Versprechen.<br> Kurz: Wieso ist dein Unternehmen wichtig für den Markt Österreich?", "modal_header" => "Unternehmensphilosophie <br><small><strong>Warum machst du das?</strong></small>"]])
                                                    @include("zeigprofilaward.partials.textfield", ["option" => ["col" => "col-md-6 mb-3", "type" => "text", "name" => "project_goal", "placeholder" => "Unternehmensidee/-ziel", "required" => true, "limit300" => true, "info" => "Maximal 300 Wörter", "modal_header" => "Unternehmensidee / Ziel<br><small><strong>Was machst du genau?</strong></small>", "modal" => "Beschreibung deiner Idee für die Unternehmensinitiative und/oder seiner Gründung sowie das Ziel, das damit erreicht werden soll."]])
                                                    {{--
                                                                                                        @include("zeigprofilaward.partials.textfield", ["option" => ["col" => "col-md-6 mb-3", "type" => "text", "name" => "credentials", "placeholder" => "Referenzen", "required" => false]])
                                                    --}}

                                                    {{--  <div class="col-6 mb-3">
                                                          <label for="company_logo" class="form-label">Upload Unternehmenslogo*</label>
                                                          <input type="file" class="form-control" id="company_logo" name="company_logo" />
                                                      </div>

                                                      <div class="col-6 mb-3">
                                                          <label for="company_picture" class="form-label">Upload Unternehmensbild</label>
                                                          <input type="file" class="form-control" id="company_picture" name="company_picture">
                                                      </div>

                                                      <div class="col-6 mb-3">
                                                          <label for="company_skizze" class="form-label">Upload Unternehmensskizze*
                                                              <small>(PDF, max. 10MB)</small>
                                                          </label>
                                                          <input type="file" accept="application/pdf" class="form-control" id="company_skizze" name="company_skizze">
                                                      </div>

                                                      <div class="col-6 mb-3">
                                                          <label for="project_picture" class="form-label">Upload Projektbild</label>
                                                          <input type="file" class="form-control" id="project_picture" name="project_picture">
                                                      </div>--}}


                                                    <div class="col-12 col-md-6 mb-3">
                                                        <label class="form-label">
                                                            Maßnahmen und Umsetzung*
                                                            <button type="button"
                                                                    class="btn btn-sm btn-outline-secondary ms-2 py-1 px-1"
                                                                    data-bs-toggle="modal"
                                                                    data-bs-target="#modal_measures"
                                                                    style="font-size: 0.8rem; line-height: 1;">
                                                                <i class="fas fa-info-circle"></i>
                                                            </button>
                                                        </label>

                                                        <div class="custom-file-input-wrapper position-relative">
                                                            <input type="file" class="custom-file-input" id="company_picture" name="company_picture" onchange="updateFileName(this)">
                                                            <div class="form-control-file-mock custom-file-display fw-bolder text-center" id="display_company_picture">
                                                                Upload PDF
                                                            </div>
                                                            <i class="fas fa-upload file-upload-icon"></i>
                                                        </div>
                                                    </div>

                                                    <div class="modal fade" id="modal_measures" tabindex="-1" aria-labelledby="modalLabel_measures" aria-hidden="true">
                                                        <div class="modal-dialog modal-dialog-centered modal-md">
                                                            <div class="modal-content">
                                                                <div class="modal-header">
                                                                    <h5 class="modal-title" id="modalLabel_measures">Maßnahmen und Umsetzung</h5>
                                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Schließen"></button>
                                                                </div>
                                                                <div class="modal-body">
                                                                    Was tun Sie genau?
                                                                    <br><br>
                                                                    Bitte um Beschreibung deiner ergriffenen Maßnahme für ein konkretes Bild des Geschäftsmodells inkl. Finanzierung, Markteintritt & Businessplan.
                                                                    <br><br>
                                                                    PDF upload (max. 3 Seiten)
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>


                                                    <div class="col-12 col-md-6 mb-3">
                                                        <label class="form-label">
                                                            Unternehmenslogo*
                                                            <!-- Info-Button -->
                                                            <button type="button"
                                                                    class="btn btn-sm btn-outline-secondary ms-2 py-1 px-1"
                                                                    data-bs-toggle="modal"
                                                                    data-bs-target="#modal_logo"
                                                                    style="font-size: 0.8rem; line-height: 1;">
                                                                <i class="fas fa-info-circle"></i>
                                                            </button>
                                                        </label>

                                                        <div class="custom-file-input-wrapper position-relative">
                                                            <input type="file" class="custom-file-input" id="company_logo" name="company_logo" onchange="updateFileName(this)">
                                                            <div class="form-control-file-mock custom-file-display fw-bolder text-center" id="display_company_logo">
                                                                Upload Logo
                                                            </div>
                                                            <i class="fas fa-upload file-upload-icon"></i>
                                                        </div>
                                                    </div>

                                                    <div class="modal fade" id="modal_logo" tabindex="-1" aria-labelledby="modalLabel_logo" aria-hidden="true">
                                                        <div class="modal-dialog modal-dialog-centered modal-md">
                                                            <div class="modal-content">
                                                                <div class="modal-header">
                                                                    <h5 class="modal-title" id="modalLabel_logo">Unternehmenslogo</h5>
                                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Schließen"></button>
                                                                </div>
                                                                <div class="modal-body">
                                                                    Logo-upload als PNG oder JPG
                                                                    <br><br>
                                                                    3MB maximal
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="col-12 col-md-6 mb-3">
                                                        <label class="form-label">
                                                            Referenzen

                                                            <button type="button"
                                                                    class="btn btn-sm btn-outline-secondary ms-2 py-1 px-1"
                                                                    data-bs-toggle="modal"
                                                                    data-bs-target="#modal_referenzen"
                                                                    style="font-size: 0.8rem; line-height: 1;">
                                                                <i class="fas fa-info-circle"></i>
                                                            </button>
                                                        </label>

                                                        <div class="custom-file-input-wrapper position-relative">
                                                            <input type="file" accept="application/pdf" class="custom-file-input" id="company_skizze" name="company_skizze" onchange="updateFileName(this)">
                                                            <div class="form-control-file-mock custom-file-display fw-bolder text-center" id="display_company_skizze">
                                                                Upload PDF
                                                            </div>
                                                            <i class="fas fa-upload file-upload-icon"></i>
                                                        </div>
                                                    </div>
                                                    <!-- Modal -->
                                                    <div class="modal fade" id="modal_referenzen" tabindex="-1" aria-labelledby="modalLabel_referenzen" aria-hidden="true">
                                                        <div class="modal-dialog modal-dialog-centered modal-md">
                                                            <div class="modal-content">
                                                                <div class="modal-header">
                                                                    <h5 class="modal-title" id="modalLabel_referenzen">Referenzen</h5>
                                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Schließen"></button>
                                                                </div>
                                                                <div class="modal-body">
                                                                    Beschreibung der bereits repräsentativ umgesetzten Projektschritte zur Erreichung des Unternehmensziels.<br><br>
                                                                    PDF Upload (maximal 2 Seiten)
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="col-12 col-md-6 mb-3">
                                                        <label class="form-label">
                                                            Unternehmenspräsentation*
                                                            <!-- Info-Button -->
                                                            <button type="button"
                                                                    class="btn btn-sm btn-outline-secondary ms-2 py-1 px-1"
                                                                    data-bs-toggle="modal"
                                                                    data-bs-target="#modal_praesentation"
                                                                    style="font-size: 0.8rem; line-height: 1;">
                                                                <i class="fas fa-info-circle"></i>
                                                            </button>
                                                        </label>

                                                        <div class="custom-file-input-wrapper position-relative">
                                                            <input type="file" class="custom-file-input" id="project_picture" name="project_picture" onchange="updateFileName(this)">
                                                            <div class="form-control-file-mock custom-file-display fw-bolder text-center" id="display_project_picture">
                                                                Upload PDF
                                                            </div>
                                                            <i class="fas fa-upload file-upload-icon"></i>
                                                        </div>
                                                    </div>

                                                    <div class="modal fade" id="modal_praesentation" tabindex="-1" aria-labelledby="modalLabel_praesentation" aria-hidden="true">
                                                        <div class="modal-dialog modal-dialog-centered modal-md">
                                                            <div class="modal-content">
                                                                <div class="modal-header">
                                                                    <h5 class="modal-title" id="modalLabel_praesentation">Unternehmenspräsentation</h5>
                                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Schließen"></button>
                                                                </div>
                                                                <div class="modal-body">
                                                                    Bitte lade hier deine Unternehmenspräsentation hoch. Sie soll uns einen kompakten Überblick über dein Unternehmen, dein Geschäftsmodell, deine Alleinstellungsmerkmale sowie deine strategische Ausrichtung bieten. Die Präsentation wird einer Expertenjury zur Bewertung vorgelegt und sollte daher professionell, klar strukturiert und aussagekräftig sein.
                                                                    <br><br>PDF Upload (maximal 2 Seiten)
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>


                                                    <script>
                                                        function updateFileName(input){
                                                            const displayId = "display_" + input.id;
                                                            const fileName = input.files.length > 0 ? input.files[0].name : "Datei auswählen...";
                                                            document.getElementById(displayId).textContent = fileName;
                                                        }
                                                    </script>
                                                </div>

                                            </div>


                                        </div>


                                    </div>

                                </div>
                                <div class="text-start">

                                    <button goodcaptcha="true" class="btn btn-lg btn-danger px-5 fw-bold">Einreichen</button>

                                </div>

                            </div>

                        @endif
                    </form>
                </div>

            @else
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="alert alert-info">
                            Die Möglichkeit zur Projekteinreichung steht aktuell nicht zur Verfügung.
                        </div>
                    </div>
                </div>
            @endif

            {{--  <hr>
              <div class="col-12">
                  <div class="card card-program">
                      <div class="card-body d-flex flex-column justify-content-center align-items-center">
                          <div class="card-header h1">Einreichformular</div>
                          <div class="card-text d-block w-100">
                              <div class="row">
                                  <div class="col-12 col-md-8 mx-auto">
                                      <form action="{{ url("einreichen") }}" id="einreichenForm" name="einreichenForm" method="post" enctype="multipart/form-data">
                                          {{ csrf_field() }}


                                          @if(Session::has('message'))
                                              <div class="alert alert-success">
                                                  {!! Session::get('message') !!}
                                              </div>

                                              <script>
                                                  fbq('track', 'Lead');
                                              </script>
                                          @else

                                              @include("messages.error")

                                              <div class="row">
                                                  @include("zeigprofilaward.partials.textfield", ["option" => ["col" => "col-md-12 mb-3", "type" => "text", "name" => "firstname", "placeholder" => "Vorname", "required" => false]])
                                                  @include("zeigprofilaward.partials.textfield", ["option" => ["col" => "col-md-12 mb-3", "type" => "text", "name" => "lastname", "placeholder" => "Nachname", "required" => false]])
                                                  @include("zeigprofilaward.partials.textfield", ["option" => ["col" => "col-md-12 mb-3", "type" => "email", "name" => "email", "placeholder" => "E-Mail Adresse", "required" => true]])
                                                  @include("zeigprofilaward.partials.textfield", ["option" => ["col" => "col-md-12", "type" => "text", "name" => "company", "placeholder" => "Firmenname", "required" => true]])

                                                  <div class="col-12 py-3">
                                                      <hr>
                                                  </div>

                                                  <div class="col-12 col-md-6 mb-3">
                                                      <div class="form-floating">
                                                          <select class="form-select" id="category" name="category">
                                                              <option value="">--- Bitte wählen ---</option>
                                                              @foreach($categories as $category)
                                                                  <option value="{{$category->id}}" {{ (old("category") == $category->id || $selectedCat == $category->id) ? "selected" : "" }}>{{$category->title}}</option>
                                                              @endforeach
                                                          </select>
                                                          <label for="category">Kategorie*</label>
                                                      </div>
                                                  </div>

                                                  <div class="col-12 col-md-6 mb-3">
                                                      <div class="form-floating">
                                                          <select class="form-select" id="epu" name="epu">
                                                              <option value="">--- Bitte wählen ---</option>
                                                              <option value="J" {{ (old("epu") == "J") ? "selected" : "" }}>Ja</option>
                                                              <option value="N" {{ (old("epu") == "N") ? "selected" : "" }}>Nein</option>
                                                          </select>
                                                          <label for="epu">Ein-Personen-Unternehmen (EPU)*</label>
                                                      </div>
                                                  </div>

                                                  @include("zeigprofilaward.partials.textfield", ["option" => ["col" => "col-md-6 mb-3", "type" => "date", "name" => "founding_date", "placeholder" => "Gründungsdatum", "required" => true]])
                                                  @include("zeigprofilaward.partials.textfield", ["option" => ["col" => "col-md-6 mb-3", "type" => "text", "name" => "founding_place", "placeholder" => "Gründungsort", "required" => true]])
                                                  @include("zeigprofilaward.partials.textfield", ["option" => ["col" => "col-md-6 mb-3", "type" => "text", "name" => "mission_statement", "placeholder" => "Mission Statement", "required" => false]])
                                                  @include("zeigprofilaward.partials.textfield", ["option" => ["col" => "col-md-6 mb-3", "type" => "text", "name" => "claim", "placeholder" => "Claim/Slogan", "required" => false]])
                                                  @include("zeigprofilaward.partials.textfield", ["option" => ["col" => "col-md-6 mb-3", "type" => "text", "name" => "project_goal", "placeholder" => "Projektidee/-ziel", "required" => true]])
                                                  @include("zeigprofilaward.partials.textfield", ["option" => ["col" => "col-md-6 mb-3", "type" => "text", "name" => "credentials", "placeholder" => "Referenzen", "required" => false]])

                                                  <div class="col-12 mb-3">
                                                      <label for="company_logo" class="form-label">Upload Unternehmenslogo*</label>
                                                      <input type="file" class="form-control" id="company_logo" name="company_logo" />
                                                  </div>

                                                  <div class="col-12 mb-3">
                                                      <label for="company_picture" class="form-label">Upload Unternehmensbild</label>
                                                      <input type="file" class="form-control" id="company_picture" name="company_picture">
                                                  </div>

                                                  <div class="col-12 mb-3">
                                                      <label for="company_skizze" class="form-label">Upload Unternehmensskizze*
                                                          <small>(PDF, max. 10MB)</small>
                                                      </label>
                                                      <input type="file" accept="application/pdf" class="form-control" id="company_skizze" name="company_skizze">
                                                  </div>

                                                  <div class="col-12 mb-3">
                                                      <label for="project_picture" class="form-label">Upload Projektbild</label>
                                                      <input type="file" class="form-control" id="project_picture" name="project_picture">
                                                  </div>

                                                  <div class="col-12 py-3">
                                                      <hr>
                                                  </div>

                                                  <div class="text-end">

                                                      <button goodcaptcha="true" class="btn btn-lg btn-danger px-5 fw-bold">Einreichen</button>

                                                  </div>
                                              </div>
                                          @endif
                                      </form>
                                  </div>
                              </div>
                          </div>
                      </div>
                  </div>
              </div>
  </div>
--}}


        </div>
    </div>
@endsection
