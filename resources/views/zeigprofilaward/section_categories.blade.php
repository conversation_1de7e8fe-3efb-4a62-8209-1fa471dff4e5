@foreach($categories as $category)
    @include('zeigprofilaward.partials.modal-category', ['category' => $category])
@endforeach




<style>

</style>


<div class="category-wrapper">
    <div class="container-fluid ">
        <div class="row">
            <div class="col-12 mb-4">
                <div class="h1">Kategorien</div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="sixr-wrapper">
                    <a class="row row-cols-2 {{($phase == 3 && $name != null) ? 'row-cols-lg-6 ' : 'row-cols-lg-6 ' }}">
                        @foreach($categories as $category)
                            @php
                                $overlays = ['architektur', 'demokratisch', 'digital', 'innovation', 'tradition', 'gesundheit'];
                                $overlayMatch = collect($overlays)->first(function($overlay) use ($category) {
                                    return str_contains(strtolower($category->title), $overlay);
                                });
                            @endphp

                           {{-- @if(($category->id == 6 && ($phase == 3 && $name == null)) || ($category->id == 6 && ($phase == 2 || $phase == 1)))
                                @continue
                            @endif--}}
                            @if($phase == 1)
                                <div class="sixr col mb-4 mb-lg-0" data-bs-toggle="modal" data-bs-target="#modal-category-{{$category->id}}">
                                    <div class="sixr-image position-relative">
                                        <img src="{{ $category->imageFullPath }}" class="d-block w-100" />
                                        @if($overlayMatch)
                                            <img src="{{ asset('img/zeigprofilaward/overlay/'.$overlayMatch. '.png') }}"
                                                 alt="Overlay"
                                                 class="overlay-image" />
                                        @endif
                                    </div>


                                    <div class="sixr-title">{!! $category->title === 'Traditionsunternehmen'
                                        ? str_replace('Traditionsunternehmen', 'Traditions-<br>unternehmen', $category->title)
                                        : e($category->title) !!}</div>
                                </div>
                            @elseif($phase == 2 || ($phase == 3 && $name != null))
                                <div class="sixr col mb-4 mb-lg-0 scroll-link" data-target="#{{$category->id}}">
                                    <div class="sixr-image">
                                        <img src="{{ $category->imageFullPath }}" class="d-block w-100" />
                                        @if($overlayMatch)
                                            <img src="{{ asset('img/zeigprofilaward/overlay/'.$overlayMatch. '.png') }}"
                                                 alt="Overlay"
                                                 class="overlay-image" />
                                        @endif
                                    </div>
                                    <div class="sixr-title">{!! $category->title === 'Traditionsunternehmen'
                                        ? str_replace('Traditionsunternehmen', 'Traditions<br>-unternehmen', $category->title)
                                        : e($category->title) !!}</div>
                                </div>
                            @else
                                <div class="sixr col mb-4 mb-lg-0" data-bs-toggle="modal" data-bs-target="#modal-category-{{$category->id}}">
                                    <div class="sixr-image">
                                        <img src="{{ $category->imageFullPath }}" class="d-block w-100" />
                                        @if($overlayMatch)
                                            <img src="{{ asset('img/zeigprofilaward/overlay/'.$overlayMatch. '.png') }}"
                                                 alt="Overlay"
                                                 class="overlay-image" />
                                        @endif
                                    </div>
                                    <div class="sixr-title">{!! $category->title === 'Traditionsunternehmen'
                                        ? str_replace('Traditionsunternehmen', 'Traditions<br>-unternehmen', $category->title)
                                        : e($category->title) !!}</div>
                                </div>
                            @endif
                        @endforeach
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>



<script>
    $(function() {
        // Bei Modal-Show (während der Fade‑Animation)
        $(document).on('show.bs.modal', '.modal', function() {
            var modalId = $(this).attr('id');
            $('.sixr .overlay-image').removeClass('overlay-active');
            $('.sixr[data-bs-target="#' + modalId + '"] .overlay-image')
                .addClass('overlay-active');
        });

        // Beim Schließen wie gehabt
        $(document).on('hidden.bs.modal', '.modal', function() {
            var modalId = $(this).attr('id');
            $('.sixr[data-bs-target="#' + modalId + '"] .overlay-image')
                .removeClass('overlay-active');
        });
    });
</script>
