@extends('tourismuswahl2022.master')

@section('content')
    <div class="row">
        <div class="col-12 text-center mb-5">
            <img src="{{ url("img/tourismuswahl2022/Schriftzug_Tourismuspreis_Noe_2022.png") }}?v2" class="teaser-logo" />
        </div>
        <div class="col-12">
            <p>
                <strong>Die Fachjury hat aus 43 Einreichungen elf Nominierte in den drei Kategorien NACHHALTIGKEIT, DIGITALISIERUNG und TOURISTISCHES GESAMTERLEBNIS gewählt.</strong><br>
                <br>
                Vom 7. bis 20. November läuft das Publikums-Voting für den Tourismuspreis, bei dem heuer erstmals auch der Gast mitentscheiden darf.<br>
                <br>
                <strong>Voten Sie mit und wählen Sie Ihr Lieblingsprojekt aus den 11 Nominierungen.</strong>
            </p>
            <br>
        </div>
    </div>

    @if($beendet)
        <div class="row mt-5">
            <div class="col-12">
                <div class="alert alert-secondary">
                    Das Voting ist beendet.
                </div>
            </div>
        </div>
    @else

        @if(Session::has('message'))
            <div class="row">
                <div class="col-12">
                    <div class="alert alert-success">
                        <h3 class="alert-heading mb-0">{{ Session::get('message') }}</h3>
                    </div>
                </div>
            </div>
        @else
            <form action="{{ url("/") }}" id="tourismusForm" name="tourismusForm" method="post">
                {{ csrf_field() }}


                @foreach($kategorien as $key => $kategorie)
                    <div class="row">
                        <div class="col-12 claim {{ (!$loop->first) ? "mt-5" : "" }}">
                            {{ $kategorie["title"] }}
                            <hr>
                        </div>
                    </div>
                    <div class="row">
                        @foreach($kategorie["projekte"] as $key2 => $value)
                            <div class="col-12 col-md-4 my-3">
                                <div class="card h-100">
                                    <div class="card-body voting-body p-0">
                                        <img src="{{ url("img/tourismuswahl2022/projekte/".$value["image"]) }}" class="d-block w-100">
                                        <div class="bg-{{$key}} mb-3 text-center">
                                            <div class="form-check d-inline-block">
                                                <input class="form-check-input" type="radio" name="kategorie" id="kategorie{{$key}}{{$key2}}" value="{{ $key2 }}" {{ (old("kategorie") == $key2) ? "checked" : "" }}>
                                                <label class="form-check-label" for="kategorie{{$key}}{{$key2}}"></label>
                                            </div>
                                        </div>
                                        <div class="project-title">{!! str_replace(";", "<br>", $value["title"]) !!}</div>
                                        <div class="project-description my-2">{!! $value["intro_description"] !!}</div>
                                    </div>
                                    <div class="card-footer">
                                        <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#modal_{{ $value["slug"] }}">
                                            Mehr über dieses Projekt
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Modal -->
                            <div class="modal fade modal-lg" id="modal_{{ $value["slug"] }}" tabindex="-1">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header border-0 pb-0">
                                            <h1 class="modal-title fs-5">&nbsp;</h1>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body pb-4">
                                            <img src="{{ url("img/tourismuswahl2022/projekte/".$value["image"]) }}" class="d-block w-100 mb-3">
                                            <h4>{!! str_replace(";", "<br>", $value["title"]) !!}</h4>
                                            <div class="project-description my-2">
                                                {!! $value["intro_description"] !!}
                                                {!! $value["description"] !!}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @endforeach

                <div class="row">
                    <div class="col-12 mt-5 mb-4">
                        <div class="bg-light p-4">
                            <div class="row">
                                <div class="col-12 mb-3">
                                    <div class="captcha">
                                        <span>{!! captcha_img("flat_tourismus") !!}</span>
                                        <button type="button" class="btn btn-primary" id="captchaReload" config="flat_tourismus">
                                            &#x21bb;
                                        </button>
                                    </div>
                                </div>
                                <div class="col-12 col-md-6">
                                    <input type="text" name="captcha" id="captcha" class="form-control" placeholder="Sicherheitscode eingeben">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    @if (count($errors) > 0)
                        <div class="col-12 mb-3">
                            <div class="alert alert-danger">
                                <ul class="fa-ul mb-0">
                                    @foreach ($errors->all() as $error)
                                        <li>
                                            <span class="fa-li"><i class="fas fa-exclamation-triangle"></i></span>{!! $error  !!}
                                        </li>
                                    @endforeach
                                </ul>
                            </div>
                        </div>
                    @endif

                    <div class="col-12 mb-5 text-end">
                        <div class="d-grid d-md-block">
                            <button class="btn btn-primary">Voting abschließen</button>
                        </div>
                    </div>
                </div>
            </form>
        @endif
    @endif
@endsection
