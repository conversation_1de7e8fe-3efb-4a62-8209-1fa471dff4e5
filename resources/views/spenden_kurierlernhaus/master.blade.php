<!DOCTYPE html>
<html lang="de">
<head>
    <title>@yield("title") | spenden.kurierlernhaus.at</title>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="_token" content="{!! csrf_token() !!}" />
    <meta http-equiv="Cache-control" content="no-cache">

    <meta name="theme-color" content="#11100B" />

    <link rel="icon" type="image/png" href="img/spenden_kurierlernhaus/favicon-16x16.png" sizes="16x16">
    <link rel="icon" type="image/png" href="img/spenden_kurierlernhaus/favicon-32x32.png" sizes="32x32">

    {{-- FACEBOOK SHARE --}}
    <meta property="og:url" content="https://spenden.kurierlernhaus.at" />
    <meta property="og:type" content="article" />
    <meta property="og:title" content="Jetzt spenden und Kindern Perspektiven schenken!" />
    <meta property="og:description" content="Mit einem positiven Schulabschluss. Mit besseren Chancen für die Zukunft – im KURIER Lernhaus. ➔ jetzt spenden." />
    <meta property="og:image" content="{{ url("img/spenden_kurierlernhaus/header-banner-mobile.png") }}" />

    <!-- CSS-START -->
    <link rel="stylesheet" href="{{ url("inc/fontawesome-pro-6.5.2-min/dist/all.min.css") }}">
    <script src="{{ url("inc/fontawesome-pro-6.5.2-min/dist/all.min.js") }}"  type="text/javascript"></script>

    <link rel="stylesheet" href="{{url('inc/bootstrap-5.2.2/dist/css/bootstrap.min.css')}}">
    <script src="{{url('inc/bootstrap-5.2.2/dist/js/bootstrap.bundle.min.js')}}"></script>
    <link rel="stylesheet" type="text/css" href='{{ url("/inc/css/spenden_kurierlernhaus_fonts.css") }}' />
    <link rel="stylesheet" type="text/css" href='{{ url("/inc/css/spenden_kurierlernhaus.css") }}?v4' />

    <script src="{{url('inc/jquery/jquery.js')}}"></script>
    @include('spenden_kurierlernhaus.didomi')

</head>

<body>
<nav class="navbar navbar-expand-xl bg-danger fixed-top" id="mainNav">
    <div class="container-fluid">
        <a class="navbar-brand" href="{{ url("/") }}">
            <img src="{{ url("img/spenden_kurierlernhaus/header.svg") }}" class="d-block header-logo" />
        </a>
        <button class="navbar-toggler text-white" type="button" data-bs-toggle="collapse" data-bs-target="#navbarResponsive">
            <i class="fas fa-lg fa-bars"></i>
        </button>
        <div class="collapse navbar-collapse" id="navbarResponsive">
            <ul class="navbar-nav ms-auto pl-0">
                <li class="nav-item mx-0 mx-lg-1 d-inline-block pt-0">
                    <a class="nav-link py-3 px-0 px-lg-3 d-block d-lg-inline-block text-center text-lg-start" href="{{ (request()->segment(1) == "cookiepolicy") ? url("/#section_spenden") : "#section_spenden" }}" data-bs-smooth-scroll="smooth-scroll" data-bs-offset="75">Jetzt spenden</a>
                    <a class="nav-link py-3 px-0 px-lg-3 d-block d-lg-inline-block text-center text-lg-start" href="https://kurier.at/thema/lernhaus">KURIER Lernhaus</a>
                </li>
            </ul>
        </div>
    </div>
</nav>

<div class="content-wrapper">
    @yield("content")
</div>
<didomi id="didomicategories" categories='' value=''></didomi>
<div class="container-fluid">
    <div class="row footer-wrapper">
        <div class="col-12">
            <nav class="nav justify-content-center align-items-center my-3">
                <a class="nav-link px-1 px-md-3" href="mailto:<EMAIL>" target="_blank">Kontakt</a>
                /
                <a class="nav-link px-1 px-md-3" href="https://kurier.at/datenschutz" target="_blank">Datenschutz</a>
                /
                <a class="nav-link pr-1 pr-md-3" href="https://kurier.at/info/impressum-kurierat/711895" target="_blank">Impressum</a>
                /
                <a class="nav-link px-1 px-md-3" href="{{ url("cookiepolicy") }}" target="_blank">Cookie Richtlinien</a>
                /
                <a class="nav-link pl-1 pl-md-3 " href="javascript:Didomi.preferences.show()">Cookie Einstellungen</a>

            </nav>
        </div>
    </div>
</div>

<script>
    var baseurl = "{{url('/')}}";
    var mobile = false;

    $(function(){
        if($("#is-xs").css("display") != "none"){
            mobile = true;
        }

        @if(Session::has('message'))
        $(window).on('load', function(){
            $('html, body').animate({ scrollTop : ($(".alert-success").offset().top - 30) });
        });
        @endif
        @if (count($errors) > 0)
        $(window).on('load', function(){
            $('html, body').animate({ scrollTop : ($(".alert-danger").offset().top - 30) });
        });
        @endif
    });
</script>

<!-- ALLGEMEINES JS -->

</body>
</html>

