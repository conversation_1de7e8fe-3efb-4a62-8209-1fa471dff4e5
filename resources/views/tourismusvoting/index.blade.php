@extends('tourismusvoting.master')

@section('content')
	<div class="row">
		<div class="col-12 text-center mb-5">
			<img src="{{ url("inc/tourismusvoting/schriftzug.png") }}?v1" class="teaser-logo" />
		</div>
		<div class="col-12">
			<p>
				<strong>Die Fachjury hat aus 46 Einreichungen neun Nominierte in den drei Kategorien NACHHALTIGKEIT & REGENERATIVER TOURISMUS, DIGITALISIERUNG und TOURISTISCHES GESAMTERLEBNIS gewählt.</strong><br>
				<br>
				Vom 1. bis 17. November läuft das Publikums-Voting für den Tourismuspreis, das heuer bereits zum zweiten Mal stattfindet.<br>
				<br>
				<strong>Voten Sie mit und wählen Sie Ihr Lieblingsprojekt aus jeder Kategorie.</strong>
			</p>
			<br>
		</div>
	</div>

	@if($beendet)
		<div class="row mt-5">
			<div class="col-12">
				<div class="alert alert-secondary">
					Das Voting ist beendet.
				</div>
			</div>
		</div>
	@else

		@if(Session::has('message'))
			<div class="row">
				<div class="col-12">
					<div class="alert alert-success">
						<h3 class="alert-heading mb-0">{{ Session::get('message') }}</h3>
					</div>
				</div>
			</div>
		@else
			<form action="{{ url("/") }}" id="tourismusForm" name="tourismusForm" method="post">
				{{ csrf_field() }}

				@foreach($kategorien as $key => $kategorie)
					<div class="row">
						<div class="col-12 claim {{ (!$loop->first) ? "mt-5" : "" }}">
							{{$key}}. {{ $kategorie["title"] }}
							<hr>
						</div>
					</div>
					<div class="row">
						@foreach($kategorie["projekte"] as $key2 => $value)
							<div class="col-12 col-md-4 my-3">
								<div class="card h-100">
									<div class="card-body voting-body p-0">
										<img src="{{ url("inc/tourismusvoting/projekte/".$value["image"]) }}?v2" class="d-block w-100">
										<div class="bg-{{$key}} mb-3 text-center">
											<div class="form-check d-inline-block">
												<input class="form-check-input" type="radio" name="kategorie{{$key}}" id="kategorie{{$key}}{{$key2}}" value="{{ $key2 }}" {{ (old("kategorie".$key) == $key2) ? "checked" : "" }}>
												<label class="form-check-label" for="kategorie{{$key}}{{$key2}}"></label>
											</div>
										</div>
										<div class="project-title">{!! str_replace(";", "<br>", $value["title"]) !!}</div>
										<div class="project-description my-2">{!! $value["intro_description"] !!}</div>
									</div>
									<div class="card-footer">
										<button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#modal_{{ $value["slug"] }}">
											Mehr über dieses Projekt
										</button>
									</div>
								</div>
							</div>

							<!-- Modal -->
							<div class="modal fade modal-lg" id="modal_{{ $value["slug"] }}" tabindex="-1">
								<div class="modal-dialog">
									<div class="modal-content">
										<div class="modal-header border-0 pb-0">
											<h1 class="modal-title fs-5">&nbsp;</h1>
											<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
										</div>
										<div class="modal-body pb-4">
											<img src="{{ url("inc/tourismusvoting/projekte/".$value["image"]) }}" class="d-block w-100 mb-3">
											<h4>{!! str_replace(";", "<br>", $value["title"]) !!}</h4>
											<div class="project-description my-2">
												{!! $value["intro_description"] !!}
												{!! $value["description"] !!}
											</div>
										</div>
									</div>
								</div>
							</div>
						@endforeach
					</div>
				@endforeach

				<div class="row">
					@if (count($errors) > 0)
						<div class="col-12 mb-3">
							<div class="alert alert-danger">
								<ul class="fa-ul mb-0">
									@foreach ($errors->all() as $error)
										<li>
											<span class="fa-li"><i class="fas fa-exclamation-triangle"></i></span>{!! $error  !!}
										</li>
									@endforeach
								</ul>
							</div>
						</div>
					@endif

					<div class="col-12 my-5 text-end">
						<div class="d-grid d-md-block">
							<button class="btn btn-primary btn-lg" goodcaptcha="true">Voting abschließen</button>
						</div>
					</div>
				</div>
			</form>
		@endif
	@endif
@endsection
