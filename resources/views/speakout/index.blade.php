@extends('speakout.master')

@section('content')

    <section class="d-sm-none d-block">
        @include("speakout.sections.mobile-hero")
    </section>



    <section id="section_intro" class="d-none d-sm-block">
        @include("speakout.sections.section_intro")
    </section>

    <section id="section_info" class="d-none d-sm-block">
        @include("speakout.sections.section_info")
    </section>

    @if($newsArray->count())
        <section id="section_highlights">
            @include("speakout.sections.posts", ['sectionTitle' => "Highlights vor Ort", "allPosts" => $newsArray, "overviewUrl" => "highlights"])
        </section>
    @endif

    <section id="section_speaker" class="d-flex align-items-center">
        @include("speakout.sections.section_speakers")
    </section>

    <section id="section_partner" class="d-flex align-items-center">
        @include("speakout.sections.section_partner")
    </section>

@endsection
