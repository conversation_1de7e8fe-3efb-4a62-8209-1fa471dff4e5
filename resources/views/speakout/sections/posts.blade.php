<style>
    @media (min-width: 1023.98px) {
        .ar-carousel {
            width: 100%;
            height: auto;
            aspect-ratio: 2.5 / 1;
            /* Fallback */
            position: relative;
        }
    }

</style>
<div class="<!--container-fluid--> ">
    <div id="carouselPosts" class="carousel slide" data-bs-ride="carousel">

        <ol class="carousel-indicators">
            @foreach($allPosts as $news)
                <li data-bs-target="#carouselPosts" data-bs-slide-to="{{ $loop->index }}"
                    class="{{ $loop->first ? 'active' : '' }}"></li>
            @endforeach
        </ol>

        <div class="carousel-inner  position-relative ar-carousel" style=" ">
            @foreach($allPosts as $news)
                <div class="carousel-item h-100 position-relative {{ $loop->first ? 'active' : '' }}">

                    <img class="d-block w-100  " src="{{ $news->imageFullPath }}" alt="{{ $news->imageAlt }}">


                    <div class="position-absolute container-fluid top-0 start-0 bottom-0 end-0 text-white p-4 d-small-none d-block">
                        <div class="d-flex align-items-center h-100">

                            <div class="d-lg-flex d-none flex-column w-50 text-shadow-lg position-relative" >
                                <a href="{{ url($overviewUrl. "/" .$news->seo_url) }}" class="stretched-link"></a>
                                <div>
                                    <div class="display-5 fw-bold mb-4">{{ $news->title }}</div>
                                </div>
                                <div>
                                    <div class="lead fs-4 ">{{ $news->pre_text }}</div>
                                </div>
                            </div>




                            <div class="d-lg-none d-block text-shadow-lg position-relative"  >
                                <a href="{{ url($overviewUrl. "/" .$news->seo_url) }}" class="stretched-link"></a>
                                <div>
                                    <div class="display-6 fw-bold mb-1 ">{{ $news->title }}</div>
                                </div>
                                <div class=" ">
                                    <div class="lead   fs-6">{{ $news->pre_text }}</div>
                                </div>
                            </div>


                        </div>

                    </div>

                    <div class="carousel-caption d-small-block d-none">
                        <div class="h2">{{ $news->title }}</div>
                    </div>


                </div>
            @endforeach
        </div>

        <button class="carousel-control-prev" type="button" data-bs-target="#carouselPosts" data-bs-slide="prev">
            <span class="carousel-control-prev-icon" aria-hidden="true"></span>
            <span class="visually-hidden">Vorherige</span>
        </button>
        <button class="carousel-control-next" type="button" data-bs-target="#carouselPosts" data-bs-slide="next">
            <span class="carousel-control-next-icon" aria-hidden="true"></span>
            <span class="visually-hidden">Nächste</span>
        </button>
    </div>

</div>


</div>








