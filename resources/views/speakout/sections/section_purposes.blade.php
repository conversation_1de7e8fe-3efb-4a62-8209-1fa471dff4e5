@foreach(\App\Models\Speakout\SpeakoutR::whereNotNull('title')->get() as $r)

    @include('speakout.partials.modal-rs', [
        'mID' => 'modal-rs-' . $r->id,
        'headerText' => $r->title,
        'text' => $r->text
    ])
@endforeach


<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="section-title mt-4">
                WARUM SPEAK OUT?
            </div>
        </div>
    </div>
</div>
<div class="purposes-wrapper">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="row">
                    @foreach(\App\Models\Speakout\SpeakoutPurpose::all() as $purpose)
                        {{-- BOX --}}
                        <div class="col-lg-6 mb-4">
                            <div class="card" style="min-height: 110px" data-bs-toggle="modal"
                                 data-bs-target="#purpose-modal-{{ $loop->iteration }}">
                                <div class="card-body d-flex justify-content-between">
                                    <div class="purpose-title">{{ $purpose->title }}</div>
                                    <div class="purpose-info">
                                        <i class="fa-solid fa-info-circle"></i>
                                    </div>
                                </div>
                            </div>
                        </div>


                        {{-- MODAL --}}
                        @include('speakout.partials.modal-info', [
                            "mID" => "purpose-modal-$loop->iteration",
                            "headerText" =>$purpose->title ,
                            "data" => $purpose->text
                        ])
                    @endforeach
                </div>
            </div>
      {{--      <div class="col-12 text-center">
                <div class="section-title">
                    Die 6 R's
                </div>
            </div>
            <div class="col-12">
                <div class="sixr-wrapper">
                    <div class="row">
                        @foreach(\App\Models\Speakout\SpeakoutR::whereNotNull('title')->get() as $r)

                            <div class="col-4 col-lg-2 mb-4 mb-lg-0 ">
                                <div
                                        class="sixr"
                                        data-bs-toggle="modal"
                                        data-bs-target="#modal-rs-{{$r->id}}"
                                >
                                    <img src="{{ $r->imageFullPath }}" class="sixr-image"/>
                                    <div class="sixr-title">{{$r->title}}</div>
                                </div>


                            </div>
                        @endforeach

                    </div>
                </div>
            </div>--}}
        </div>
    </div>
</div>
