
<div class="container-fluid ">
    <div class="row py-3  ">
        @foreach($speakers->take(4) as $index => $speaker)
            <div class="col-6 col-lg-3 my-lg-0 my-3 {{$index >= 2 ? "d-none d-lg-block" : ""}}"> {{--col-12 col-lg-3--}}
                @include('speakout.partials.speaker-card', ["speaker" => $speaker])
            </div>
        @endforeach
    </div>
    <div class="row py-4">
        <div class="col-12 d-block d-lg-flex justify-content-between align-items-center mb-5">
            <div class="mb-4 mb-lg-0">
                <a href="{{ url('programm') }}" class="btn big-button">
                    Programm <i class="fa-solid fa-angle-right"></i>
                </a>
            </div>
            <div class="ps-lg-5 lead">
                Speaker:innen aus ganz unterschiedlichen Ecken der Nachhaltigkeit – und direkt vom KURIER. Check das Programm für alle weiteren spannenden Voices!
                <br>

            </div>
        </div>
        <div class="col-12">
            <div class="text-end">
                <a href="{{url('speaker')}}" class="link-primary hover-underline fs-5 fw-bold ">Alle Speakers anzeigen <i class="fa-solid fa-angle-right"></i></a>
            </div>
        </div>

    </div>
</div>

{{--
@foreach($speakers as $speaker)


        @include('speakout.partials.modal-speaker', [
           "mID" => "speaker-modal-$loop->iteration",
           "data" => $speaker
       ])

    --}}
{{-- MODAL --}}{{--





@endforeach

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="section-title text-white">Speaker:Innen</div>
        </div>
    </div>
</div>

<div class="slider-wrapper">



    <div class="container-fluid">



        <div class="row">
            <div class="col-12">
                <div class="splide px-5">
                    <div class="splide__track ">
                        <ul class="splide__list">
                            @foreach($speakers as $speaker)
                                --}}
{{-- BOX --}}{{--

                                <li class="splide__slide" title="&copy; {{ $speaker->imageCopyright }}" style="cursor:pointer;">
                                    <div class="splide__slide__container">
                                        <div class="card h-100" data-bs-toggle="modal"
                                             data-bs-target="#speaker-modal-{{ $loop->iteration }}">
                                            <img src="{{ $speaker->imageFullPath }}" class="d-block w-100 slider-image"
                                                 alt="{{ $speaker->name}} | &copy; {{ $speaker->imageCopyright }}"/>
                                            <div class="card-body">
                                                <div class="slider-title">{{ $speaker->name }}</div>
                                                <div class="slider-text">{{ $speaker->shortDescription }}</div>
                                            </div>
                                        </div>
                                    </div>
                                </li>

                            @endforeach
                        </ul>
                    </div>
                </div>
            </div>
        </div>


        <div class="col-12 text-end">
            <a href="{{ url("speaker") }}" target="_blank" class="btn-to-overview">
                Zur Übersicht <i class="fa-solid fa-angle-right"></i>
            </a>
        </div>
    </div>
</div>



<script>
    const sliderOptions = {
        type: 'slide',
        gap: '1rem',
        perPage: 5,
        perMove: 5,

                i18n: {
                    prev: 'Previous Speaker',
                    next: 'Next Speaker',
                },
        breakpoints: {
            640: {
                perPage: 1,
                perMove: 1
            },
        },
        pagination: false
    }

    const slider = document.querySelector(".splide");

    if (slider) {
        const splide = new Splide(slider, sliderOptions).mount();
    }
</script>
--}}
