@extends('speakout.master')

@section('content')
    <section class="page-section bg-yellow-gradient">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12 my-5">
                    <h1 class="section-title mb-0">Speaker:<PERSON><PERSON></h1>
                </div>
                @foreach($speakers as $speaker)

                    {{-- MODAL --}}


                    @include('speakout.partials.modal-speaker', [
                        "mID" => "speaker-modal-$loop->iteration",
                        "data" => $speaker
                    ])




                    <div class="col-12 col-sm-6 col-lg-4 col-xl-3 mb-5">


                        @include('speakout.partials.speaker-card', ["speaker" => $speaker])




                    </div>
                @endforeach
            </div>
        </div>
    </section>

    @if($selectedSpeaker)
        <script>
            const selectedSpeaker = `{{$selectedSpeaker}}`
            const selectSpeakerTrigger = document.querySelector(`[data-slug-name="${selectedSpeaker}"]`)
            if (selectSpeakerTrigger) {
                selectSpeakerTrigger.click();
            }
            /**/
            document.body.addEventListener('hidden.bs.modal', () => {
                let currentUrl = window.location.href.replace(selectedSpeaker, '');
                let url = new URL(currentUrl);
                history.pushState({}, '', url);
            })
        </script>
    @endif

@endsection
