@php use App\Models\Speakout\SpeakoutProgram;use App\Models\Speakout\SpeakoutSpeaker; @endphp
@extends('speakout.master')

@section('content')

    @foreach( SpeakoutSpeaker::getWholeSpeakersList() as $speaker)
        @include('speakout.partials.modal-speaker', [
                "mID" => "speaker-modal-$speaker->id",
                "data" => $speaker
                ])
    @endforeach

    @foreach($programs as $program)
        @include('speakout.partials.modal-program-event', ["mID" => "program-modal-$program->id", "data" => $program])
    @endforeach

    <section id="section_programm">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <nav class="d-flex justify-content-lg-center py-5 overflow-auto">
                        <button class="btn btn-orange text-white  me-3" data-room-selector="">
                            Alle
                        </button>
                        @foreach($rooms as $room)
                            <button class="btn text-white btn-light-orange me-3 now text-nowrap" data-room-selector="{{$room['id']}}">{{$room['name']}}</button>
                        @endforeach
                    </nav>


                    <main class="overflow-hidden">
                        @foreach($programs as $program)

                            <section data-room="{{$program->room_id}}" class="shadow mb-3  bg-white program-card  d-flex py-3 data-room ">
                                <aside class="program-card--left-panel border-end border-orange border-2 px-3 d-none d-lg-block">
                                    <div class="d-flex flex-column ">
                                        <p class="h3">
                                            {{$program->timeRangeString}}
                                        </p>
                                        <p>
                                            <span class="badge text-bg-orange text-white">{{$program->roomName}}</span>
                                            <span class="d-block mt-2">
                                    {{$program->roomSub}}
                                </span>
                                        </p>
                                    </div>
                                </aside>
                                <article class="program-card--right-panel px-3 w-100">
                                    <aside class="d-lg-none">
                                        <p class="h3">
                                            {{$program->timeRangeString}}
                                        </p>
                                        <p>
                                            <span class="badge  text-bg-orange text-white">{{$program->roomName}}</span>
                                            <span class="d-block mt-2">
                                    {{$program->roomSub}}
                                </span>
                                        </p>
                                    </aside>

                                    <h3>
                                        {{$program->title}}
                                    </h3>
                                    <p>
                                        {{$program->description}}
                                    </p>
                                    @if($program->speakers->count())
                                        <div class="d-flex justify-content-lg-start flex-column flex-lg-row pt-3 ">
                                            @foreach($program->speakers as $s)

                                                {{-- MODAL --}}

                                                <div
                                                    {{--      data-program-id="{{$s->id}}"--}}
                                                    class="mb-lg-0 @if(!$loop->last) mb-3 @endif me-3 borders border-dark d-flex program-speaker"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#speaker-modal-{{ $s->id }}">
                                                    <div>
                                                        <img src="{{$s->imageFullPath}}" alt="Pic: {{$s->name}}" width="55px">
                                                    </div>
                                                    <section class="ps-2 ">
                                                        <h5>
                                                            {{$s->name}}
                                                        </h5>

                                                        <small class="d-lg-block" style="max-width: 180px;">
                                                      {{$s->shortDescription}}
                                                        </small>
                                                    </section>
                                                </div>
                                            @endforeach
                                        </div>
                                    @endif
                                    <div class="text-end">
                                        <div
                                            data-program-id="{{$program->id}}"
                                            class="btn btn-outline-orange fw-bold white-on-hover"
                                            data-bs-toggle="modal"
                                            data-bs-target="#program-modal-{{$program->id}}"
                                        >
                                            Weitere Infos <i class="fa-duotone fa-circle-info"></i>
                                        </div>
                                    </div>
                                </article>
                            </section>

                        @endforeach
                    </main>
                </div>
            </div>
        </div>
    </section>

    <script>
        const roomSelectors = document.querySelectorAll('[data-room-selector]');
        const dataRoom = document.querySelectorAll('[data-room]');

        function filterProgram(key){
            // Empty string
            if(!key){
                key = null;
            }
            // Hide all
            dataRoom.forEach(x => {

                x.classList.remove('data-room--slide')

                setTimeout(() => {
                    x.classList.add('data-room--slide')

                    x.classList.add('d-none')
                    // show selected or all
                    dataRoom.forEach(x => {
                        if(x.dataset.room === key || key === null){
                            x.classList.remove('d-none')
                        }
                    })
                }, 0)


            })


        }

        roomSelectors.forEach(button => {
            button.onclick = () => {
                roomSelectors.forEach(x => {
                    if(x === button){
                        x.classList.add('btn-orange')
                        x.classList.remove('btn-light-orange')
                    }else{
                        x.classList.remove('btn-orange')
                        x.classList.add('btn-light-orange')
                    }
                })
                filterProgram(button.dataset.roomSelector)
            }

            if(button.dataset.roomSelector === "1"){
                button.click()
            }

        })

        @if($selectedProgram instanceof  SpeakoutProgram)
        const selectedProgram = `{{$selectedProgram->id}}`

        const selectProgramTrigger = document.querySelector(`[data-program-id="${selectedProgram}"]`)

        if(selectProgramTrigger){
            selectProgramTrigger.click();
        }
        /**/
        document.body.addEventListener('hidden.bs.modal', () => {

            let currentUrl = window.location.href.replace(`programm/${selectedProgram}`, 'programm');
            let url = new URL(currentUrl);
            history.pushState({}, '', url);
        })

        @endif

        function copyToClipboard(text){
            navigator.clipboard.writeText(text);
        }

    </script>

@endsection
