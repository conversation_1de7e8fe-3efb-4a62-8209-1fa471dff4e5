@php
    use App\Models\Speakout\SpeakoutHomepage;
    $shareImage = SpeakoutHomepage::logo();
@endphp

<meta property="og:url" content="{{url()->current()}}"/>
<meta property="og:type" content="article"/>
<meta property="og:image" content="{{ $shareImage }}"/>
<meta property="og:site_name" content="speakout.futurezone.at">
@if(request()->segment(1) === "programm" && !empty($selectedProgram) )

    <!-- Open Graph / Facebook Meta Tags -->
    <meta property="og:title" content="{{$selectedProgram->title}}">
    <meta property="og:description" content="{{$selectedProgram->description}}">


    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="{{$shareImage}}">
    <meta name="twitter:title" content="{{$selectedProgram->title}}">
    <meta name="twitter:description" content="{{$selectedProgram->description}}">
    <meta name="twitter:image" content="{{$shareImage}}">

    <!-- Additional Meta Tags -->
    <link rel="canonical" href="{{url()->current()}}">
@else
    <meta property="og:title" content="speakout.futurezone.at"/>
@endif
