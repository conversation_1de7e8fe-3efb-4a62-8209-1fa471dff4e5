@php use App\Models\Speakout\SpeakoutSpeaker; @endphp
<div class="modal modal-xl fade" id="{{ $mID }}" data-bs-backdrop="static"
     tabindex="-1" aria-hidden="true">
    <div class="modal-dialog {{--modal-dialog-scrollable--}} modal-dialog-centered">
        <div class="modal-content">


            <div class="modal-body">
                <div class="d-flex justify-content-between mb-3 mb-lg-0">
                    <div class="fw-bold fs-1 mb-0">{{ $speaker->name }}</div>
                    <div class="">
                        <button type="button" class="btn-close  " data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                </div>


                @if($speaker instanceof SpeakoutSpeaker)

                    <div class="row flex-column-reverse flex-lg-row">

                        <div class="col-lg-8 d-flex flex-column justify-content-between">
                            <div>
                                <div class="speaker-function mb-lg-5 mb-3  fs-1 text-lightgreen fw-bold">{{ $speaker->function }}</div>

                                <div class="speaker-company mb-3  text-lightgreen ">{{ $speaker->shortDescription  }}</div>

                                <div class="speaker-biography mb-3">{!! $speaker->biography !!}</div>
                            </div>


                            @if(!empty($speaker->socialMediaArray))
                                <div class="d-flex">
                                    @foreach($speaker->socialMediaArray as $social)
                                        <a href="{{ $social->url }}" target="_blank" class="me-3 text-decoration-none">
                                            <div
                                                    class="position-relative text-white d-flex justify-content-center align-items-center p-3"
                                                    style="border-radius: 100%; height: 4rem; width: 4rem; background-color: #0a0a0a">
                                                @switch($social->type)
                                                    @case("website")
                                                        <i class="fa-solid fa-desktop fa-fw fa-2x "></i>
                                                        @break
                                                    @case("linkedin")
                                                        <i class="fa-brands fa-linkedin fa-fw fa-2x "></i>
                                                        @break
                                                    @case("twitter")
                                                        <i class="fa-brands fa-x-twitter fa-fw fa-2x "></i>
                                                        @break
                                                    @case("facebook")
                                                        <i class="fa-brands fa-square-facebook fa-fw fa-2x "></i>
                                                        @break
                                                    @case("instagram")
                                                        <i class="fa-brands fa-square-instagram fa-fw fa-2x "></i>
                                                        @break
                                                @endswitch
                                            </div>

                                        </a>
                                    @endforeach
                                </div>

                            @endif

                        </div>

                        <div class="col-lg-4">
                            <img src="{{ $speaker->imageFullPath }}" class="d-block w-100 mb-2"
                                 alt="{{ $speaker->name }}"/>
                            <div class="small w-100 text-end mb-3">&copy; {{ $speaker->imageCopyright }}</div>
                        </div>
                    </div>

                @endif

            </div>


        </div>
    </div>
</div>


