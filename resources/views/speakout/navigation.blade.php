@php use App\Models\Speakout\SpeakoutHomepage; @endphp
<div class="header-wrapper fixed-top   {{!empty($animateHeader) ? " header-style " : " header-wrapper--scrolled "}}" >
    <nav class="navbar navbar-expand-xl" id="mainNav">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url('/') }}">
                <img src="{{ SpeakoutHomepage::logo() }}" class="d-block" alt="logo" />
            </a>
            <button class="navbar-toggler text-white" type="button" data-bs-toggle="collapse"
                    data-bs-target="#navbarResponsive" aria-controls="navbarResponsive" aria-expanded="false"
                    aria-label="Toggle navigation">
                <i class="fas fa-lg fa-bars"></i>
            </button>
            <div class="collapse navbar-collapse" id="navbarResponsive">
                <ul class="navbar-nav ms-auto">
                    <!-- Programm -->
                    <li class="nav-item mx-0 mx-lg-1">
                        <a class="page-scroll nav-link d-block d-xl-inline-block text-center text-lg-start" href="{{ url('/programm') }}">PROGRAMM</a>
                    </li>

                    <!-- Speaker:innen -->
                    <li class="nav-item mx-0 mx-lg-1">
                        <a class="page-scroll nav-link d-block d-xl-inline-block text-center text-lg-start"
                           href="{{ empty(request()->segment(1)) ? '#section_speaker' : url('/') . '#section_speaker' }}"
                           id="navi_speaker">SPEAKER:INNEN</a>
                    </li>

                    <!-- Galerie Dropdown -->
                    <li class="nav-item dropdown mx-0 mx-lg-1">
                        <a class="nav-link dropdown-toggle d-block d-xl-inline-block text-center text-lg-start" href="#" role="button"
                           data-bs-toggle="dropdown" aria-expanded="false">
                            GALERIE
                        </a>
                        <ul class="dropdown-menu mb-3" >
                            <li><a class="dropdown-item" href="{{ url('impressionen-video') }}">VIDEO</a></li>
                            <li><a class="dropdown-item" href="{{ url('impressionen-fotogalerie') }}">FOTOGALERIE</a></li>
                        </ul>
                    </li>

                    <!-- Tickets -->
                    <li class="nav-item mx-0 mx-lg-1">
                        <a class="page-scroll nav-link d-block d-xl-inline-block text-center text-lg-start special-btn"
                           href="{{ SpeakoutHomepage::ticketLink() }}" target="_blank">TICKETS</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

</div>
<div class="navbar-placeholder"></div>


@section('scripts')
    <script>
        document.addEventListener("DOMContentLoaded", function () {

            const animatedHeader = document.querySelector(".header-style");

            if(!animatedHeader) return;

            window.addEventListener("scroll", function () {
                if (window.scrollY > 50) {
                    animatedHeader.classList.add("header-wrapper--scrolled");

                } else {

                    animatedHeader.classList.remove("header-wrapper--scrolled");
                }
            });
        })
    </script>
@endsection

