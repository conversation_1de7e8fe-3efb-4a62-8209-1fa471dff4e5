


@extends('speakout.master')

@section('content')
    <div id="section_impressionen">
        <div class="container-fluid">




            <div class="row">
                <div class="col-12">
                    <h1 class="my-5 section-title">Fotogalerie</h1>
                </div>
                @foreach($images as $index => $img)
                    <div class="col-lg-3 col-sm-6 mb-4">
                        <div class="card h-100 shadow-sm border-0">
                            <div class="splide-thumb" data-index="{{ $index }}" style="cursor: pointer;">
                                <img src="{{ $img->fullPath }}" alt="{{ $img->title }}" class="img-fluid">
                                <div class="p-3  bg-white">{{ $img->title }}</div>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>



            <div id="fullscreen-carousel-wrapper"
                 style="display:none; position:fixed; inset:0; background:#000; z-index:9999;"
                 class="px-3 ">
                <div id="fullscreen-carousel" class="splide" style="height: 97%">
                    <div class="splide__track h-100">
                        <ul class="splide__list h-100">
                            @foreach($images as $img)
                                <li class="splide__slide d-flex justify-content-center align-items-center   h-100">

                            {{--        <div class="h-100 pt-3 position-relative">
                                        <img src="{{ $img->fullPath }}" alt="{{ $img->title }}" class="h-100"  >
                                        <div class="position-absolute bottom-0 start-0 end-0" style="background-color: rgba(10,10,10,0.51)">
                                            <div class="p-2 text-white fs-5 text-center">{{ $img->title }}</div>
                                        </div>


                                    </div>--}}


                                    <div class="position-relative d-flex align-content-center">
                                        <div class="position-relative bg-warning">
                                            <img src="{{ $img->fullPath }}" alt="{{ $img->title }}" class="w-100" {{--style="max-height: 100vh; object-fit: contain;"--}}>
                                            <div class="position-absolute bottom-0 start-0 end-0" style="background-color: rgba(10,10,10,0.51)">
                                                <div class="p-2 text-white fs-5 text-center">{{ $img->title }}</div>
                                            </div>
                                        </div>

                                    </div>


                                </li>
                            @endforeach
                        </ul>
                    </div>
                </div>
                <button id="close-carousel" class="btn btn-light position-absolute top-0 end-0 m-3 fs-3 rounded-5 fs-3 p-0" style="width: 3rem; height: 3rem">
                    &times;
                </button>
            </div>

        </div>
    </div>



    <style>
        .card {
            overflow: hidden;
        }
        .card img {
            transition: 0.4s ease-out;
        }
        .card:hover img {
            transform: scale(1.04);
        }
    </style>



    <script>
        let splideFullscreen;

        document.addEventListener('DOMContentLoaded', function () {
            const wrapper = document.getElementById('fullscreen-carousel-wrapper');
            const closeBtn = document.getElementById('close-carousel');
            const thumbs = document.querySelectorAll('.splide-thumb');

            // Mount fullscreen Splide only once
            splideFullscreen = new Splide('#fullscreen-carousel', {
                type: 'loop',
                arrows: true,
                pagination: false,
                height: '100vh',
            }).mount();

            // Open carousel at clicked index
            thumbs.forEach((thumb, index) => {
                thumb.addEventListener('click', () => {
                    wrapper.style.display = 'block';
                    splideFullscreen.go(index);
                });
            });

            // Close handler
            closeBtn.addEventListener('click', () => {
                wrapper.style.display = 'none';
            });



            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') {
                    wrapper.style.display = 'none';
                } else if (e.key === 'ArrowRight') {
                    splideFullscreen.go('>');
                } else if (e.key === 'ArrowLeft') {
                    splideFullscreen.go('<');
                }
            });

        });
    </script>
@endsection

