@extends('frontend.master')

@section('content')
    <div class="container">
        <div class="row">
            <div class="col-12">
                <img src="{{ url("img/eventanmeldung/".$page.".jpg?v1") }}" class="d-none d-lg-block w-100">
                <img src="{{ url("img/eventanmeldung/mobile/".$page.".jpg?v1") }}" class="d-block d-lg-none w-100">
            </div>
            <div class="col-12 mt-3">
                <h1 class="my-3 fw-normal title-smaller">{{ $title }}</h1>
                @if(!empty($description))
                    <p>{{ $description }}</p>
                @endif
            </div>
        </div>


        @if(!empty($registration) && empty($editable))
            <div class="row">
                <div class="col-12 col-md-6">
                    <div class="alert alert-success">
                        <h5 class="mb-0">Sie sind bereits angemeldet</h5>
                    </div>
                </div>
            </div>
        @else
            @if(!empty($registration))
                <div class="row">
                    <div class="col-12 col-md-6">
                        <div class="alert alert-success">
                            <h5>Sie sind bereits mit folgenden Daten angemeldet:</h5>
                            <b>Vorname:</b> {{ $registration->firstname }}<br>
                            <b>Nachname:</b> {{ $registration->lastname }}<br>
                            <b>E-Mail:</b> {{ $registration->email }}
                            @if(!empty($bustransfer))
                                <br>
                                <b>Bustransfer:</b> {{ ($registration->bustransfer) ? $registration->bustransfer : "Kein Transfer benötigt" }}
                            @endif
                        </div>
                    </div>
                </div>
            @endif

            <form action="{{ url("anmeldung/".$page) }}" id="anmeldeform" name="anmeldeform" method="post">

                <input type="hidden" name="event" value="{{$page}}" />
                {{ csrf_field() }}

                <div class="row">
                    <div class="col-12 col-md-6 mt-3 mb-5">
                        <div class="row">
                            <div class="col-12">
                                @include('messages.error')
                                @include('messages.success')
                            </div>
                            <div class="col-12 mb-3">
                                <label for="firstname">Vorname*</label>
                                <input type="text" id="firstname" name="firstname" class="form-control" value="{{ old('firstname', $person['firstname']) }}">
                            </div>
                            <div class="col-12 mb-3">
                                <label for="lastname">Nachname*</label>
                                <input type="text" id="lastname" name="lastname" class="form-control" value="{{ old('lastname', $person['lastname']) }}">
                            </div>

                            <div class="col-12 mb-3">
                                <label for="email">E-Mail</label>
                                <input type="email" id="email" name="email" class="form-control" value="{{  old('email', $person['email'])  }}" {{ (!empty($person['email'])) ? "readonly" : "" }}>
                            </div>

                            @if(!empty($bustransfer))
                                <div class="col-12 mb-3">
                                    <label for="bustransfer">Bustransfer
                                        <small>(wenn gewünscht bitte auswählen)</small>
                                    </label>
                                    <select id="bustransfer" name="bustransfer" class="custom-select">
                                        <option value="" {{ (old("bustransfer", $person['bustransfer']) == '') ? 'selected' : '' }}></option>
                                        {{--<option value="W19" {{ (old("bustransfer", $person['bustransfer']) == 'W19') ? 'selected' : '' }}>Wien 19</option>--}}
                                        <option value="Innsbruck" {{ (old("bustransfer", $person['bustransfer']) == 'Innsbruck') ? 'selected' : '' }}>Innsbruck</option>
                                        <option value="Salzburg" {{ (old("bustransfer", $person['bustransfer']) == 'Salzburg') ? 'selected' : '' }}>Salzburg</option>
                                        <option value="Linz" {{ (old("bustransfer", $person['bustransfer']) == 'Linz') ? 'selected' : '' }}>Linz</option>
                                        <option value="Klagenfurt" {{ (old("bustransfer", $person['bustransfer']) == 'Klagenfurt') ? 'selected' : '' }}>Klagenfurt</option>
                                        <option value="St. Andrä" {{ (old("bustransfer", $person['bustransfer']) == 'St. Andrä') ? 'selected' : '' }}>St. Andrä</option>
                                        <option value="Graz" {{ (old("bustransfer", $person['bustransfer']) == 'Graz') ? 'selected' : '' }}>Graz</option>
                                    </select>
                                </div>
                            @endif

                            <div class="col-12">
                                <button type="submit" class="btn btn-brown mb-3 d-block">Anmeldung absenden</button>
                                <small>* sind Pflichtfelder</small>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        @endif
    </div>
@endsection
