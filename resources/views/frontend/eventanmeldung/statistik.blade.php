@extends('frontend.master')

@section('content')

    <div class="container">
        <div class="row">
            <div class="col-12">
                <img src="{{ url("img/eventanmeldung/".$page.".jpg") }}" class="d-none d-lg-block w-100">
                <img src="{{ url("img/eventanmeldung/mobile/".$page.".jpg") }}" class="d-block d-lg-none w-100">
            </div>
            <div class="col-12 mt-3">
                <h1 class="my-3 fw-normal title-smaller">{{ $title }}</h1>
                @if(!empty($description))
                    <p>{{ $description }}</p>
                @endif
            </div>
        </div>
        <div class="row">
            <div class="col-12">
                <table class="table table-hover table-sm pb-5">
                    <thead>
                    <tr>
                        <th scope="col">Transfer</th>
                        <th scope="col">Personen</th>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach($raw as $land)
                        <tr>
                            <td>{{ ($land->bustransfer) ? $land->bustransfer : 'Kein Transfer benötigt'  }}</td>
                            <td>{{ $land->anzahl }}</td>
                        </tr>
                    @endforeach
                    <tr>
                        <th scope="col">Gesamt</th>
                        <th scope="col">{{ $gesamt[0]->anzahl }}</th>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="row">
            <div class="col-12">
                <br>
                <h2>Anmeldungen</h2>
                <br>
                <table class="table table-hover table-sm pb-5">
                    <thead>
                    <tr>
                        <th scope="col">#</th>
                        <th scope="col">Nachname</th>
                        <th scope="col">Vorname</th>
                        <th scope="col">Email</th>
                        <th scope="col">Bustransfer</th>
                    </tr>
                    </thead>
                    @if(!empty($anmeldungen) && count($anmeldungen) > 0)
                        <tbody>
                        @foreach($anmeldungen as $anmeldung)
                            <tr>
                                <th>{{ $loop->iteration }}</th>
                                <td>{{ $anmeldung->lastname }}</td>
                                <td>{{ $anmeldung->firstname }}</td>
                                <td>{{ $anmeldung->email }}</td>
                                <td>{{ ($anmeldung->bustransfer) ? $anmeldung->bustransfer : 'Kein Transfer benötigt'  }}</td>
                            </tr>
                        @endforeach
                        </tbody>
                    @endif
                </table>
            </div>
        </div>
    </div>
@endsection
