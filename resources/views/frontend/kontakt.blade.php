@extends('frontend.master')
@section('content')
    <div class="container">
        <section class="mb-5">
            <h1 class="py-3 mt-4 fw-normal title-smaller">KONTAKT</h1>
            <p class=" py-2">
                Sie interessieren sich für unsere Produkte oder Dienstleistungen und haben Fragen an uns? Hier finden Sie die Ansprechpartner der jeweiligen
                Bereiche. Unsere Mitarbeiter freuen sich, mit Ihnen ins Gespräch zu kommen und beantworten gerne Ihre Anfrage.
            </p>
        </section>
    </div>

    <div class="bg-brown1 py-4">   <!-- // brown row-->
        <div class="container">
            <div class=" row">
                <div class="col-12">
                    <div class="h3 pb-2 fw-bold py-4">Fragen zu Ihrem Abo</div>
                </div>
                <div class="col-lg-6 mt-2 mb-4">
                    <p class="m-0 fw-bold">Krone-Abo</p>
                    <p class="mb-0">Bei Fragen zu Ihrem Krone-Abo</p>
                    <p>wenden Sie sich bitte an unser Kundenservice;</p>
                    <p class="mb-0">
                        E-Mail:
                        <a href="mailto:<EMAIL>" class="text-danger"><EMAIL></a>
                    </p>
                    <p class="m-0">
                        <a href="tel:+43 (0)5 7060-600">Tel.: +43 (0)5 7060-600</a>
                    </p>
                </div>

                <div class="col-lg-6 mt-2 mb-4">
                    <p class="m-0 fw-bold">KURIER-Abo</p>
                    <p class="mb-0">Bei Fragen zu Ihrem KURIER-Abo</p>
                    <p>wenden Sie sich bitte an unser Kundenservice;</p>
                    <p class="mb-0">E-Mail:
                        <a href="mailto:<EMAIL>" class="text-danger"><EMAIL>
                        </a>
                    </p>
                    <p class="m-0">
                        <a href="tel:+43 (0)5 9030-600">Tel.: +43 (0)5 9030-600</a>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="row py-4">
            <div class="col-12">
                <div class="h3 pb-2 fw-bold py-4">Kontaktformular</div>
            </div>
            @if(Session::has('message'))
                <div class="col-12">
                    @include("messages.success")
                </div>
            @else
                <div class="col-12 col-md-10">
                    <form action="{{ url("kontakt") }}" id="kontaktForm" name="kontaktForm" method="post">
                        {{ csrf_field() }}

                        <input type="hidden" id="email" name="email" value="{{ old("email") }}" />

                        <div class="row">

                            @if (count($errors) > 0)
                                <div class="col-12">
                                    @include("messages.error")
                                </div>
                            @endif

                            <div class="col-12 col-md-6 mb-3">
                                <label for="firstname">Vorname *</label>
                                <input type="text" class="form-control" id="firstname" name="firstname" value="{{ old("firstname") }}" />
                                {!!  ($errors->has("firstname")) ? "<small class='form-text text-danger'><span class='fas fa-exclamation-circle'></span> ".join('<br/>', $errors->get('firstname'))."</small>" : "" !!}
                            </div>
                            <div class="col-12 col-md-6 mb-3">
                                <label for="lastname">Nachname *</label>
                                <input type="text" class="form-control" id="lastname" name="lastname" value="{{ old("lastname") }}" />
                                {!!  ($errors->has("lastname")) ? "<small class='form-text text-danger'><span class='fas fa-exclamation-circle'></span> ".join('<br/>', $errors->get('lastname'))."</small>" : "" !!}
                            </div>
                            <div class="col-12 col-md-6 mb-3">
                                <label for="email2">E-Mail Adresse *</label>
                                <input type="email" class="form-control" id="email2" name="email2" value="{{ old("email2") }}" />
                                {!!  ($errors->has("email2")) ? "<small class='form-text text-danger'><span class='fas fa-exclamation-circle'></span> ".join('<br/>', $errors->get('email2'))."</small>" : "" !!}
                            </div>
                            <div class="col-12 col-md-6 mb-3">
                                <label for="phone">Telefonnummer *</label>
                                <input type="text" class="form-control" id="phone" name="phone" value="{{ old("phone") }}" />
                                {!!  ($errors->has("phone")) ? "<small class='form-text text-danger'><span class='fas fa-exclamation-circle'></span> ".join('<br/>', $errors->get('phone'))."</small>" : "" !!}
                            </div>
                            <div class="col-12 mb-3">
                                <label for="area">Bereich wählen *</label>
                                <select class="form-select" id="area" name="area">
                                    <option value=""></option>
                                    @foreach(config("frontend.kontakt") as $key => $value)
                                        <option value="{{ $key }}" {{ (old("area")==$key) ? "selected" : "" }}>{{ $value["title"] }}</option>
                                    @endforeach
                                </select>
                                {!!  ($errors->has("area")) ? "<small class='form-text text-danger'><span class='fas fa-exclamation-circle'></span> ".join('<br/>', $errors->get('area'))."</small>" : "" !!}
                            </div>
                            <div class="col-12 mb-3">
                                <label for="message">Anliegen *</label>
                                <textarea class="form-control" id="message" name="message" rows="7" limit="1000">{{ old("message") }}</textarea>
                                {!!  ($errors->has("message")) ? "<small class='form-text text-danger'><span class='fas fa-exclamation-circle'></span> ".join('<br/>', $errors->get('message'))."</small>" : "" !!}
                            </div>
                            <div class="col-12 text-end">
                                <button goodcaptcha="true" class="btn btn-success">Anfrage senden</button>
                            </div>
                        </div>
                    </form>
                </div>
            @endif
        </div>
    </div>

@endsection
