@extends('frontend.master')
@section('content')

    <div class="container">
        <div class="row mb-4">
            <section class="col-12">
                <h1 class=" fw-normal  py-3 title-smaller">MARKENPORTFOLIO</h1>
                <p class="">
                    Unsere Leidenschaft gilt unseren Markenwelten. Mit der Kronen Zeitung als Österreichs leserstärkster
                    Tageszeitung und dem KURIER als
                    führende
                    Qualitätszeitung des Landes prägt MediaPrint erfolgreich Österreichs Medienlandschaft und bietet
                    hervorragende Service-Leistungen für Leser
                    und
                    B2B-Kunden –
                    in Print und digital.
                </p>
            </section>

            <div class="col-12">
                <div class="d-flex justify-content-start flex-wrap">
                    @foreach($tags->sortBy('name') as $tag )
                        <div>
                            <div class=" my-1 me-2">
                                <div
                                    style="min-width:5rem"
                                    class="btn btn-brown filters " id="{{$tag->name}}"
                                    data-btn-tag="{{$tag->id}}" label="{{$tag->name}}">
                                    <span class="never-wrap marken-btns p-0"> {{ $tag->title }}</span>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>

        <div class="row" id="boxes-container">
            @foreach($brands as $brand)
                <div class="col-6 col-lg-4 mb-3 brand-card" data-all-tags="@foreach($brand->getTags as $tag){{$tag->id}},@endforeach">
                    <div class="card h-100">
                        <div class="card-body d-flex align-items-center justify-content-center">
                            <a href="{{url($brand->url)}}">
                                <img src="{{ asset("/storage/app/".$brand->path)}}" class="w-100" alt="Logo {{ $brand->title }}">
                            </a>
                        </div>

                    </div>
                </div>
            @endforeach
        </div>
    </div>

    <script>
        const buttons = document.querySelectorAll(`[data-btn-tag]`);
        const cards = Array.from(document.querySelectorAll('[data-all-tags]'));
        let lastActiveButton = null;
        buttons.forEach((btn) => {

            btn.onclick = () => {

                if(lastActiveButton === btn.dataset.btnTag){
                    btn.classList.remove('btn-active')
                    lastActiveButton = null;
                    cards.forEach(x => x.classList.remove('d-none'));
                    return;
                }

                lastActiveButton = btn.dataset.btnTag;

                buttons.forEach(x => x.classList.remove('btn-active'))
                btn.classList.add('btn-active')

                cards.forEach(x => x.classList.add('d-none'))

                const tag = btn.dataset.btnTag;
                const selected = cards.filter(x => x.dataset.allTags.includes(tag))
                selected.forEach(x => x.classList.remove('d-none'))
            }
        })
    </script>
@endsection
