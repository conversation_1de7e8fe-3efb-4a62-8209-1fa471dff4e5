@extends('frontend.master')

{{-- @section('javascripts') @endsection--}}

@section('content')

    <div class="container">
        <div class="row">

                    <div class="col-12">
                        <div>
                            <br>
                            <h1>{!!  $ueberschrift!!}</h1><br>
                            {!!  $text !!}<br>
                            <br>
                        </div>
                    </div>
                    @if(Session::has("message"))
                        <div class="col-12 ">
                            @include("messages.success")
                        </div>
                    @else
                        <form class="w-100" action="{{ url('widerruf') }}" method="post" enctype="multipart/form-data">
                            {{ csrf_field() }}
                            <input type="hidden" name="type" value="{{ $type }}">

                            <div class="col-12">
                            <div class="card card-bordered mb-4">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-12 col-md-8">
                                            <div class="row">
                                                <div class="col-12">
                                                    <div class="form-group mb-3">
                                                        <label for="firstname">Vorname*</label>
                                                        <input type="text" maxlength="60" class="form-control {{ ($errors->has("firstname")) ? "border-danger" : "" }}" name="firstname" id="firstname" value="{{ old("firstname") }}">
                                                        <div class="invalid-feedback"></div>
                                                    </div>
                                                </div>
                                                <div class="col-12">
                                                    <div class="form-group mb-3">
                                                        <label for="lastname">Nachname*</label>
                                                        <input type="text" maxlength="60" class="form-control {{ ($errors->has("lastname")) ? "border-danger" : "" }}" name="lastname" id="lastname" value="{{ old("lastname") }}">
                                                        <div class="invalid-feedback"></div>
                                                    </div>
                                                </div>
                                                <div class="col-12 col-md-6">
                                                    <div class="form-group mb-3">
                                                        <label for="order_date">Bestellung vom*</label>
                                                        <div class="input-group pointer">
                                                            <input type="text" autocomplete="off" maxlength="10" class="form-control datepicker-date {{ ($errors->has("order_date")) ? "border-danger" : "" }}" min-date="{{ \Carbon\Carbon::now()->subYear() }}" max-date="{{ \Carbon\Carbon::now() }}" name="order_date" id="order_date" value="{{ old("order_date") }}">
                                                            <div class="input-group-append">
                                                                <span class="input-group-text"><span class="fas fa-calendar"></span></span>
                                                            </div>
                                                        </div>
                                                        <div class="invalid-feedback"></div>
                                                    </div>
                                                </div>
                                                <div class="col-12 col-md-6">
                                                    <div class="form-group mb-3">
                                                        <label for="received_date">Erhalten am</label>
                                                        <div class="input-group pointer">
                                                            <input type="text" autocomplete="off" maxlength="10" class="form-control datepicker-date {{ ($errors->has("received_date")) ? "border-danger" : "" }}" min-date="{{ \Carbon\Carbon::now()->subYear() }}" max-date="{{ \Carbon\Carbon::now() }}" name="received_date" id="received_date" value="{{ old("received_date") }}">
                                                            <div class="input-group-append">
                                                                <span class="input-group-text"><span class="fas fa-calendar"></span></span>
                                                            </div>
                                                        </div>
                                                        <div class="invalid-feedback"></div>
                                                    </div>
                                                </div>
                                                <div class="col-12 ">
                                                    <div class="form-group mb-3">
                                                        <label for="produkt">Produkt*</label>
                                                        <div class="input-group pointer">
                                                            <select class="form-select custom-select {{ ($errors->has("produkt")) ? "border-danger" : "" }}" name="produkt" id="produkt" >
                                                                <option value="" {{(empty(old("produkt")) ? "selected":"")}}>Bitte wählen..</option>
                                                                <option value="Krone Abo" {{(!empty(old("produkt")) && old("produkt")=="Krone Abo") ? "selected":""}}>Krone Abo</option>
                                                                <option value="Krone Vorteilswelt Bestellung" {{(!empty(old("produkt")) && old("produkt")=="Krone Vorteilswelt Bestellung") ? "selected":""}}>Krone Vorteilswelt Bestellung</option>
                                                                <option value="Kurier Abo" {{(!empty(old("produkt")) && old("produkt")=="Kurier Abo") ? "selected":""}}>Kurier Abo</option>
                                                                <option value="Kurier Vorteilswelt Bestellung" {{(!empty(old("produkt")) && old("produkt")=="Kurier Vorteilswelt Bestellung") ? "selected":""}}>Kurier Vorteilswelt Bestellung</option>
                                                                <option value="Kids Krone" {{(!empty(old("produkt")) && old("produkt")=="Kids Krone") ? "selected":""}}>Kids Krone</option>
                                                                <option value="Rätselkrone" {{(!empty(old("produkt")) && old("produkt")=="Rätselkrone") ? "selected":""}}>Rätselkrone</option>
                                                                <option value="Rätselkrone Sudoku" {{(!empty(old("produkt")) && old("produkt")=="Rätselkrone Sudoku") ? "selected":""}}>Rätselkrone Sudoku</option>
                                                                <option value="Kreuzworträtsel-Spezial" {{(!empty(old("produkt")) && old("produkt")=="Kreuzworträtsel-Spezial") ? "selected":""}}>Kreuzworträtsel-Spezial</option>
                                                                <option value="Bundesliga-Journal" {{(!empty(old("produkt")) && old("produkt")=="Bundesliga-Journal") ? "selected":""}}>Bundesliga-Journal</option>
                                                                <option value="GESÜNDER Leben" {{(!empty(old("produkt")) && old("produkt")=="GESÜNDER Leben") ? "selected":""}}>GESÜNDER Leben</option>
                                                                <option value="WIENER" {{(!empty(old("produkt")) && old("produkt")=="WIENER") ? "selected":""}}>WIENER</option>
                                                            </select>

                                                        </div>
                                                        <div class="invalid-feedback"></div>
                                                    </div>
                                                </div>

                                                <div class="col-12">
                                                    <div class="form-group mb-3">
                                                        <label for="email">E-Mail Adresse*</label>
                                                        <input type="email" maxlength="100" class="form-control {{ ($errors->has("email")) ? "border-danger" : "" }}" name="email" id="email" value="{{ old("email") }}">
                                                        <div class="invalid-feedback"></div>
                                                    </div>
                                                </div>
                                                <div class="col-12">
                                                    <div class="form-group mb-3">
                                                        <label for="kundennummer">Kundennummer (falls vorhanden)</label>
                                                        @if($type == "abo")
                                                            <small class="float-right">
                                                                <a onfocus="this.blur()" data-toggle="modal" data-target="#kundennummerModal" class="text-danger">
                                                                    <span class="fas fa-caret-right"></span>
                                                                    Hier ermitteln
                                                                </a>
                                                            </small>
                                                        @endif
                                                        <input type="text" maxlength="12" class="form-control {{ ($errors->has("customer_number")) ? "border-danger" : "" }}" name="customer_number" id="kundennummer" value="{{ old("customer_number") }}">
                                                        <div class="invalid-feedback"></div>
                                                    </div>
                                                </div>
                                                <div class="col-12">
                                                    <div class="form-group mb-3">
                                                        <label for="address">Adresse*</label>
                                                        <textarea  rows="3" limit="200" class="form-control {{ ($errors->has("address")) ? "border-danger" : "" }}" name="address" id="address">{{ old("address") }}</textarea>
                                                        <div class="invalid-feedback"></div>
                                                    </div>
                                                </div>
                                                <div class="col-12">
                                                    <div class="form-group mb-3">
                                                        <label for="improvement_suggestion">
                                                            <small>Bitte begründen Sie Ihren Widerruf – Sie helfen uns damit unser Service zu verbessern:</small>
                                                        </label>
                                                        <textarea rows="5" limit="2000" class="form-control {{ ($errors->has("improvement_suggestion")) ? "border-danger" : "" }}" name="improvement_suggestion" id="improvement_suggestion">{{ old("improvement_suggestion") }}</textarea>
                                                        <div class="invalid-feedback"></div>
                                                    </div>
                                                </div>


                                                <div class="col-12">
                                                    <div class="form-group mb-3">
                                                        <small>{!! $alerts["T-ALL-01"]->message !!}</small>
                                                    </div>
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>



                        <div class="col-12">
                            @if (count($errors) > 0)
                                <div class="alert alert-danger">
                                    <ul>
                                        @foreach ($errors->all() as $error)
                                            <li>{!! $error !!}</li>
                                        @endforeach
                                    </ul>
                                </div>
                            @endif

                        </div>
                        <div class="col-12 text-end pb-5">
                            <div class="form-group mb-3">
                                <button onfocus="this.blur();" goodcaptcha="true"  class="btn btn-danger">
                                    Jetzt absenden
                                </button>
                            </div>
                        </div>
                        </form>
                    @endif


        </div> {{--row--}}

    </div>{{--container--}}

@endsection
@section('scripts')
    <script type="text/javascript" src='{{ url("inc/js/moment-with-locales.js") }}'></script>
    <script type="text/javascript" src='{{ url("inc/bootstrap-4-datetimepicker/js/bootstrap-datetimepicker.min.js") }}'></script>
    <script type="text/javascript" src='{{ url("inc/aos/aos.js") }}'></script>
    <script src="{{url('inc/js/bs-custom-file-input.min.js')}}"></script>
    <script type="text/javascript" src='{{ url("inc/js/bowser.js") }}'></script>
    <script src="{{url('inc/js/frontend.js?v=3')}}"></script>
@endsection
