<!DOCTYPE html>
<html lang="de">
<head>
    <title>Info-Screen</title>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description"
          content="Als österreichischer Marktführer steht MediaPrint mit Ihren starken Markenfamilien Kronen Zeitung und KURIER für unabhängige Informationsvielfalt und innovative 360° Medienangebote und Produktvielfalt."/>
    <link rel="stylesheet" href="{{url('inc/bootstrap-5.2.2/dist/css/bootstrap.min.css')}}">
    <script src="{{url('inc/bootstrap-5.2.2/dist/js/bootstrap.bundle.min.js')}}"></script>
    <link rel="stylesheet" href="{{url('inc/css/krone-fonts/placard-compbold.css')}}">
    <link rel="stylesheet" href="{{url('inc/css/kurier-fonts/merriweather/merriweather.css')}}">
    <link rel="stylesheet" href="{{url('inc/css/frontend_fonts.css?v=4')}}">
    <style>
        @if($medium == "krone")
        .carousel-caption h1{
            font-family:PlacardKrone, Verdana, Geneva, "sans-serif" !important;
            font-size:4.3rem;
        }
        .logo{
            position:absolute;
            width:230px;
            top:2%;
            left:1%;
            z-index:999;
        }
        @else
        .carousel-caption h1{
            font-family:'Merriweather', Verdana, Geneva, "sans-serif" !important;
            font-size:3.5rem;
            font-weight:bold;
        }
        .logo{
            position:absolute;
            width:300px;
            top:5%;
            left:1%;
            z-index:999;
        }
        @endif
        body{
            height:100vh;
            background-color:black;
        }
        .carousel-caption{
            width:100%;
            left:0;
            bottom:0;
            background:rgba(255, 255, 255, 0.7);
            color:black;
            font-weight:bold;
            text-align:left;
            padding:1rem;
            height:30vh;
        }
        .carousel-caption p{
            font-family:Roboto, "sans-serif" !important;
            font-size:3rem;
            line-height:3.9rem;
            font-weight:bold;
        }
    </style>
</head>
<body>
@if(!empty($checkSlides))
    <div class="container-fluid d-flex justify-content-center flex-column h-100">
        <div class="row row-cols-5">
            @foreach($elements as $index => $el)
                <div class="col mb-3">
                    <div class="card w-100 h-100">
                        <img src="{{$el['image']}}" alt="" class="w-100">
                        <div class="card-body">
                             {{$el['filename']}}
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    </div>
@else
    <div id="carouselExampleControls"  class="carousel slide carousel-fade" data-bs-ride="carousel">
        <div class="carousel-inner">
            @if(!empty($logo))
                <img src="{{$logo}}" class="logo" alt="...">
            @endif
            @foreach($elements as $el)

                <div class="carousel-item   {{($loop->first) ? 'active' : ''}} overflow-hidden vh-100 mh-100"
                     data-bs-interval="{{(!empty($el['interval'])) ? $el['interval'] : 10000}}"
                     data-pause="false"
                >
                    <img src="{{$el["image"]}}" class="d-block w-100 img-fluid" alt="...">
                    @if($el["title"] || $el["description"])
                        <div class="carousel-caption text-left d-none d-md-block">
                            <h1>{{$el["title"]}}</h1>
                            <p>{{$el["description"]}}</p>
                        </div>
                    @endif
                </div>
            @endforeach
        </div>
    </div>
@endif

{{--

    <div id="carouselExampleControls" class="carousel slide carousel-fade" data-bs-ride="carousel">
        <div class="carousel-inner">
            <img src="{{url('img/rssviewer/krone_big.png')}}" class="logo">
            @foreach($rss as $item)
                <div class="carousel-item {{($loop->first) ? 'active' : ''}} vh-100 mh-100" data-bs-interval="12000">
                    <img src="{{$item["image"]}}" class="d-block w-100" alt="...">
                    <div class="carousel-caption text-left d-none d-md-block">
                        <h1>{{$item["title"]}}</h1>
                        <p>{{$item["description"]}}</p>
                    </div>
                </div>
            @endforeach
        </div>
    </div>
--}}

</body>
</html>


