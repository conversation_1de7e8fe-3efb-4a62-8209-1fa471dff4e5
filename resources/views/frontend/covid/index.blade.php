<!DOCTYPE html>
<html lang="de">
<head>
    <title>{{  env("APP_TITLE") }}</title>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="_token" content="{!! csrf_token() !!}" />
    <meta name="description"
          content="Als österreichischer Marktführer steht MediaPrint mit Ihren starken Markenfamilien Kronen Zeitung und KURIER für unabhängige Informationsvielfalt und innovative 360° Medienangebote und Produktvielfalt." />
    <link rel="icon" type="image/png" href="{{url('favicon.ico')}}" />
    <link rel="stylesheet" href="{{url('inc/bootstrap-5.2.2/dist/css/bootstrap.min.css')}}">

    <link rel="stylesheet" href="{{url('inc/css/frontend.css')}}">
    <link rel="stylesheet" href="{{ url("inc/fontawesome-pro-6.5.2-min/dist/all.min.css") }}">
    <script src="{{ url("inc/fontawesome-pro-6.5.2-min/dist/all.min.js") }}"  type="text/javascript"></script>
    <link href="https://fonts.googleapis.com/css2?family=Spartan:wght@400;500;700&display=swap" rel="stylesheet">

    <script src="{{url('inc/bootstrap-5.2.2/dist/js/bootstrap.bundle.min.js')}}"></script>
    <script src="{{url('inc/jquery/jquery.js')}}"></script>
    <script src="{{url('inc/js/isIE.js')}}"></script>

</head>
<body class="mt-5">
<div class="container">
    <div class="row " style="margin-bottom: 6rem;">

        <div class="col-12">
            <div class="px-5 mt-3 mb-5">
                <div class="card w-100">
                    <div class="card-body ">
                        <h3 class="card-title mb-4">Vorregistrierung COVID-Impfung</h3>
                        <p>
                            Die Frist für die Anmeldung ist vorbei. <br>
                            Danke für Ihr Interesse!
                        </p>
                        {{--<form action="{{url('covid-impfung-check')}}" method="POST">
                            @csrf
                            <div class="mb-4">
                                <p>
                                    Das Unternehmen ist aktuell bemüht, COVID-Impftermine zu organisieren. Im Vorfeld ist es notwendig, die Anzahl der impfwilligen Mitarbeiter/innen im Unternehmen zu erheben. Wenn Sie sich impfen lassen wollen,
                                    <u>registrieren Sie sich bitte verbindlich</u> unter nachfolgendem Link.
                                    <br>
                                    <br>
                                    Geben Sie dafür bitte Ihren Nachnamen und Ihre Sozialversicherungsnummer ein (die Daten werden ausschließlich zweckgebunden für diese Abfrage verwendet). Sobald Impftermine verfügbar sind, werden Sie kontaktiert.
                                    <br><br>
                                    Sollten Sie bereits anderweitig einen Impftermin erhalten haben, dann bitten wir Sie dringend, auf Doppelbuchungen zu verzichten um anderen Kolleg/innen die Möglichkeit zu geben, einen Impftermin zu erhalten.
                                </p>
                            </div>
                            <div class="mb-3">
                                <label for="nachname" class="form-label">Nachname</label>
                                <input type="text" class="form-control" id="nachname"
                                       name="nachname"
                                       value="{{old('nachname')}}"
                                       placeholder="Nachname">
                            </div>
                            <div class="mb-3 input-group-lg">
                                <label for="svn" class="form-label">SVNR</label>
                                <input
                                    placeholder="SVNR TTMMJJ"
                                    value="{{old('svn')}}"
                                    type="text" class="form-control" id="svn"
                                    name="svn">
                            </div>

                            <div class="text-end">
                                <button class="btn btn-brown">
                                    Jetzt Anmelden
                                </button>
                            </div>
                        </form>--}}
                    </div>



                    {{--
                                       <div class="card-footer p-0 m-0 bg-white border-0">
                                           <div class="alert alert-danger m-0 border-0" role="alert">
                                               Bei Fragen oder technischen Problemen wenden Sie sich bitte an
                                               <a href="mailto:<EMAIL>"><EMAIL></a>
                                      </div>
                                       </div>

                                      {{-- <div class="card-footer p-0 m-0 bg-white border-0">
                                           <div class="alert alert-danger m-0 border-0" role="alert">
                                               Achtung: Es gibt aktuell nur mehr Termine für Sonntag, den 30.05.2021

                                           </div>
                                       </div>

                                       @if($errors->first('nachname') || $errors->first('svn') )
                                           <div class="card-footer p-0 m-0 bg-white border-0">
                                               <div class="alert alert-danger m-0 border-0" role="alert">
                                                   Bitte alle Felder ausfüllen
                                               </div>
                                           </div>
                                       @endif

                                       @if($errors->first('notFound')  )
                                           <div class="card-footer p-0 m-0 bg-white border-0">
                                               <div class="alert alert-danger m-0 border-0" role="alert">
                                                   {{$errors->first('notFound')  }}
                                               </div>
                                           </div>
                                       @endif--}}


                </div>
            </div>

        </div>
    </div>
</div>

{{--<script>
    const baseurl = "{{url('/')}}";
    const GA = "{{env('GA')}}";

    document.getElementById('svn').addEventListener('input', function (e) {
        if(e.target.value.length < 6) e.target.value = e.target.value.replace(/[^\dA-Z]/g, '').replace(/(.{4})/g, '$1 ').trim();
    });
</script>--}}


</body>
</html>
