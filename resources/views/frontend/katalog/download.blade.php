<!DOCTYPE html>
<html lang="de">
<head>
    <title>{{ $epaper->description }}</title>
    <meta name="description" content="{{ $epaper->description }}">
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="_token" content="{!! csrf_token() !!}"/>
    <link rel="icon" type="image/png" href="{{url('favicon.ico')}}"/>
    <link rel="stylesheet" href="{{url('inc/bootstrap-5.2.2/dist/css/bootstrap.min.css')}}">

    <link rel="stylesheet" href="{{url('inc/css/frontend.css')}}">
    <link rel="stylesheet" href="{{ url("inc/fontawesome-pro-6.5.2-min/dist/all.min.css") }}">
    <script src="{{ url("inc/fontawesome-pro-6.5.2-min/dist/all.min.js") }}"  type="text/javascript"></script>
    <link href="https://fonts.googleapis.com/css2?family=Spartan:wght@400;500;700&display=swap" rel="stylesheet">

    <script src="{{url('inc/js/isIE.js')}}"></script>
    <script src="{{url('inc/js/jspdf.min.js')}}"></script>
    <style>
        .v-align {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%,-50%);
        }
        #spinner{
            animation:rotation 2s infinite linear;
            display:inline-block;
            font-size:9rem;
            line-height:10rem;
            width:10rem;
            height:10rem;
            text-align:center;
            vertical-align:bottom;
        }
        @keyframes rotation{
            from{
                transform:rotate(0deg);
            }
            to{
                transform:rotate(359deg);
            }
        }
        .text-brown {
            color: #c7a28d;
        }
        .text-red{
            color: red;
        }
    </style>
</head>
<body class=" container  overflow-hidden h-100">

    <div class="row">
        <div class="col-12 text-center mt-5 " data-class="loading">

            @if($cover)
                <div class="position-relative">
                    <img class="px-2 cover" src="{{asset("storage/app/".$cover)}}" style="width: {{$width/5}}px; height: {{$height/5}}px" alt="hidden-img">
                    <div style="top:0; bottom: 0; left: 0; right: 0; position:absolute; background-color: rgba(255,255,255,0.3)"></div>
                    <div class="v-align">
                        <i id="spinner" class="text-red fad fa-spinner"></i>
                    </div>
                </div>
            @else
                <div>
                    <i id="spinner" class="text-red fad fa-spinner"></i>
                </div>
            @endif
            <p class="mt-3 h5">
                Warten Sie, während Ihr eBook heruntergeladen wird
            </p>
        </div>
        <div class="col-12 text-center mt-5 " data-class="completed" style="display:none">
            @if($cover)
                <div class="position-relative">
                    <img class="px-2 cover" src="{{asset("storage/app/".$cover)}}" style="width: {{$width/5}}px; height: {{$height/5}}px" alt="cover ebook">

                </div>

            @endif
            <p class="my-3 h5">
                <i class="text-success far fa-check" style=" font-size: 2rem;"></i> Ihr Ebook wurde heruntergeladen!
            </p>
            <div>
                <a href="{{url('/mediathek')}}" class="btn btn-brown">
                    <i class="fas fa-arrow-left"></i>  Zum Mediathek
                </a>
            </div>
        </div>
    </div>



<div id="jsPdfDomContent" style="visibility:hidden;" class="position-absolute">
    @foreach($images as $img)
        <div>
            <img class="px-2" src="{{asset("storage/app/".$img)}}" style="max-width: {{$width/3}}px; max-height: {{$height/3}}px" alt="hidden-img">
        </div>
    @endforeach
</div>
</body>

<script>
    function downloadPdf(){
        const pdf = new jsPDF('p', 'pt', [ {{$width/3}}, {{$height/3}}]);
        const source = document.getElementById('jsPdfDomContent')
        specialElementHandlers = {

        };
        margins = {
            top : 5,
            bottom : 10,
        };
        // all coords and widths are in jsPDF instance's declared units
        // 'inches' in this case
        pdf.fromHTML(
            source, // HTML string or DOM elem ref.
            margins.left, // x coord
            margins.top, { // y coord
                'width' : margins.width, // max width of content on PDF
                'elementHandlers' : specialElementHandlers
            },

            function(dispose){
                document.querySelector('[data-class="loading"]').style.display = 'none'
                document.querySelector('[data-class="completed"]').style.display = 'block'
                document.getElementById('jsPdfDomContent').style.display = 'none'
                // dispose: object with X, Y of the last line add to the PDF
                //          this allow the insertion of new lines after html
                pdf.save('ebook.pdf');
            }, margins
        );
    }


    downloadPdf();
</script>
</html>
