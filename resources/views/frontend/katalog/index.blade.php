<!DOCTYPE html>
<html class="no-js">
<head>
	<meta charset="utf-8">
	<title>{{ $epaper->description }}</title>
	<meta name="description" content="{{ $epaper->description }}">
	<meta name="HandheldFriendly" content="True">
	<meta name="MobileOptimized" content="320">
	<meta name="viewport" content="width=device-width, initial-scale=1, minimal-ui,maximum-scale=2">
	<meta http-equiv="cleartype" content="on">

    <link rel="stylesheet" href="{{ url("inc/wow_book_plugin/css/normalize.css") }}">
    <link rel="stylesheet" href="{{ url("inc/wow_book_plugin/wow_book/wow_book.css") }}" type="text/css" />
    <link rel="stylesheet" href="{{ url("inc/wow_book_plugin/css/main.css") }}">
    <script src="{{ url("inc/wow_book_plugin/js/vendor/modernizr-2.7.1.min.js") }}"></script>


</head>
<body>

<!-- Add your site or application content here -->
<div class='book_container'>
	<div id="book">
		@foreach($images as $img)
			<div>
				<img src="{{asset("storage/app/".$img)}}" alt="{{ $img }}">
			</div>
		@endforeach
	</div>
</div>



<script src="{{ url("inc/wow_book_plugin/js/vendor/jquery-2.1.0.min.js") }}"></script>
<script src="{{ url("inc/wow_book_plugin/js/helper.js") }}"></script>
<script type="text/javascript" src="{{ url("inc/wow_book_plugin/wow_book/pdf.combined.min.js") }}"></script>
<script src="{{ url("inc/wow_book_plugin/wow_book/wow_book.min.js") }}"></script>

<script>
    onInit = () => {
        // download Ebook
        const download = document.querySelector('[title = "download"]')
        download.addEventListener('click',()=>{
            location.href = window.location.href.split('#')[0] + '/download'
        })

    }
	$(function(){
		var bookOptions = {
			height : {{ $height }},
			width : {{ $width*2 }},
			maxHeight : {{ $height }},
			maxWidth : {{ $width*2 }},
			centeredWhenClosed : true,
			hardcovers : false,
			pageNumbers : false,
			toolbar : "lastLeft, left, right, lastRight, zoomin, zoomout, fullscreen, thumbnails, download",
			thumbnailsPosition : 'left',
			responsiveHandleWidth : 50,
			container : window,
			containerPadding : "25px",
			toolbarContainerPosition : "top"
		};

        if(window.innerWidth < 480){
            bookOptions.toolbar = "lastLeft, left, right, lastRight, fullscreen, download";
        }
		// create the book
		$('#book').wowBook(bookOptions);
        onInit();
	})



</script>
</body>
</html>
