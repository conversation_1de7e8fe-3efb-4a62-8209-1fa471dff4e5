@extends('frontend.master')
@section('content')


	<div class="container" style="min-height:30rem">
		<div class="row my-4">
			<section class="col-12">
				<img src="{{url('img/karriere/lehre_image.jpg')}}" class="w-100" alt="lehre">

				<h1 class="py-2 my-4 font-reg  title-smaller">Ihre Videobewerbung bei MediaPrint </h1>
                <div class="w-100 alert alert-success text-center my-5 " role="alert" id="success-msg">
                    <p class="h4 m-0 py-2">Videoformular erfolgreich gesendet!</p>
                </div>
			</section>

			<section class="col-12  section-description mb-4">
				<p class="mb-4">
					Um uns einen persönlichen Eindruck von Ihnen zu verschaffen haben Sie hier die Möglichkeit 3 kurze Videos über sich und Ihre bisherigen
					Erfahrungen aufzunehmen.
				</p>
				<p class="mb-4">
					Hier gibt es eine Schritt für Schritt Anleitung wie Sie Ihre Videobewerbung durchführen können:
				</p>
				<p><b>Schritt 1:</b> Lesen Sie sich die Fragestellung für alle drei Videos durch und machen Sie sich Gedanken was Sie uns in den Videos
					mitteilen möchten.</p>
				<p><b>Schritt 2:</b> Nehmen Sie die Videos bequem über Ihr Smartphone oder per Laptop-Kamera auf. Jedes Video sollte max. 1,5 Minuten
					dauern.</p>
				<p><b>Schritt 3:</b> Laden Sie die von Ihnen ausgewählten Videos in dem dafür vorgesehenen Feld hoch.</p>
				<p><b>Schritt 4:</b> Absenden!</p>
				Achtung, sobald Sie auf absenden klicken, können die Videos nicht mehr ausgetauscht werden. Die Videobewerbung ist dann endgültig
				abgeschlossen!
			</section>

			<section class="col-12  section-description mb-1 mt-5">
				<b class="m-0 d-block mb-1 h5 fw-bold mb-3 mt-4" style="">Worüber sollten die Videos handeln?</b>
				<p>
					Bitte nehmen Sie insgesamt 3 Videos auf mit den folgenden Inhalten:
				</p>
			</section>

			<section class="col-12  section-description mb-4">
				<b class="m-0 d-block mb-1 h5 fw-bold mb-3 mt-4" style="">
					Video 1: Stellen Sie sich selber vor.
				</b>
				<p class="mb-1">
					Gehen Sie dabei auf folgende Punkte ein:
				</p>
				<ul class="mb-0 pl-4">
					<li>Schule (Welche Schulart, wie viele Klassen absolviert, Lieblingsfächer etc.) </li>
					<li>Welche Eigenschaften machen Sie aus? Warum sind Sie der perfekte Lehrling?</li>
					<li>Alles was Sie sonst noch über sich erzählen wollen (Hobbies, Freunde, besondere Interessen etc.)</li>
				</ul>
			</section>

			<section class="col-12  section-description mb-4">
				<b class="m-0 d-block mb-1 h5 fw-bold mb-3 mt-4" style="">
					Video 2: Erzählen Sie uns warum Sie diesen Weg für sich gewählt haben.
				</b>
				<ul class="mb-0 pl-4">
					<li>Warum haben Sie sich für diesen Lehrberuf bei MediaPrint entschieden?</li>
				</ul>
			</section>

			<section class="col-12  section-description mb-4">
				<b class="m-0 d-block mb-1 h5 fw-bold mb-3 mt-4" style="">
					Video 3: Erzählen Sie uns von Ihren bisherigen Erfahrung im Zusammenhang mit den Tätigkeiten des Lehrberufs. Gehen Sie dabei auf folgende Fragen Ihres Wunsch-Lehrberufs ein:
				</b>
				<p class="mb-4">
					<b>Bürokauffrau/-mann: </b>
					Haben Sie schon Erfahrung in einem Büro gesammelt? Wo haben Sie bereits Ihr Organisationstalent bewiesen?
				</p>
				<p class="mb-4">
					<b>IT-Systemtechniker/-in: </b>
					Wie haben Sie bisher Ihre IT-Kenntnisse im Alltag / in der Schule genutzt? Haben Sie schon mal einen PC konfiguriert oder jemand anderen bei der Installation und Einstellungen von Programmen geholfen?
				</p>
				<p class="mb-4">
					<b>Medienfachfrau/-mann: </b>
					Wie nutzen Sie Medien / Social Media in Ihrer Freizeit? Wie leben Sie Ihre Kreativität aus?
				</p>
			</section>

			<div class="col-12 my-1" id="formContainer">
				<div class="form row mt-5 ">
					<div class="form-group col-lg-6 col-12 firstStep">
						<label for="bw-firstname" class="form-label">Vorname*</label>
						<input id="bw-firstname" name="bw-firstname" class="form-control" type="text">
					</div>
					<div class="form-group col-lg-6 col-12 firstStep">
						<label for="bw-lastname" class="form-label">Nachname*</label>
						<input id="bw-lastname" name="bw-lastname" class="form-control" type="text">
					</div>
					<div class="col-12 mt-1">
						<div class="alert alert-danger text-center  " role="alert" id="alertFields">
							Alle Felder* sind erforderlich!
						</div>
					</div>
					<div class="col-12 form-group mt-3" id="dropzone-form-container">
						<label for="videoUp" class="form-label d-flex justify-content-between">
						    <span>
							    Videos* <small>(.mp4,.mkv,.avi,.mov)</small>
						    </span>
							<span class="h6 m-0 p-0">
								Videos: <span id="videoCounter">0</span>/3
									<i class="fas fa-trash h4 point fw-bold text-danger" id="deleteQueue"></i>
						    </span>

						</label>
						<div id="videoUp">
							<form
								name="videoBewerbungForm"
								method="post"
								id="videoUploadForm"
								enctype="multipart/form-data" class="dropzone px-0 border">
								{{ csrf_field() }}
								{{ method_field('post') }}
								<input name="firstname" id="real-firstname" type="hidden">
								<input name="lastname" id="real-lastname" type="hidden">
								<input name="dateNow" id="real-date" type="hidden">
							</form>
							<small class="pt-4">
								Mit Hochladen des Videos bestätigen Sie die Speicherung Ihrer Daten für den weiteren Bewerbungsprozess.
							</small>
						</div>
					</div>
					<div class="col-12 text-md-end text-center">
						<button class="btn btn-brown " id="inactiveBtn" disabled>Formular Absenden</button>
						<button class="btn btn-brown " id="sendForm">Formular Absenden</button>
					</div>
					<div class="col-12 alert alert-info text-center " id="uploading" role="alert">
						Uploading...<br>
						Bitte schließen Sie die Seite erst, wenn der Upload abgeschlossen ist
					</div>
				</div>
			</div>
		</div>
	</div>






@endsection

@section('scripts')
    <link rel="stylesheet" type="text/css" href="{{url('inc/dropzone-5.7.0/dist/dropzone.css')}}">
    <script src="{{url('inc/dropzone-5.7.0/dist/dropzone.js')}}"></script>
    <script src="{{url('inc/dropzone-5.7.0/dist/dropzone.js')}}"></script>
    <script>
        /*
        *   Variables
        *
        */
        const deleteQueue = $('#deleteQueue')
        const formContainer = $('#formContainer')
        const dropZoneForm = $('#dropzone-form-container')

        const alertFields = $('#alertFields')
        const successMsg = $('#success-msg')

        // Non Form Inputs
        const firstName = $('#bw-firstname')
        const lastName = $('#bw-lastname')

        // Real Form Inputs
        const realFirstname = $('#real-firstname')
        const realLastname = $('#real-lastname')

        const sendFormular = $('#sendForm')
        const uploading = $('#uploading')
        const dateNow = $('#real-date')

        console.log()
        // hide on start
        alertFields.hide()
        successMsg.hide()
        uploading.hide()
        /*dropZoneForm.hide()*/
        sendFormular.hide()
        // Dropzone instance
        let uploadedFile = 0;
        Dropzone.autoDiscover = false;
        const myDropzone = new Dropzone("form#videoUploadForm", {
            url : "/karriere/bewerbungsvideo/send",
            paramName : "video",
            maxFiles : 3,
            parallelUploads : 3,
            maxFilesize : 200,
            timeout : 9900000,
            acceptedFiles : ".mp4,.mkv,.avi, .mov",
            dictDefaultMessage : 'Legen Sie Ihre Videodateien hier ab',
            init : function(){
                this.on("success", function(file, response){
                    uploadedFile++;
                    if(uploadedFile === 3){
                        showSuccessMsg()
                    }
                })

                this.on("error", function(file, response){
                    showError('Es werden nur 3 Videodateien akzeptiert, die die Größe von 200 MB nicht überschreiten', 4000)
                    // if non-video file or video file bigger than 200mb
                    this.removeFile(this.files[this.files.length - 1]);
                    showSendButton(this.files.length)

                })
                this.on("addedfile", function(file){

                    showSendButton(this.files.length)
                });

            }
        });

        deleteQueue.on('click', function(){
            myDropzone.removeAllFiles()
            $('#videoCounter').html("0")
        })

        function showSendButton(num){
            $('#videoCounter').html(num)
            if(num === 3){
                sendFormular.show()
                $('#inactiveBtn').hide()
                sendFormular.on('click', function(){
                    if(checkInputs()){
                        myDropzone.processQueue()
                        uploading.show()
                        sendFormular.hide()
                        deleteQueue.hide()
                    }
                })
            }


        }



        function showSuccessMsg(){
            $('.section-description').hide()
            formContainer.hide()
            successMsg.show()

        }

        function checkInputs(){
            if(firstName.val() && lastName.val()){
                dateNow.val(new Date().getTime())
                realFirstname.val(firstName.val().replace(/\s+/g, '_'));
                realLastname.val(lastName.val().replace(/\s+/g, '_'));
                dropZoneForm.show()
                $('.firstStep').hide()
                alertFields.hide()
                return true
            }else{
                console.log('aa')
                showError('Alle Felder* sind erforderlich!')
                return false
            }

        }

        // shows custom Bootstrap alert
        function showError(string, time = 3000){
            alertFields.html(string)
            alertFields.show()
            setTimeout(function(){
                alertFields.hide()
            }, time)
        }

    </script>
@endsection
