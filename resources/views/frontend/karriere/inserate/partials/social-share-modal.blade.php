<div class="modal fade" id="shareModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h1 class="modal-title fs-5" id="">Inserat teilen</h1>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body px-3">
                <div class="d-flex justify-content-around mb-3">
                    <a href="https://www.linkedin.com/shareArticle?url={{ url()->current() }}" target="_blank" rel="noopener noreferrer">
                        <i class="fa-3x fab fa-linkedin" style="color: #0077B5;"></i>
                    </a>
                    <a href="https://www.facebook.com/sharer/sharer.php?u={{ url()->current() }}" target="_blank" rel="noopener noreferrer">
                        <i class="fa-3x fab fa-facebook" style="color: #3B5998;"></i>
                    </a>
                    <a href="mailto:?subject={{$job->title}}&amp;body={{ url()->current() }}" target="_blank" rel="noopener noreferrer">
                        <i class="fa-3x fas fa-envelope" style="color: #D44638;"></i>
                    </a>
                    <a href="https://api.whatsapp.com/send?text={{ url()->current() }}" target="_blank" rel="noopener noreferrer">
                        <i class="fa-3x fab fa-whatsapp" style="color: #25D366;"></i>
                    </a>


                </div>

                <hr>

                <div class="text-center">
                    <button class="btn btn-outline-success" type="button" id="linkCopyButton" data-url="{{ url()->current() }}">Link kopieren</button>
                </div>







            </div>


        </div>
    </div>
</div>





<script>

   const linkCopyButton = document.getElementById("linkCopyButton");
    linkCopyButton.addEventListener("click", (ev) => {
        try {
            navigator.clipboard.writeText(ev.target.dataset.url);
            linkCopyButton.innerHTML = "Link kopiert !";
        } catch (error) {
            console.error("Unable to copy link to clipboard:", error);
        }
    });

   const shareModal = document.getElementById("shareModal");
   shareModal.addEventListener("hidden.bs.modal", () => {
       linkCopyButton.innerHTML = "Link kopieren";
   });



</script>

