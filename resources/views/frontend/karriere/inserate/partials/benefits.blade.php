@if($benefits->count())

    <div class="row d-none d-md-flex">

        <div class="col-lg-10 offset-lg-1">
            @foreach ($benefits->chunk(3) as $chunk)

                <div class="d-flex justify-content-center">
                    <div class="d-flex justify-content-around">

                        @foreach ($chunk as $bn)

                            <div class="position-relative p-3">


                                <div data-bs-toggle="popover"
                                     data-bs-title="{{$bn->title}}"
                                     data-bs-content="{{$bn->text}}"
                                     style="cursor: pointer;">
                                    @include('components.image-circle', ["image" => url($bn->imagePath), 'size' => '12rem', 'color' => $color])
                                </div>


                            </div>

                        @endforeach

                    </div>
                </div>

            @endforeach
        </div>

    </div>

    <div class="d-block d-md-none">

        <div class=" accordion" id="benefit-accordion">
            @foreach ($benefits as $bn)

                <div class="border-bottom mb-3">
                    <div class="position-relative collapsed" data-bs-toggle="collapse"
                         data-bs-target="#benefit-collapse-{{$bn->id}}">
                        <div class="d-flex justify-content-center">


                            @include('components.image-circle', ["image" => url($bn->imagePath), 'size' => '7rem', 'color' => $color])

                        </div>
                        <div class="d-flex flex-column justify-content-center end-0 top-0 bottom-0 position-absolute">
                            <i class="fas fa-plus text-mp-red fa-2x icon-open"></i>
                            <i class="fas fa-minus text-mp-red fa-2x icon-close"></i>
                        </div>
                    </div>
                    <div class="text-center pt-3  border-dark mb-3">
                        <h5 class="text-mp-red fw-bold">
                            {{$bn->title}}
                        </h5>
                    </div>

                    <div id="benefit-collapse-{{$bn->id}}" class="accordion-collapse collapse"
                         data-bs-parent="#benefit-accordion">
                        <div class="accordion-body text-center">
                            {{$bn->text}}
                        </div>
                    </div>
                </div>

            @endforeach
        </div>

    </div>

@elseif(count($benefits_original))
    <section class="mb-4">
        <ul class="ps-3 mb-0">
            @foreach($benefits_original as $point)
                <li class="{{($loop->last) ? '' : 'mb-3'}}">
                    <span>{{$point}}</span>
                </li>
            @endforeach
        </ul>
    </section>
  {{--  <hr class="separator mt-5">--}}

@endif
