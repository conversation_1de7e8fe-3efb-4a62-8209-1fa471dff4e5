<!DOCTYPE html>
<html lang="de">
<head>
    <title>{{  env("APP_TITLE") }}</title>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="_token" content="{!! csrf_token() !!}"/>
    <link rel="stylesheet" href="{{url('inc/css/frontend_fonts.css')}}">
    <link rel="stylesheet" href="{{url('inc/bootstrap-5.2.2/dist/css/bootstrap.min.css')}}">
    <link rel="stylesheet" href="{{ url("inc/fontawesome-pro-6.5.2-min/dist/all.min.css") }}">
    <script src="{{ url("inc/fontawesome-pro-6.5.2-min/dist/all.min.js") }}"  type="text/javascript"></script>
    <link rel="stylesheet" type="text/css" href='{{ url("inc/css/frontend.css") }}'/>
    <style>

        #main-title{
            font-size:1.8rem;

        }
        .infos{
            font-size:1.5rem;
            font-weight:bold;
        }

        .inserate{
            border-bottom:2px solid #ed2129;
        }
        .section-title, .header-text, .inserate, footer p{
            font-size:1.2rem;
        }
        .text-red{
            color:#ed2129;
        }
        hr{
            background-color:#ed2129;
            color:#ed2129;
            height:0.15rem;
            opacity:10;
        }

        li{
            color:#ed2129;
        }
        li span{
            color:#212529;
        }

        #ball{
            padding-top:2.5rem;
            text-align:center;
            width:14rem;
            height:14rem;
            top:-10.5rem;
            right:2.5rem;
            border-radius:100%;
            font-size:1.5rem;
        }

        .block-text{
            text-align:justify;
            text-justify:inter-word
        }
        .bt-custom{
            min-width:8rem;
        }
    </style>
</head>
<body >

<div class="container   shadow my-sm-3  inserate position-relative " style="max-width: 1044px!important;">
    <div class="row">
        <div class="w-100 position-relative  overflow-hidden col-12 px-0 ">
            <img src="{{$job->header_img}}" alt="{{$job->title}}" class="w-100 "
                 onerror="this.onerror=null;this.src={!! '\'' . url('img/karriere/inserate/placeholder.png').'\''  !!};"
            >
        </div>

        <section class="col-12 px-md-5 px-4 position-relative">
            <h3 class="fw-bold pt-4 title-smaller text-red " id="main-title" style="max-width:80%;">{{mb_strtoupper(trans($job->title), 'UTF-8')}}</h3>
            <div class="row px-0">
                <div class="col-xl-5 col-lg-6 col-12  d-flex infos text-red">
                    @if($job->location)
                        <div>
                            <i class="fal fa-map-marker-alt title-smaller"></i>
                            <span class=" me-3 title-smaller">{{mb_strtoupper(trans($job->location), 'UTF-8')}}</span>
                        </div>

                    @endif
                </div>
                <div class="col-xl-5 col-lg-6 col-12  d-flex infos text-red">
                    @if($job->time  )
                        <div>
                            <i class="fal fa-clock title-smaller"></i>
                            <span class=" me-3 title-smaller">{{mb_strtoupper(trans($job->time), 'UTF-8')}}</span>
                        </div>
                    @endif
                </div>
                <div class="col-2">
                    <div class="bg-dark position-absolute text-white d-none d-xl-block" id="ball">
                        <p class="m-0">KRONE</p>
                        @if($job->location)
                            <p class="m-0">{{mb_strtoupper(trans($job->location), 'UTF-8')}}</p>
                        @endif
                        <p class="fw-bold m-0">SUCHT</p>
                        @if(!$job->anrede || strtolower(trans($job->anrede)) === 'du')
                            <p class="fw-bold m-0">DICH!</p>
                        @else
                            <p class="fw-bold m-0">SIE!</p>
                        @endif
                    </div>
                </div>
                <div class="col-12">
                    <hr class="bg-red">
                </div>
                <div class="col-12  pt-4">
                    <h2 class="fw-bold header-text  title-smaller {{--block-text--}}">
                        @if($job->header_text )
                                {{mb_strtoupper(trans($job->header_text), 'UTF-8')}}
                        @endif
                    </h2>
                </div>
            </div>
        </section>

        @if($job->tasks)
            <section class="col-12 px-md-5 px-4 mb-1 mt-5">
                <h5 class="fw-bold section-title">
                    @if(!$job->anrede || strtolower(trans($job->anrede)) === 'du')
                        {{--DEINE AUFGABEN--}}
                        WAS DICH ERWARTET
                    @else
                        {{--IHRE AUFGABEN--}}
                        WAS SIE ERWARTET
                    @endif
                </h5>
                <ul class="ps-4 text-smaller">
                    @foreach($job->tasks as $job->task)

                        <li>
                            <span>{{$job->task}}</span>
                        </li>
                    @endforeach
                </ul>
            </section>
        @endif

        @if($job->requirements)
            <section class="col-12 px-md-5 px-4 mb-1 mt-5">
                <h5 class="fw-bold section-title">
                    WAS WIR UNS WÜNSCHEN
                    {{--@if(!$job->anrede || strtolower(trans($job->anrede)) === 'du')
                        DEIN PROFIL
                    @else
                        IHR PROFIL
                    @endif--}}
                </h5>
                <ul class="ps-4 text-smaller">
                    @foreach($job->requirements as $requirement)

                        <li>
                            <span>{{$requirement}}</span>
                        </li>

                    @endforeach
                </ul>

            </section>
        @endif

        @if($job->benefits )
            <section class="col-12 px-md-5 px-4 mb-1 mt-5">
                <h5 class="fw-bold section-title">
                   {{-- UNSERE BENEFITS--}}
                    @if(!$job->anrede || strtolower(trans($job->anrede)) === 'du')
                        WAS WIR DIR BIETEN
                    @else
                        WAS WIR IHNEN BIETEN
                    @endif
                </h5>
                <ul class="ps-4 text-smaller">
                    @foreach($job->benefits as $benefit)

                        <li>
                            <span>{{$benefit}}</span>
                        </li>

                    @endforeach
                </ul>
            </section>
        @endif

        <section class="col-12 px-md-5 px-4 mt-4 mb-1 block-text">
            <div id="inserat-footer " >
                @if($job->footer_text)
                    {{$job->footer_text}}
                @endif
            </div>
        </section>

        <footer class="col-12 px-md-5 px-4  mb-1 row pb-3">
            <div class="col-lg-12  ">
                <h3 class="fw-bold pt-4 title-smaller text-red " id="main-title">
                    KRONE. ÖSTERREICHS GRÖSSTE TAGESZEITUNG.
                </h3>
            </div>
            <div class=" col-12  px-0">
                <div class="row">
                    <div class="col-lg-8"></div>
                    <div class="col-lg-4 ">
                        <div class="position-relative  pt-4 text-right row ">
                            <div class="col-6 px-2 ">
                                @if(!$kurier)
                                    <a href="{{url('karriere/bewerben')}}" >
                                        <span class="mx-3 mb-0 btn btn-gray  bt-custom">ZURÜCK</span>
                                    </a>
                                @endif
                            </div>
                            <div class="col-6 text-end ">
                                <a href="{{url('karriere/bewerbung/'.$job->id)}}"  target="_blank" >
                                    <span class="mx-3 btn btn-red py-2 bt-custom">BEWERBEN</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </footer>

    </div>
</div>
</body>
</html>
