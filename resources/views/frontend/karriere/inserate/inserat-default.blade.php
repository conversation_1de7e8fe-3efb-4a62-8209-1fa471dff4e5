<!DOCTYPE html>
<html lang="de">
<head>
    <title>{{  env("APP_TITLE") }}</title>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="_token" content="{!! csrf_token() !!}"/>

    <link rel="stylesheet" href="{{url('inc/css/frontend_fonts.css')}}">
    <link rel="stylesheet" href="{{url('inc/bootstrap-5.2.2/dist/css/bootstrap.min.css')}}">
    <link rel="stylesheet" href="{{ url("inc/fontawesome-pro-6.5.2-min/dist/all.min.css") }}">
    <script src="{{ url("inc/fontawesome-pro-6.5.2-min/dist/all.min.js") }}"  type="text/javascript"></script>
    <link rel="stylesheet" type="text/css" href='{{ url("inc/css/frontend.css") }}'/>
    <style>



        #main-title{
            font-size:1.8rem;

        }
        .infos{
            font-size:1.5rem;
            font-weight:bold;
        }

        .inserate{
            border-bottom:2px solid black;
        }
        .section-title, .header-text, .inserate, footer p{
            font-size:1.2rem;
        }
        #separator{
            background-color:black;
            opacity:10;
            height:0.15rem !important;

        }


        li span{
            color:#212529;
        }

        .block-text{
            text-align:justify;
            text-justify:inter-word
        }
        .bt-custom{
            min-width:8rem;
        }

    </style>
</head>

<body>
<div class="container shadow   inserate my-sm-3  position-relative " style="max-width: 1044px!important;">
    <div class="row ">

        <div class="col-12 w-100 position-relative  overflow-hidden px-0">
            <img src="{{$job->header_img}}" alt="{{$job->title}}" class="w-100"
                 onerror="this.onerror=null;this.src={!! '\'' . url('img/karriere/inserate/placeholder.png').'\''  !!};"
            >
        </div>
        <section class="px-md-5 px-4  col-12">
            <div class="row px-0">
                <h1 class="fw-bold pt-4 title-smaller  col-12" id="main-title" style="max-width:80%;">{{mb_strtoupper(trans($job->title), 'UTF-8')}}</h1>
                <div class="col-xl-6 col-lg-6 col-12  d-flex infos ">
                    @if($job->location)
                        <div>
                            <i class="fal fa-map-marker-alt title-smaller"></i>
                            <span class=" me-3 title-smaller ">{{mb_strtoupper(trans($job->location), 'UTF-8')}}</span>
                        </div>
                    @endif
                </div>
                <div class="col-xl-6 col-lg-6 col-12  d-flex infos ">
                    @if($job->time)
                        <div>
                            <i class="fal fa-clock title-smaller"></i>
                            <span class=" me-3 title-smaller">{{mb_strtoupper(trans($job->time), 'UTF-8')}}</span>
                        </div>

                    @endif
                </div>

                <div class="col-12">
                    <hr id="separator" style="color:black!important">
                </div>

                <div class="col-12  pt-4">
                    <h2 class="fw-bold header-text block-text title-smaller">
                        @if($job->header_text )
                            {{mb_strtoupper(trans($job->header_text), 'UTF-8')}}
                        @endif
                    </h2>
                </div>

            </div>
        </section>

        @if($job->tasks)
            <section class="col-12 px-md-5 px-4  mb-1 mt-5">
                <h5 class="fw-bold section-title">
                    @if(strtolower(trans($job->anrede)) === 'du')
                        DEINE AUFGABEN
                    @else
                        IHRE AUFGABEN
                    @endif
                </h5>
                <ul class="ps-4 text-smaller">
                    @foreach($job->tasks as $task)
                        <li>
                            <span>{{$task}}</span>
                        </li>
                    @endforeach
                </ul>
            </section>
        @endif

        @if($job->requirements)
            <section class="col-12 px-md-5 px-4  mb-1 mt-4">
                <h5 class="fw-bold section-title ">
                    @if(!$job->anrede || strtolower(trans($job->anrede)) === 'du')
                        DEIN PROFIL
                    @else
                        IHR PROFIL
                    @endif
                </h5>
                <ul class="ps-4 text-smaller">
                    @foreach($job->requirements as $requirement)
                        <li>
                            <span>{{$requirement}}</span>
                        </li>
                    @endforeach
                </ul>
            </section>
        @endif

        @if($job->benefits )
            <section class="col-12 px-md-5 px-4  mb-1 mt-4">
                <h5 class="fw-bold section-title ">
                    UNSERE BENEFITS
                </h5>
                <ul class="ps-4 text-smaller">
                    @foreach($job->benefits as $benefit)
                        <li>
                            <span>{{$benefit}}</span>
                        </li>
                    @endforeach
                </ul>
            </section>
        @endif

        <section class="col-12 px-md-5 px-4  mt-4 mb-1 block-text">
            <div id="inserat-footer">
                @if($job->footer_text)
                    {{$job->footer_text}}
                @endif
            </div>
        </section>

        <footer class="col-12 px-md-5 px-4   mb-1 pb-3 mt-5">
            <div class="row">
                <div class="col-lg-8 col-12  ps-0 pb-0" style="max-height:10rem;">
                    <img
                        src="{{$job->footer_img}}"
                        alt="{{$job->company}}"
                        onerror="this.onerror=null;this.src={!! '\'' . url('img/karriere/inserate/placeholder.png').'\''  !!};"
                        class="w-100 " style="max-height: 100%;">
                </div>
                <div class="col-lg-4  col-12 d-flex flex-column align-self-end  ">
                    <div class="position-relative pt-4 text-right row">
                        @if(isset($job->company) && strtolower(trans($job->company)) === 'mediaprint' or strtolower(trans($job->company)) === 'mediacalling')
                            <div class="col-12">
                                <div class="text-lg-end text-center mb-4 mb-lg-0">
                                    <a target="_blank" href="https://www.facebook.com/mediaprintvermarktung/">
                                        <i style="font-size:1.7rem;" class="pe-3 fab fa-facebook-square"></i>
                                    </a>
                                    <a target="_blank" href="https://www.instagram.com/mediaprint_vermarktung/ ">
                                        <i style="font-size:1.7rem;" class="pe-3 fab fa-instagram-square"></i>
                                    </a>
                                    <a target="_blank" href="https://at.linkedin.com/company/mediaprint-zeitungs-und-zeitschriftenverlag-ges-m-b-h-co-kg">
                                        <i style="font-size:1.7rem;" class="pe-3 fab fa-linkedin"></i>
                                    </a>
                                    <a target="_blank" href="https://www.xing.com/companies/mediaprintzeitungs-undzeitschriftenverlagges.m.b.h.%26cokg">
                                        <i style="font-size:1.7rem;" class=" fab fa-xing-square"></i>
                                    </a>
                                </div>
                            </div>
                        @endif

                        <div class="col-6  mt-lg-5 ">
                            <div>
                                @if(!$kurier)
                                    <a href="{{url('karriere/bewerben')}}">
                                        <span class="btn  btn-gray  bt-custom">ZURÜCK</span>
                                    </a>
                                @endif
                            </div>

                        </div>
                        <div class="col-6  mt-lg-5 text-end">
                            <div>
                                <a href="{{url('karriere/bewerbung/'.$job->id)}}" target="_blank">
                                    <span class=" btn btn-brown bt-custom">BEWERBEN</span>
                                </a>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </footer>
    </div>


</div>

</body>

</html>
