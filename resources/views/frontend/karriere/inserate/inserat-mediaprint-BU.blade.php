<!DOCTYPE html>
<html lang="de">
<head>
    <title>{{  env("APP_TITLE") }}</title>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="_token" content="{!! csrf_token() !!}"/>

    <meta property="og:title" content="{{$job->title}}">
    <meta property="og:description" content="{{trans($job->header_text)}}">
    <meta property="og:image" content="{{$job->header_img}}">
    <meta property="og:url" content="{{url()->current()}}">
    <meta property="og:type" content="website">

    <link rel="stylesheet" href="{{url('inc/css/frontend_fonts.css')}}">
    <link rel="stylesheet" href="{{url('inc/bootstrap-5.2.2/dist/css/bootstrap.min.css')}}">
    <link rel="stylesheet" href="{{ url("inc/fontawesome-pro-6.5.2-min/dist/all.min.css") }}">
    <script src="{{ url("inc/fontawesome-pro-6.5.2-min/dist/all.min.js") }}"  type="text/javascript"></script>
    <link rel="stylesheet" type="text/css" href='{{ url("inc/css/frontend.css") }}'/>


    <script src="{{url('inc/bootstrap-5.2.2/dist/js/bootstrap.bundle.min.js')}}"></script>

    <style>


        .bg-mp-red{
            background-color:#F13D37;
        }
        .text-mp-red{
            color:#F13D37;
        }

        .social-icons{
            font-size:2.8rem;
        }

        html{
            font-size:16px;
        }

        .separator{
            border:.1rem solid #000;
            opacity:1;
        }

        /*
            Change icon in accordion on collapsed
        */
        .accordion .icon-open{
            display:none;
        }
        .accordion .collapsed .icon-close{
            display:none;
        }
        .accordion .collapsed .icon-open{
            display:block;
        }


        .big-button{
            padding:1.7rem 1.7rem 1.3rem;
        }

        .main-icons{
            font-size:2.8rem;

        }

        .popover{
            max-width:30rem !important;
        }

        .popover-header{
            color:#F13D37;
            font-weight:bold;
            font-size:1.3rem;
            text-align:center;
            white-space:nowrap;
        }

        .popover-body{
            text-align:center;
            background-color:#f0f0f0;
            font-size:1.1rem;
        }

        .custom-inside-container{
            padding:20px 20px 0;
        }

        @media (min-width:768px){
            .custom-inside-container{
                padding:60px 80px 0;
            }

        }

        .btn-non-styled{
            border: unset;
            background-color:unset;
            padding: unset;
            margin: unset;
        }



        .benefit-image--container {
            position: relative;
            overflow: hidden;
        }

        .benefit-image--container img {
            transition: transform 0.3s;
        }

        .benefit-image--container:hover img {
            transform: scale(1.4);
        }

    </style>
</head>

<body>



@include('frontend.karriere.inserate.partials.notifications')
@include('frontend.karriere.inserate.partials.social-share-modal')



<!-- Modal -->

<div class="container  position-relative fs-4 " style="max-width: 1200px!important;">
    <div class="row">


    {{--    <div class="col-12 px-0">
            @include('frontend.karriere.inserate.partials.notifications')
        </div>--}}

        <div class="col-12 w-100 position-relative  overflow-hidden px-0">


            <img src="{{$job->header_img}}" alt="{{$job->title}}" class="w-100"
                 onerror="this.onerror=null;this.src={!! '\'' . url('img/karriere/inserate/placeholder.png').'\''  !!};"
            >
        </div>

        <div class="col-12">
            <div class="custom-inside-container">

                {{--
                    Title
                --}}
                <h1 class="fw-bold my-4 title-smaller  col-12 display-5" id="main-title"
                    {{-- style="max-width:80%;"--}}
                >{{mb_strtoupper(trans($job->title), 'UTF-8')}}</h1>


                {{--
                    Header Text
                --}}
                <p class=" mb-5">
                    {{trans($job->header_text)}}
                </p>


                {{--
                Icons:  Location, Home Office, Type
                --}}
                <div class="border-top border-bottom d-flex justify-content-between flex-md-row flex-column py-4 mb-5 border-2 border-dark">
                    @if($job->realLocation)
                        <div class="d-flex align-items-center mb-lg-0 mb-3">
                            <i class="fal fa-map-marker-alt text-mp-red main-icons me-3"></i>
                            <span class="fs-3 fw-bold">{{mb_strtoupper(trans($job->realLocation), 'UTF-8')}}</span>
                        </div>
                    @endif

                    @if($job->hybrid)
                        <div class="d-flex align-items-center mb-lg-0 mb-3">
                            <i class="far fa-laptop text-mp-red main-icons me-3"></i>
                            <span class="fs-3 fw-bold">HYBRID</span>
                        </div>
                    @endif

                    @if($job->time)
                        <div class="d-flex align-items-center">
                            <i class="fal fa-clock text-mp-red main-icons me-3"></i>
                            <span class="fs-3 fw-bold">{{mb_strtoupper(trans($job->time), 'UTF-8')}}</span>
                        </div>
                    @endif
                </div>


                {{--
                    Sections
                --}}

                @php
                    $contactPerson = $job->contactPerson ?? null;
                @endphp

                {{--
                    Section 1
               --}}

                <div class="row justify-content-between">
                    <div
                        @if($contactPerson)
                            class="col-lg-7"
                        @else
                            class="col-12"
                        @endif
                    >

                        <h2 class="bg-mp-red p-2 pb-0 text-white d-inline-block mb-4"> {{--H2--}}
                            DAS ERWARTET {{($job->anrede == "Du") ? "DICH" : "SIE"}} BEI UNS:
                        </h2>

                        <ul class="ps-3 mb-0">
                            @foreach($job->tasks as $task)
                                <li class="{{($loop->last) ? '' : 'mb-3'}}">
                                    <span>{{$task}}</span>
                                </li>
                            @endforeach
                        </ul>
                    </div>

                    {{--
                        CONTACT PERSON
                    --}}
                    @if($contactPerson)

                        <div class="col-12 col-lg-4 mt-3 mt-lg-0">



                            @include('frontend.karriere.inserate.partials.contact-person', ['person' => $contactPerson])
                            @include('frontend.karriere.inserate.partials.contact-person-email-form',["person" => $contactPerson, "job" => $job])
                        </div>

                    @endif

                </div> {{--END ROW WAS ERWARTET DICH--}}

                <hr class="separator my-5">
                {{--
                    Section 2
                --}}
                <div class="row justify-content-between">
                    <section class="col-12">

                        <h2 class="bg-mp-red p-2 pb-0 text-white d-inline-block mb-4"> {{--H2--}}
                            DAS {{($job->anrede == "Du") ? "BRINGST DU" : "BRINGEN SIE"}} MIT:
                        </h2>

                        <ul class="ps-3 mb-0">
                            @foreach($job->requirements as $requirement)
                                <li class="{{($loop->last) ? '' : 'mb-3'}}">
                                    <span>{{$requirement}}</span>
                                </li>
                            @endforeach
                        </ul>
                    </section>


                </div> {{--END ROW DAS BRINGST DU MIT--}}
                <hr class="separator my-5">
                {{--
                    Section 3 BENEFITS
                --}}

                <div class="row justify-content-between">
                    <section class="col-12">

                        <h2 class="bg-mp-red p-2 pb-0 text-white d-inline-block mb-4"> {{--H2--}}
                            DAS BIETEN WIR {{($job->anrede == "Du") ? "DIR" : "IHNEN"}} MIT:
                        </h2>

                    </section>

                    <div class="py-3">
                        @include('frontend.karriere.inserate.partials.benefits',["benefits" => $job->benefits])
                    </div>

                </div>

                <section class="block-text my-4">
                    @if($job->footer_text)
                        {{$job->footer_text}}
                    @endif
                </section>


                <div class="fs-3 fw-bold">Wir freuen uns auf {{($job->anrede == "Du") ? "deine" : "Ihre"}} Bewerbung.</div>
                <div class="fs-2 fw-bold text-mp-red">{{($job->anrede == "Du") ? "BEWIRB DICH" : "BEWERBEN SIE SICH"}} GLEICH HEUTE!</div>


                <div class="my-5 py-5 text-center">
                    <a class="big-button bg-mp-red text-white fs-1" href="{{url('karriere/bewerbung/'.$job->id)}}" target="_blank">
                        JETZT BEWERBEN!
                    </a>
                </div>

                <hr class="separator">

                <footer class="text-center my-5 py-5">
                    <div class="mb-3">
                        <img style="max-width: 20rem" src="{{url('storage/app/public/logos/Mediaprint_Logo_RGB.png')}}" alt="Logo">
                    </div>
                    <div class="fs-6">
                        DIE <b>MEDIAPRINT ZEITUNGS UND ZEITSCHRIFTENVERLAG GES.M.B.H. & CO KG</b> IST EINE TOCHTERGESELLSCHAFT DER VERLAGE KRONE UND KURIER UND
                        FÜHRT
                        SÄMTLICHE ZEITUNGSWIRTSCHAFTLICHEN BELANGE DER BEIDEN VERLAGE DURCH. WIR SUCHEN VERSTÄRKUNG FÜR UNSER TEAM!
                    </div>
                </footer>

            </div> {{--Px-3--}}
        </div> {{--Col--}}


    </div>


</div>

<script>


    const popoverTriggerList = document.querySelectorAll('[data-bs-toggle="popover"]')


    const popoverList = [...popoverTriggerList].map(popoverTriggerEl => new bootstrap.Popover(popoverTriggerEl))


    popoverTriggerList.forEach(function(popover){
        popover.addEventListener('click', function(){
            // Close all other open popovers
            popoverTriggerList.forEach(function(otherPopover){
                if(otherPopover !== popover){
                    const popoverInstance = bootstrap.Popover.getInstance(otherPopover);
                    if(popoverInstance){
                        popoverInstance.hide();
                    }
                }
            });
        });
        document.addEventListener('click', function(event){
            const popoverInstance = bootstrap.Popover.getInstance(popover);
            if(popoverInstance && !popover.contains(event.target)){
                popoverInstance.hide();
            }
        });
    });


    /**/

    document.querySelectorAll('.disable-submit-on-send').forEach(form => {
        form.addEventListener('submit', () => {
            form.querySelectorAll(`[type=submit]`).forEach((x) =>{
                x.textContent = 'WIRD GESENDET'
                x.disabled = true;
            })
        })
    })





</script>

</body>

</html>
