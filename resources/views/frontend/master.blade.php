<!DOCTYPE html>
<html lang="de">
<head>
    <title>{{  env("APP_TITLE") }}</title>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="app" content="{{env('APP_ENV')}}">
    <meta name="_token" content="{!! csrf_token() !!}" />
    <meta name="description" content="Als österreichischer Marktführer steht MediaPrint mit Ihren starken Markenfamilien Kronen Zeitung und KURIER für unabhängige Informationsvielfalt und innovative 360° Medienangebote und Produktvielfalt." />
    @if(in_array("kontakt", Request::segments()))
        <meta name="robots" content="nosnippet">
    @endif

    <link rel="apple-touch-icon" sizes="57x57" href="{{url('favicons/apple-icon-57x57.png')}}">
    <link rel="apple-touch-icon" sizes="60x60" href="{{url('favicons/apple-icon-60x60.png')}}">
    <link rel="apple-touch-icon" sizes="72x72" href="{{url('favicons/apple-icon-72x72.png')}}">
    <link rel="apple-touch-icon" sizes="76x76" href="{{url('favicons/apple-icon-76x76.png')}}">
    <link rel="apple-touch-icon" sizes="114x114" href="{{url('favicons/apple-icon-114x114.png')}}">
    <link rel="apple-touch-icon" sizes="120x120" href="{{url('favicons/apple-icon-120x120.png')}}">
    <link rel="apple-touch-icon" sizes="144x144" href="{{url('favicons/apple-icon-144x144.png')}}">
    <link rel="apple-touch-icon" sizes="152x152" href="{{url('favicons/apple-icon-152x152.png')}}">
    <link rel="apple-touch-icon" sizes="180x180" href="{{url('favicons/apple-icon-180x180.png')}}">
    <link rel="icon" type="image/png" sizes="192x192" href="{{url('favicons/android-icon-192x192.png')}}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{url('favicons/favicon-32x32.png')}}">
    <link rel="icon" type="image/png" sizes="96x96" href="{{url('favicons/favicon-96x96.png')}}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{url('favicons/favicon-16x16.png')}}">
    <link rel="manifest" href="{{url('favicons/manifest.json')}}">
    <meta name="msapplication-TileColor" content="#ffffff">
    <meta name="msapplication-TileImage" content="{{url('favicons/ms-icon-144x144.png')}}">
    <meta name="theme-color" content="#ffffff">


    <!-- Google Tag Manager -->
    <script>(function(w, d, s, l, i){
            w[l] = w[l] || [];
            w[l].push({
                          'gtm.start' :
                              new Date().getTime(),
                          event : 'gtm.js'
                      });
            var f = d.getElementsByTagName(s)[0],
                j = d.createElement(s), dl = l != 'dataLayer' ? '&l=' + l : '';
            j.async = true;
            j.src =
                'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
            f.parentNode.insertBefore(j, f);
        })(window, document, 'script', 'dataLayer', 'GTM-KBPRTN5');</script>
    <!-- End Google Tag Manager -->

    <link rel="stylesheet" href="{{url('inc/bootstrap-5.2.2/dist/css/bootstrap.min.css')}}">
    <script src="{{url('inc/bootstrap-5.2.2/dist/js/bootstrap.bundle.min.js')}}"></script>

    <link rel="stylesheet" href="{{url('inc/css/frontend_fonts.css')}}">
    <link rel="stylesheet" href="{{url('inc/css/frontend.css')}}">
    <link rel="stylesheet" href="{{ url("inc/fontawesome-pro-6.5.2-min/dist/all.min.css") }}">
    <script src="{{ url("inc/fontawesome-pro-6.5.2-min/dist/all.min.js") }}" type="text/javascript"></script>

    <script src="{{url('inc/jquery/jquery.js')}}"></script>
    <script src="{{url('inc/js/isIE.js')}}"></script>
    <script src="{{ asset('https://w19.captcha.at/sdk/sdk.js?v='.now()->format('YmdH'))}}" type="text/javascript"></script>
    <script src="{{url('inc/js/goodcaptcha.js')}}" type="text/javascript"></script>

    @include('partials.didomi')

</head>
<body class="padding-top">


@include("partials.navbar")
@yield('content')

<didomi id="didomicategories" categories='' value=''></didomi>

@include('partials.footer')


{{--
to include script in a view: @section('scripts'), default null
--}}
@yield('scripts', null)

<script>
    const baseurl = "{{url('/')}}";
    const GA = "{{env('GA')}}";

    $(function(){
        @if(Session::has('message'))
        $(window).on('load', function(){
            $('html, body').animate({ scrollTop : ($(".alert-success").offset().top - 200) });
        });
        @endif
        @if (count($errors) > 0)
        $(window).on('load', function(){
            $('html, body').animate({ scrollTop : ($(".alert-danger").offset().top - 200) });
        });
        @endif
    });
</script>


</body>
</html>
