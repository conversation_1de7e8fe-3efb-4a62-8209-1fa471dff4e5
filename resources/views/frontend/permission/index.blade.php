@extends('frontend.master')

@section('title', $title )
@section('description', $description )

@section('content')
	<div class="w-100 bg-darkgrey font-white pt-5 pb-5">
		<div class="container">
			<div class="row">
				<div class="col-12 font-shadow">
					Hier können Sie uns Ihr Einverständnis zur Kommunikation erteilen
				</div>
			</div>
		</div>
	</div>

	<div class="w-100 bg-white pt-5 pb-5">
		<div class="container">
			@if(Session::has('message'))
				@include("messages.success")
			@else
				@if(empty($fehlermeldung))
					<div class="row">
						<div class="col-12 mt-4 mb-4">
							<h1>Einverständniserklärung</h1>
						</div>
					</div>
					<form action="{{ url("permission/".$transaktionsid) }}" id="permissionForm" name="permissionForm" method="post">
						{{ csrf_field() }}
						<div class="row">
							<div class="col-12">
								<h3>Persönliche Daten</h3>
							</div>
							<div class="col-12 col-md-7">
								<div class="row">
									<div class="col-12">
										<div class="form-group mt-3">
											<label>Kundennummer</label>
											<input type="text" class="form-control" value="{{ (!empty($result["kundennummer"])) ? $result["kundennummer"] : "" }}" disabled />
										</div>
									</div>
									<div class="col-12">
										<div class="form-group">
											<label>Anrede</label>
											<input type="text" class="form-control" value="{{ (!empty($result["anrede"])) ? $result["anrede"] : "" }}" disabled />
										</div>
									</div>
									<div class="col-12">
										<div class="form-group">
											<label>Titel</label>
											<input type="text" class="form-control" value="{{ (!empty($result["titel"])) ? $result["titel"] : "" }}" disabled />
										</div>
									</div>
									<div class="col-12">
										<div class="form-group">
											<label>Vorname</label>
											<input type="text" class="form-control" value="{{ (!empty($result["vorname"])) ? $result["vorname"] : "" }}" disabled />
										</div>
									</div>
									<div class="col-12">
										<div class="form-group">
											<label>Nachname</label>
											<input type="text" class="form-control" value="{{ (!empty($result["nachname"])) ? $result["nachname"] : "" }}" disabled />
										</div>
									</div>
								</div>

							</div>

							@if(!empty($result["emails"]))
								<div class="col-12 mt-4 mb-3">
									<h3>{{ (count($result["emails"]) > 2) ? "E-Mail Adressen bestätigen" : "E-Mail Adresse bestätigen" }}</h3>
								</div>
								<div class="col-12">
									<div class="row">
										@foreach($result["emails"] as $email)
											@if(!empty($email))
												<div class="col-12 col-md-7">
													<div class="form-group">
														<input type="text" class="form-control" value="{{ (!empty($email["email"])) ? $email["email"] : "" }}" disabled />
													</div>
												</div>
												<div class="col-12 col-md-5 d-flex align-items-center mb-3 mb-md-0">
													<div class="form-group">
														<div class="custom-control custom-checkbox">
															<input type="checkbox" value="{{$email["guid"]}}" {{(in_array($email["guid"], (!empty(old("emails"))) ? old("emails") : []) || empty(old("transaktionsid"))) ? 'checked':''}} class="custom-control-input" id="email_{{$email["guid"]}}" name="emails[]" onfocus="this.blur()">
															<label class="custom-control-label" for="email_{{$email["guid"]}}">Kommunikationserlaubnis</label>
														</div>
													</div>
												</div>
											@endif
										@endforeach
									</div>
								</div>
							@endif

							@if(!empty($result["telefone"]))
								<div class="col-12 mt-4 mb-3">
									<h3>{{ (count($result["telefone"]) > 2) ? "Telefonnummern bestätigen" : "Telefonnummer bestätigen" }}</h3>
								</div>
								<div class="col-12">
									<div class="row">
										@foreach($result["telefone"] as $telefon)
											@if(!empty($telefon))
												<div class="col-12 col-md-7">
													<div class="form-group">
														<input type="text" class="form-control" value="{{ (!empty($telefon["telefon"])) ? $telefon["telefon"] : "" }}" disabled />
													</div>
												</div>
												<div class="col-12 col-md-5 d-flex align-items-center mb-3 mb-md-0">
													<div class="form-group">
														<div class="custom-control custom-checkbox">
															<input type="checkbox" value="{{$telefon["guid"]}}" {{(in_array($telefon["guid"], (!empty(old("emails"))) ? old("emails") : []) || empty(old("transaktionsid"))) ? 'checked':''}} class="custom-control-input" id="email_{{$telefon["guid"]}}" name="telefone[]" onfocus="this.blur()">
															<label class="custom-control-label" for="email_{{$telefon["guid"]}}">Kommunikationserlaubnis</label>
														</div>
													</div>
												</div>
											@endif
										@endforeach
									</div>
								</div>
							@endif

							@if(!empty($alerts["A-PERM-01"]->message))
								<div class="col-12">
									<hr>
								</div>
								<div class="col-12">
									{!! (!empty($alerts["A-PERM-01"]->title)) ? "<h5>".$alerts["A-PERM-01"]->title."</h5>" : "" !!}
									{!! $alerts["A-PERM-01"]->message !!}
								</div>
							@endif

							@if(!empty($alerts["A-PERM-02"]->message))
								<div class="col-12">
									<hr>
								</div>
								<div class="col-12">
									{!! (!empty($alerts["A-PERM-02"]->title)) ? "<h5>".$alerts["A-PERM-02"]->title."</h5>" : "" !!}
									{!! $alerts["A-PERM-02"]->message !!}
								</div>
							@endif

							<div class="col-12">
								@include("messages.error")
							</div>

							<div class="col-12 text-end mt-5">
								<button type="button" submit="true" loadicon="true" class="btn btn-danger btn-lg btn-block d-md-none">Abschicken</button>
								<button type="button" submit="true" loadicon="true" class="btn btn-danger btn-lg d-none d-md-inline-block">Einverständniserklärung abschicken</button>
							</div>

						</div>
					</form>
				@else
					<div class="row">
						<div class="col-12">
							<div class="alert alert-danger">
								{!! $fehlermeldung !!}
							</div>
						</div>
					</div>
				@endif
			@endif
		</div>
	</div>
@endsection
@section('scripts')
 {{--   <script src="{{url('inc/js/bs-custom-file-input.min.js')}}#1"></script>--}}
    <script type="text/javascript" src='{{ url("inc/js/moment-with-locales.js") }}#1'></script>
    <script type="text/javascript" src='{{ url("inc/bootstrap-4-datetimepicker/js/bootstrap-datetimepicker.min.js") }}#1'></script>
    <script type="text/javascript" src='{{ url("inc/aos/aos.js") }}#1'></script>
    <script type="text/javascript" src="{{url('inc/js/bs-custom-file-input.min.js')}}#11"></script>
    <script type="text/javascript" src='{{ url("inc/js/bowser.js") }}#1'></script>
    <script src="{{url('inc/js/frontend.js')}}#1"></script>
@endsection
