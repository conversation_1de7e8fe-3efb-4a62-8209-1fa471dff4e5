@extends('frontend.master')

{{--
@section('javascripts')
@endsection--}}

@section('content')

    <div class="container">
        <div class="row">
            <div class="col-12 ">
                <img class="d-block w-100" alt="slide desktop" src="{{url("storage/app/public/".$picurl)}}">
            </div>
            <div class="col-12 ">
                <form action="{{ url((($event=="true")? "event/":"").$aktionurl) }}" method="POST">
                    {{ csrf_field() }}
                    <div class="col-12">
                        <div>
                            <br>
                            {!!  $einleitung!!}<br>
                            <br>
                        </div>
                    </div>
                    @if(Session::has('alert'))
                        {!! Session::get('alert') !!}
                    @else
                        <div class="col-12">
                            <div class="title-wrapper">
                                <h2>Exemplare</h2>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="card card-bordered mb-4">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-12 col-md-8">
                                            <div class="row">
                                                @if(!$beidemedien)
                                                    <div class="col-12">
                                                        <div class="form-group mb-3">
                                                            <label for="exemplare">Anzahl der gewünschten Exemplare Krone & Kurier*</label>
                                                            <select class="form-select custom-select" name="exemplare" id="exemplare">
                                                                <option value="1" {{(!empty(old("exemplare")) && old("exemplare")==1) ? "selected":""}}>1
                                                                </option>
                                                                <option value="2" {{(!empty(old("exemplare")) && old("exemplare")==2) ? "selected":""}}>2
                                                                </option>
                                                                <option value="3" {{(!empty(old("exemplare")) && old("exemplare")==3) ? "selected":""}}>3
                                                                </option>
                                                                <option value="4" {{(!empty(old("exemplare")) && old("exemplare")==4) ? "selected":""}}>4
                                                                </option>
                                                                <option value="5" {{(!empty(old("exemplare")) && old("exemplare")==5) ? "selected":""}}>5
                                                                </option>
                                                            </select>
                                                            <div class="invalid-feedback"></div>
                                                        </div>
                                                    </div>
                                                @else
                                                    @if(!$showonlykurier)
                                                    <div class="col-12">
                                                        <div class="form-group mb-3">
                                                            <label for="exemplare_krone">Anzahl der gewünschten Exemplare Krone*</label>
                                                            <select class="form-select custom-select" name="exemplare_krone" id="exemplare_krone">
                                                                <option
                                                                    value="0" {{(!empty(old("exemplare_krone")) && old("exemplare_krone")==0) ? "selected":""}}>
                                                                    keine
                                                                </option>
                                                                <option
                                                                    value="1" {{(!empty(old("exemplare_krone")) && old("exemplare_krone")==1) ? "selected":""}}>
                                                                    1
                                                                </option>
                                                                <option
                                                                    value="2" {{(!empty(old("exemplare_krone")) && old("exemplare_krone")==2) ? "selected":""}}>
                                                                    2
                                                                </option>
                                                                <option
                                                                    value="3" {{(!empty(old("exemplare_krone")) && old("exemplare_krone")==3) ? "selected":""}}>
                                                                    3
                                                                </option>
                                                                <option
                                                                    value="4" {{(!empty(old("exemplare_krone")) && old("exemplare_krone")==4) ? "selected":""}}>
                                                                    4
                                                                </option>
                                                                <option
                                                                    value="5" {{(!empty(old("exemplare_krone")) && old("exemplare_krone")==5) ? "selected":""}}>
                                                                    5
                                                                </option>
                                                            </select>
                                                            <div class="invalid-feedback"></div>
                                                        </div>
                                                    </div>
                                                    @endif
                                                    @if(!$showonlykrone)
                                                    <div class="col-12">
                                                        <div class="form-group mb-3">
                                                            <label for="exemplare_kurier">Anzahl der gewünschten Exemplare Kurier*</label>
                                                            <select class="form-select custom-select" name="exemplare_kurier" id="exemplare_kurier">
                                                                <option
                                                                    value="0" {{(!empty(old("exemplare_kurier")) && old("exemplare_kurier")==0) ? "selected":""}}>
                                                                    keine
                                                                </option>
                                                                <option
                                                                    value="1" {{(!empty(old("exemplare_kurier")) && old("exemplare_kurier")==1) ? "selected":""}}>
                                                                    1
                                                                </option>
                                                                <option
                                                                    value="2" {{(!empty(old("exemplare_kurier")) && old("exemplare_kurier")==2) ? "selected":""}}>
                                                                    2
                                                                </option>
                                                                <option
                                                                    value="3" {{(!empty(old("exemplare_kurier")) && old("exemplare_kurier")==3) ? "selected":""}}>
                                                                    3
                                                                </option>
                                                                <option
                                                                    value="4" {{(!empty(old("exemplare_kurier")) && old("exemplare_kurier")==4) ? "selected":""}}>
                                                                    4
                                                                </option>
                                                                <option
                                                                    value="5" {{(!empty(old("exemplare_kurier")) && old("exemplare_kurier")==5) ? "selected":""}}>
                                                                    5
                                                                </option>
                                                            </select>
                                                            <div class="invalid-feedback"></div>
                                                        </div>
                                                    </div>
                                                @endif
                                                @endif
                                                <div class="col-12">
                                                    <small>{!! $alerts["T-ALL-01"]->message !!}</small>
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-12">
                            <div class="title-wrapper">
                                <h2>Ansprechperson</h2>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="card card-bordered mb-4">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-12 col-md-8">
                                            <div class="row">
                                                <div class="col-12">
                                                    <div class="form-group mb-3">
                                                        <label>Anrede*</label>
                                                        <div class="custom-control custom-radio mb-1">
                                                            <input type="radio" value="1024"
                                                                   {{(old("anrede")==1024) ? "checked":""}}  class="custom-control-input" id="anrede_2"
                                                                   name="anrede" onfocus="this.blur()">
                                                            <label class="custom-control-label {{ ($errors->has("anrede")) ? "text-danger" : "" }}"
                                                                   for="anrede_2">Frau</label>
                                                        </div>
                                                        <div class="custom-control custom-radio mb-1">
                                                            <input type="radio" value="1025"
                                                                   {{(old("anrede")==1025) ? "checked":""}} class="custom-control-input" id="anrede_1"
                                                                   name="anrede" onfocus="this.blur()">
                                                            <label class="custom-control-label {{ ($errors->has("anrede")) ? "text-danger" : "" }}"
                                                                   for="anrede_1">Herr</label>
                                                        </div>
                                                        <div class="invalid-feedback"></div>
                                                    </div>
                                                </div>
                                                <div class="col-12">
                                                    <div class="form-group mb-3">
                                                        <label for="titel">Titel</label>
                                                        <select class="form-select custom-select {{ ($errors->has("titel")) ? "border-danger" : "" }}" name="titel"
                                                                id="titel">
                                                            <option value="0"></option>
                                                            @foreach($titles as $sid => $title)
                                                                <option
                                                                    value="{{ $sid }}" {{(!empty(old("titel")) && old("titel")==$sid) ? "selected":""}}>{{ $title["kurz"] }}</option>
                                                            @endforeach
                                                        </select>
                                                        <div class="invalid-feedback"></div>
                                                    </div>
                                                </div>
                                                <div class="col-12">
                                                    <div class="form-group mb-3">
                                                        <label for="vorname">Vorname*</label>
                                                        <input type="text" maxlength="60"
                                                               class="form-control {{ ($errors->has("vorname")) ? "border-danger" : "" }}" name="vorname"
                                                               id="vorname" value="{{ old("vorname") }}">
                                                        <div class="invalid-feedback"></div>
                                                    </div>
                                                </div>
                                                <div class="col-12">
                                                    <div class="form-group mb-3">
                                                        <label for="nachname">Nachname*</label>
                                                        <input type="text" maxlength="50"
                                                               class="form-control {{ ($errors->has("nachname")) ? "border-danger" : "" }}" name="nachname"
                                                               id="nachname" value="{{ old("nachname") }}">
                                                        <div class="invalid-feedback"></div>
                                                    </div>
                                                </div>
                                                <div class="col-12">
                                                    <div class="form-group mb-3">
                                                        <label for="telefonnummer">Telefon</label>
                                                        <input type="text" maxlength="20"
                                                               class="form-control {{ ($errors->has("telefonnummer") || $errors->has("email")) ? "border-danger" : "" }}"
                                                               name="telefonnummer" id="telefonnummer" value="{{ old("telefonnummer") }}"
                                                               placeholder="+43 664 123 123">
                                                        <div class="invalid-feedback"></div>
                                                    </div>
                                                </div>
                                                <div class="col-12">
                                                    <div class="form-group mb-3">
                                                        <label for="email">E-Mail Adresse</label>
                                                        <input type="email" autocomplete="off" maxlength="100"
                                                               class="form-control {{ ($errors->has("email")) ? "border-danger" : "" }}" name="email" id="email"
                                                               value="{{ old("email") }}">
                                                        <div class="invalid-feedback"></div>
                                                    </div>
                                                </div>

                                                <div class="col-12">
                                                    <small>{!! $alerts["T-ALL-01"]->message !!}</small>
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 ">
                            <div class="title-wrapper">
                                <h2>Firma</h2>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="card card-bordered mb-4">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-12 col-md-8">
                                            <div class="row">

                                                <div class="col-12">
                                                    <div class="form-group mb-3">
                                                        <label for="firma">Firmenname*</label>
                                                        <input type="text" maxlength="60"
                                                               class="form-control {{ ($errors->has("firma")) ? "border-danger" : "" }}" name="firma" id="firma"
                                                               value="{{ old("firma") }}">
                                                        <div class="invalid-feedback"></div>
                                                    </div>
                                                </div>
                                                <div class="col-12">
                                                    <small>{!! $alerts["T-ALL-01"]->message !!}</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-12 ">
                            <div class="title-wrapper">
                                <h2>Lieferadresse</h2>
                            </div>
                        </div>
                        <div class="col-12 ">
                            <div class="card card-bordered mb-4">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-12 col-md-8">
                                            <div class="row row-adress-form">
                                                <div class="col-12 col-md-6">
                                                    <div class="form-group mb-3">
                                                        <label for="strasse">Straße*</label>
                                                        <input type="text" maxlength="50"
                                                               class="form-control {{ ($errors->has("strasse")) ? "border-danger" : "" }}" name="strasse"
                                                               id="strasse" value="{{ old("strasse") }}">
                                                        <div class="invalid-feedback"></div>
                                                    </div>
                                                </div>
                                                <div class="col-12 col-md-2">
                                                    <div class="form-group mb-3">
                                                        <label for="hausnummer">Hausnr*</label>
                                                        <input type="text" maxlength="26"
                                                               class="form-control {{ ($errors->has("hausnummer")) ? "border-danger" : "" }}" name="hausnummer"
                                                               id="hausnummer" value="{{ old("hausnummer") }}">
                                                        <div class="invalid-feedback"></div>
                                                    </div>
                                                </div>
                                                <div class="col-6 col-md-2">
                                                    <div class="form-group mb-3">
                                                        <label for="stock">Stock</label>
                                                        <input type="text" maxlength="3"
                                                               class="form-control {{ ($errors->has("stock")) ? "border-danger" : "" }}" name="stock" id="stock"
                                                               value="{{ old("stock") }}">
                                                        <div class="invalid-feedback"></div>
                                                    </div>
                                                </div>
                                                <div class="col-6 col-md-2">
                                                    <div class="form-group mb-3">
                                                        <label for="tuer">Tür</label>
                                                        <input type="text" maxlength="4"
                                                               class="form-control {{ ($errors->has("tuer")) ? "border-danger" : "" }}" name="tuer" id="tuer"
                                                               value="{{ old("tuer") }}">
                                                        <div class="invalid-feedback"></div>
                                                    </div>
                                                </div>
                                                <div class="col-12 col-md-4">
                                                    <div class="form-group mb-3">
                                                        <label for="plz">PLZ*</label>
                                                        <input type="text"  maxlength="4"
                                                               class="form-control {{ ($errors->has("plz")) ? "border-danger" : "" }}" name="plz" id="plz"
                                                               value="{{ old("plz") }}">
                                                        <div class="invalid-feedback"></div>
                                                    </div>
                                                </div>
                                                <div class="col-12 col-md-8">
                                                    <div class="form-group mb-3">
                                                        <label for="ort">Ort*</label>
                                                        <input type="text" maxlength="20"
                                                               class="form-control {{ ($errors->has("ort")) ? "border-danger" : "" }}" name="ort" id="ort"
                                                               value="{{ old("ort") }}">
                                                        <div class="invalid-feedback"></div>
                                                    </div>
                                                </div>

                                            </div>


                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-12 row-register">
                            <div class="title-wrapper">
                                <h2>Anmerkungen</h2>
                            </div>
                        </div>
                        <div class="col-12 ">
                            <div class="card card-bordered mb-4">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-12">
                                            <div class="form-group mb-3">
                                                <label for="anmerkungen">Wollen Sie zu Ihren Daten noch etwas anmerken?</label>
                                                <textarea class="form-control" name="anmerkungen" id="anmerkungen" rows="5"
                                                          limit="200">{{old("anmerkungen")}}</textarea>
                                                {!!  ($errors->has("anmerkungen")) ? "<small class='form-text text-danger'><span class='fas fa-exclamation-circle'></span> ".join('<br/>', $errors->get('anmerkungen'))."</small>" : "" !!}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>


                        @if($event=="true")

                            <div class="col-12">
                                <div class="row w-100 row-register ">
                                    <div class="col-12">
                                        <div class="title-wrapper">
                                            <h2>Eventeingabe</h2>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="card card-bordered mb-4">
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-12">
                                                        <div class="form-group mb-3">
                                                            <label for="titel">Herkunftsbezeichnung *</label>
                                                            <select class="form-select custom-select" name="herkunftsbez" id="herkunftsbez">
                                                                <option value="" {{(empty(old("herkunftsbez"))) ? "selected":""}}>keine</option>
                                                                @if($aktionurl=="friseur")
                                                                    <option
                                                                        value="VA1 Friseurtour 2020 BGLD" {{(!empty(old("herkunftsbez")) && old("herkunftsbez")=="VA1 Friseurtour 2020 BGLD") ? "selected":""}}>
                                                                        VA1 Friseurtour 2020 BGLD
                                                                    </option>
                                                                    <option
                                                                        value="VA2 Friseurtour 2020 KTN" {{(!empty(old("herkunftsbez")) && old("herkunftsbez")=="VA2 Friseurtour 2020 KTN") ? "selected":""}}>
                                                                        VA2 Friseurtour 2020 KTN
                                                                    </option>
                                                                    <option
                                                                        value="VA3 Friseurtour 2020 NÖ" {{(!empty(old("herkunftsbez")) && old("herkunftsbez")=="VA3 Friseurtour 2020 NÖ") ? "selected":""}}>
                                                                        VA3 Friseurtour 2020 NÖ
                                                                    </option>
                                                                    <option
                                                                        value="VA4 Friseurtour 2020 OÖ" {{(!empty(old("herkunftsbez")) && old("herkunftsbez")=="VA4 Friseurtour 2020 OÖ") ? "selected":""}}>
                                                                        VA4 Friseurtour 2020 OÖ
                                                                    </option>
                                                                    <option
                                                                        value="VA5 Friseurtour 2020 SBG" {{(!empty(old("herkunftsbez")) && old("herkunftsbez")=="VA5 Friseurtour 2020 SBG") ? "selected":""}}>
                                                                        VA5 Friseurtour 2020 SBG
                                                                    </option>
                                                                    <option
                                                                        value="VA6 Friseurtour 2020 STMK" {{(!empty(old("herkunftsbez")) && old("herkunftsbez")=="VA6 Friseurtour 2020 STMK") ? "selected":""}}>
                                                                        VA6 Friseurtour 2020 STMK
                                                                    </option>
                                                                    <option
                                                                        value="VA7 Friseurtour 2020 TIR" {{(!empty(old("herkunftsbez")) && old("herkunftsbez")=="VA7 Friseurtour 2020 TIR") ? "selected":""}}>
                                                                        VA7 Friseurtour 2020 TIR
                                                                    </option>
                                                                    <option
                                                                        value="VA8 Friseurtour 2020 VBG" {{(!empty(old("herkunftsbez")) && old("herkunftsbez")=="VA8 Friseurtour 2020 VBG") ? "selected":""}}>
                                                                        VA8 Friseurtour 2020 VBG
                                                                    </option>
                                                                    <option
                                                                        value="VA9 Friseurtour 2020 WIEN" {{(!empty(old("herkunftsbez")) && old("herkunftsbez")=="VA9 Friseurtour 2020 WIEN") ? "selected":""}}>
                                                                        VA9 Friseurtour 2020 WIEN
                                                                    </option>
                                                                @endif
                                                                @if($aktionurl=="gastro")
                                                                    <option
                                                                        value="VA1 Gastrotour 2021 BGLD" {{(!empty(old("herkunftsbez")) && old("herkunftsbez")=="VA1 Gastrotour 2021 BGLD") ? "selected":""}}>
                                                                        VA1 Gastrotour 2021 BGLD
                                                                    </option>
                                                                    <option
                                                                        value="VA2 Gastrotour 2021 KTN" {{(!empty(old("herkunftsbez")) && old("herkunftsbez")=="VA2 Gastrotour 2021 KTN") ? "selected":""}}>
                                                                        VA2 Gastrotour 2021 KTN
                                                                    </option>
                                                                    <option
                                                                        value="VA3 Gastrotour 2021 NOE" {{(!empty(old("herkunftsbez")) && old("herkunftsbez")=="VA3 Gastrotour 2021 NOE") ? "selected":""}}>
                                                                        VA3 Gastrotour 2021 NOE
                                                                    </option>
                                                                    <option
                                                                        value="VA4 Gastrotour 2021 OOE" {{(!empty(old("herkunftsbez")) && old("herkunftsbez")=="VA4 Gastrotour 2021 OOE") ? "selected":""}}>
                                                                        VA4 Gastrotour 2021 OOE
                                                                    </option>
                                                                    <option
                                                                        value="VA5 Gastrotour 2021 SBG" {{(!empty(old("herkunftsbez")) && old("herkunftsbez")=="VA5 Gastrotour 2021 SBG") ? "selected":""}}>
                                                                        VA5 Gastrotour 2021 SBG
                                                                    </option>
                                                                    <option
                                                                        value="VA6 Gastrotour 2021 STMK" {{(!empty(old("herkunftsbez")) && old("herkunftsbez")=="VA6 Gastrotour 2021 STMK") ? "selected":""}}>
                                                                        VA6 Gastrotour 2021 STMK
                                                                    </option>
                                                                    <option
                                                                        value="VA7 Gastrotour 2021 TIR" {{(!empty(old("herkunftsbez")) && old("herkunftsbez")=="VA7 Gastrotour 2021 TIR") ? "selected":""}}>
                                                                        VA7 Gastrotour 2021 TIR
                                                                    </option>
                                                                    <option
                                                                        value="VA8 Gastrotour 2021 VBG" {{(!empty(old("herkunftsbez")) && old("herkunftsbez")=="VA8 Gastrotour 2021 VBG") ? "selected":""}}>
                                                                        VA8 Gastrotour 2021 VBG
                                                                    </option>
                                                                    <option
                                                                        value="VA9 Gastrotour 2021 WIEN" {{(!empty(old("herkunftsbez")) && old("herkunftsbez")=="VA9 Gastrotour 2021 WIEN") ? "selected":""}}>
                                                                        VA9 Gastrotour 2021 WIEN
                                                                    </option>
                                                                @endif
                                                                @if($aktionurl=="hotel")
                                                                    <option
                                                                        value="VA1 Hoteltour 2021 BGLD" {{(!empty(old("herkunftsbez")) && old("herkunftsbez")=="VA1 Hoteltour 2021 BGLD") ? "selected":""}}>
                                                                        VA1 Hoteltour 2021 BGLD
                                                                    </option>
                                                                    <option
                                                                        value="VA2 Hoteltour 2021 KTN" {{(!empty(old("herkunftsbez")) && old("herkunftsbez")=="VA2 Hoteltour 2021 KTN") ? "selected":""}}>
                                                                        VA2 Hoteltour 2021 KTN
                                                                    </option>
                                                                    <option
                                                                        value="VA3 Hoteltour 2021 NOE" {{(!empty(old("herkunftsbez")) && old("herkunftsbez")=="VA3 Hoteltour 2021 NOE") ? "selected":""}}>
                                                                        VA3 Hoteltour 2021 NOE
                                                                    </option>
                                                                    <option
                                                                        value="VA4 Hoteltour 2021 OOE" {{(!empty(old("herkunftsbez")) && old("herkunftsbez")=="VA4 Hoteltour 2021 OOE") ? "selected":""}}>
                                                                        VA4 Hoteltour 2021 OOE
                                                                    </option>
                                                                    <option
                                                                        value="VA5 Hoteltour 2021 SBG" {{(!empty(old("herkunftsbez")) && old("herkunftsbez")=="VA5 Hoteltour 2021 SBG") ? "selected":""}}>
                                                                        VA5 Hoteltour 2021 SBG
                                                                    </option>
                                                                    <option
                                                                        value="VA6 Hoteltour 2021 STMK" {{(!empty(old("herkunftsbez")) && old("herkunftsbez")=="VA6 Hoteltour 2021 STMK") ? "selected":""}}>
                                                                        VA6 Hoteltour 2021 STMK
                                                                    </option>
                                                                    <option
                                                                        value="VA7 Hoteltour 2021 TIR" {{(!empty(old("herkunftsbez")) && old("herkunftsbez")=="VA7 Hoteltour 2021 TIR") ? "selected":""}}>
                                                                        VA7 Hoteltour 2021 TIR
                                                                    </option>
                                                                    <option
                                                                        value="VA8 Hoteltour 2021 VBG" {{(!empty(old("herkunftsbez")) && old("herkunftsbez")=="VA8 Hoteltour 2021 VBG") ? "selected":""}}>
                                                                        VA8 Hoteltour 2021 VBG
                                                                    </option>
                                                                    <option
                                                                        value="VA9 Hoteltour 2021 WIEN" {{(!empty(old("herkunftsbez")) && old("herkunftsbez")=="VA9 Hoteltour 2021 WIEN") ? "selected":""}}>
                                                                        VA9 Hoteltour 2021 WIEN
                                                                    </option>
                                                                @endif
                                                                @if($aktionurl=="firma")
                                                                    <option
                                                                        value="VA1 Firmentour 2021 BGLD" {{(!empty(old("herkunftsbez")) && old("herkunftsbez")=="VA1 Firmentour 2021 BGLD") ? "selected":""}}>
                                                                        VA1 Firmentour 2021 BGLD
                                                                    </option>
                                                                    <option
                                                                        value="VA2 Firmentour 2021 KTN" {{(!empty(old("herkunftsbez")) && old("herkunftsbez")=="VA2 Firmentour 2021 KTN") ? "selected":""}}>
                                                                        VA2 Firmentour 2021 KTN
                                                                    </option>
                                                                    <option
                                                                        value="VA3 Firmentour 2021 NOE" {{(!empty(old("herkunftsbez")) && old("herkunftsbez")=="VA3 Firmentour 2021 NOE") ? "selected":""}}>
                                                                        VA3 Firmentour 2021 NOE
                                                                    </option>
                                                                    <option
                                                                        value="VA4 Firmentour 2021 OOE" {{(!empty(old("herkunftsbez")) && old("herkunftsbez")=="VA4 Firmentour 2021 OOE") ? "selected":""}}>
                                                                        VA4 Firmentour 2021 OOE
                                                                    </option>
                                                                    <option
                                                                        value="VA5 Firmentour 2021 SBG" {{(!empty(old("herkunftsbez")) && old("herkunftsbez")=="VA5 Firmentour 2021 SBG") ? "selected":""}}>
                                                                        VA5 Firmentour 2021 SBG
                                                                    </option>
                                                                    <option
                                                                        value="VA6 Firmentour 2021 STMK" {{(!empty(old("herkunftsbez")) && old("herkunftsbez")=="VA6 Firmentour 2021 STMK") ? "selected":""}}>
                                                                        VA6 Firmentour 2021 STMK
                                                                    </option>
                                                                    <option
                                                                        value="VA7 Firmentour 2021 TIR" {{(!empty(old("herkunftsbez")) && old("herkunftsbez")=="VA7 Firmentour 2021 TIR") ? "selected":""}}>
                                                                        VA7 Firmentour 2021 TIR
                                                                    </option>
                                                                    <option
                                                                        value="VA8 Firmentour 2021 VBG" {{(!empty(old("herkunftsbez")) && old("herkunftsbez")=="VA8 Firmentour 2021 VBG") ? "selected":""}}>
                                                                        VA8 Firmentour 2021 VBG
                                                                    </option>
                                                                    <option
                                                                        value="VA9 Firmentour 2021 WIEN" {{(!empty(old("herkunftsbez")) && old("herkunftsbez")=="VA9 Firmentour 2021 WIEN") ? "selected":""}}>
                                                                        VA9 Firmentour 2021 WIEN
                                                                    </option>
                                                                @endif
                                                                @if($aktionurl=="beauty")
                                                                    <option
                                                                        value="VA1 Beautytour 2020 BGLD" {{(!empty(old("herkunftsbez")) && old("herkunftsbez")=="VA1 Beautytour 2020 BGLD") ? "selected":""}}>
                                                                        VA1 Beautytour 2020 BGLD
                                                                    </option>
                                                                    <option
                                                                        value="VA2 Beautytour 2020 KTN" {{(!empty(old("herkunftsbez")) && old("herkunftsbez")=="VA2 Beautytour 2020 KTN") ? "selected":""}}>
                                                                        VA2 Beautytour 2020 KTN
                                                                    </option>
                                                                    <option
                                                                        value="VA3 Beautytour 2020 NÖ" {{(!empty(old("herkunftsbez")) && old("herkunftsbez")=="VA3 Beautytour 2020 NÖ") ? "selected":""}}>
                                                                        VA3 Beautytour 2020 NÖ
                                                                    </option>
                                                                    <option
                                                                        value="VA4 Beautytour 2020 OÖ" {{(!empty(old("herkunftsbez")) && old("herkunftsbez")=="VA4 Beautytour 2020 OÖ") ? "selected":""}}>
                                                                        VA4 Beautytour 2020 OÖ
                                                                    </option>
                                                                    <option
                                                                        value="VA5 Beautytour 2020 SBG" {{(!empty(old("herkunftsbez")) && old("herkunftsbez")=="VA5 Beautytour 2020 SBG") ? "selected":""}}>
                                                                        VA5 Beautytour 2020 SBG
                                                                    </option>
                                                                    <option
                                                                        value="VA6 Beautytour 2020 STMK" {{(!empty(old("herkunftsbez")) && old("herkunftsbez")=="VA6 Beautytour 2020 STMK") ? "selected":""}}>
                                                                        VA6 Beautytour 2020 STMK
                                                                    </option>
                                                                    <option
                                                                        value="VA7 Beautytour 2020 TIR" {{(!empty(old("herkunftsbez")) && old("herkunftsbez")=="VA7 Beautytour 2020 TIR") ? "selected":""}}>
                                                                        VA7 Beautytour 2020 TIR
                                                                    </option>
                                                                    <option
                                                                        value="VA8 Beautytour 2020 VBG" {{(!empty(old("herkunftsbez")) && old("herkunftsbez")=="VA8 Beautytour 2020 VBG") ? "selected":""}}>
                                                                        VA8 Beautytour 2020 VBG
                                                                    </option>
                                                                    <option
                                                                        value="VA9 Beautytour 2020 WIEN" {{(!empty(old("herkunftsbez")) && old("herkunftsbez")=="VA9 Beautytour 2020 WIEN") ? "selected":""}}>
                                                                        VA9 Beautytour 2020 WIEN
                                                                    </option>
                                                                @endif
                                                            </select>
                                                            <div class="invalid-feedback"></div>


                                                        </div>
                                                    </div>

                                                    <div class="col-12">
                                                        <div class="form-group mb-3">
                                                            <label for="kundenunmmer">Kundennummer <small>(falls vorhanden)</small></label>
                                                            <input type="text" maxlength="15"
                                                                   class="form-control {{ ($errors->has("kundennummer")) ? "border-danger" : "" }}"
                                                                   name="kundennummer" id="kundennummer" value="{{ old("kundennummer") }}">
                                                            <div class="invalid-feedback"></div>
                                                        </div>
                                                    </div>
                                                    <div class="col-12">
                                                        <div class="form-group mb-3">
                                                            <label for="firmenzusatzname">Lokalname / Salonname / Hotelname <small>(falls
                                                                    vorhanden)</small></label>
                                                            <input type="text" maxlength="60"
                                                                   class="form-control {{ ($errors->has("firmenzusatzname")) ? "border-danger" : "" }}"
                                                                   name="firmenzusatzname" id="firmenzusatzname" value="{{ old("firmenzusatzname") }}">
                                                            <div class="invalid-feedback"></div>
                                                        </div>
                                                    </div>
                                                    <div class="col-12">
                                                        <div class="form-group mb-3">
                                                            <label for="werbernummer">Werbernummer </label>
                                                            <input type="text" maxlength="15"
                                                                   class="form-control {{ ($errors->has("werbernummer")) ? "border-danger" : "" }}"
                                                                   name="werbernummer" id="werbernummer" value="{{ old("werbernummer") }}">
                                                            <div class="invalid-feedback"></div>
                                                        </div>
                                                    </div>
                                                    <div class="col-12">
                                                        <small>{!! $alerts["T-ALL-01"]->message !!}</small>
                                                    </div>

                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif {{--if($event=="true")--}}


                        <div class="col-12 row-register">
                            <div class="title-wrapper">
                                <h2>Rechtliches</h2>
                            </div>
                        </div>
                        <div class="col-12 ">
                            <div class="card card-bordered mb-4">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-12">
                                            <div class="form-group mb-3">
                                                <div class="custom-control custom-checkbox mb-1">
                                                    <input type="checkbox" class="custom-control-input" value="1" name="agb" id="agb"
                                                           onfocus="this.blur()">
                                                    <label class="custom-control-label {{ ($errors->has("agb")) ? "text-danger" : "" }}"
                                                           for="agb">{!! $agb !!}</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-12">
                                            <div class="form-group mb-3">
                                                <div class="custom-control custom-checkbox mb-1">
                                                    <input type="checkbox" class="custom-control-input" name="bewerbung" id="bewerbung" value="ok"
                                                           onfocus="this.blur()">
                                                    <label class="custom-control-label {{ ($errors->has("bewerbung")) ? "text-danger" : "" }}"
                                                           for="bewerbung">{!! $alerts["A-LEGAL-08"]["message"] !!}</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-12">
                                            <p>
                                                <small>{!! $alerts["A-LEGAL-10"]["message"] !!}</small>
                                            </p>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>


                        <div class="col-12">
                            @if (count($errors) > 0)
                                <div class="alert alert-danger">
                                    <ul>
                                        @foreach ($errors->all() as $error)
                                            <li>{!! $error !!}</li>
                                        @endforeach
                                    </ul>
                                </div>
                            @endif

                        </div>

                        <div class="col-12 text-end pb-5">
                            <div class="form-group mb-3">
                                <button onfocus="this.blur();" type="button" submit="true" loadicon="true"
                                        class="btn  btn-brown btn-lg btn-block d-md-none">
                                    {{ ucfirst(trans($btntext)) }}
                                </button>
                                <button onfocus="this.blur();" type="button" submit="true" loadicon="true"
                                        class="btn  btn-brown btn-lg d-none d-md-inline-block">
                                    {{ ucfirst(trans($btntext)) }}
                                </button>
                            </div>
                        </div>

                    @endif
                </form>

            </div> {{--col-12--}}

        </div> {{--row--}}

    </div>{{--container--}}

@endsection
@section('scripts')
    <script type="text/javascript" src='{{ url("inc/js/moment-with-locales.js") }}'></script>
    <script type="text/javascript" src='{{ url("inc/bootstrap-4-datetimepicker/js/bootstrap-datetimepicker.min.js") }}'></script>
    <script type="text/javascript" src='{{ url("inc/aos/aos.js") }}'></script>
    <script src="{{url('inc/js/bs-custom-file-input.min.js')}}"></script>
    <script type="text/javascript" src='{{ url("inc/js/bowser.js") }}'></script>
    <script src="{{url('inc/js/frontend.js')}}"></script>
@endsection
