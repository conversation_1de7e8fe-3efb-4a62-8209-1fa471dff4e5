@extends('frontend.master')
@section('content')

    <div class="container">
        <div class="row py-3">
            <section class="col-12">
                <h1 class="  fw-normal   title-smaller" style="">EINSTIEG BEI MEDIAPRINT</h1>
                <span class="row flex-column-reverse flex-lg-row">
                    <span class="col-lg-6 col-12 h3 py-3 fw-bold text-start">Offene Stellen</span>
                    <span class="col-lg-6 col-12 text-lg-end my-4 my-lg-0">
                        <img class=" " alt="We are hiring" src="{{url('img/karriere/we_are_hiring.png')}}"
                             style="max-width: 20rem; ">
                    </span>
                </span>
                <p>
                    Unsere aktuellen Stellenangebote der MediaPrint finden Sie hier:
                </p>
            </section>


            <div class="col-lg-4 col-12">
                <div class="bg-brown2 rounded-custom p-2">

                    <div class="row ">
                        <div class="col-12">
                            <b class="pb-1 d-block">Be<PERSON><PERSON><PERSON></b>
                            @foreach($categories as $cat)
                                <div class="py-1 point red-on-hover position-relative "

                                     data-value="{{$cat['id']}}"
                                     data-filter="field"
                                >
                                    <label class="position-relative d-inline-block">

                                        <i class="fal fa-square bg-white text-dark text-dark "></i>
                                        <i class="fas fa-check  position-absolute text-danger checkSign "
                                           data-checked="false"

                                           style="left: 0; bottom: 6px; display:none;"></i>
                                    </label>
                                    <span class="ms-1">{{$cat['name']}}</span>
                                </div>
                            @endforeach
                        </div>

                        <b class="col-12 py-1 mt-1 d-block">Anstellungsart</b>
                        <div class="col-6">
                            <div class="py-1 point red-on-hover "

                                 data-value="Vollzeit"
                                 data-filter="time"
                            >
                                <label class="position-relative d-inline-block">
                                    <i class="fal fa-square bg-white text-dark"></i>
                                    <i class="fas fa-check  position-absolute text-danger checkSign "
                                       data-checked="false"
                                       style="left: 0; bottom: 6px; display:none"></i>
                                </label>
                                <span class="ms-1">Vollzeit</span>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="py-1 point red-on-hover "

                                 data-value="Teilzeit"
                                 data-filter="time"
                            >
                                <label class="position-relative d-inline-block">

                                    <i class="fal fa-square bg-white text-dark"></i>
                                    <i class="fas fa-check  position-absolute text-danger checkSign "
                                       data-checked="false"
                                       style="left: 0; bottom: 6px; display:none"></i>
                                </label>
                                <span class="ms-1">Teilzeit</span>
                            </div>
                        </div>

                        {{--Level--}}
                        <b class="col-12 py-1 mt-1 d-block">Level</b>
                        @foreach($levels as $level)
                            <div class="py-1 point red-on-hover"
                                 data-value="{{$level->title}}"
                                 data-filter="level"
                            >
                                <label class="position-relative d-inline-block">
                                    <i class="fal fa-square bg-white text-dark"></i>
                                    <i class="fas fa-check  position-absolute text-danger checkSign "
                                       data-checked="false"
                                       style="left: 0; bottom: 6px; display:none"></i>
                                </label>
                                <span class="ms-1">{{$level->title}}</span>
                            </div>
                        @endforeach

                        <div class="col-12">
                            <b class="py-1 mt-1 d-block">Bundesland</b>
                            @foreach($federalStates as $loc)
                                <div class="py-1 point red-on-hover"
                                     data-value="{{$loc}}"
                                     data-filter="bundesland"
                                >
                                    <label class="position-relative d-inline-block">
                                        <i class="fal fa-square bg-white text-dark"></i>
                                        <i class="fas fa-check  position-absolute text-danger checkSign "
                                           data-checked="false"
                                           style="left: 0; bottom: 6px; display:none"></i>
                                    </label>
                                    <span class="ms-1">{{$loc}}</span>
                                </div>
                            @endforeach
                        </div>

                        <div class="col-12">
                            <b class="py-1 mt-1 d-block">Firma</b>
                            @foreach($companies as $loc)
                                <div class="py-1 point red-on-hover"
                                     data-value="{{$loc}}"
                                     data-filter="company"
                                >
                                    <label class="position-relative d-inline-block">
                                        <i class="fal fa-square bg-white text-dark"></i>
                                        <i class="fas fa-check  position-absolute text-danger checkSign "
                                           data-checked="false"
                                           style="left: 0; bottom: 6px; display:none"></i>
                                    </label>
                                    <span class="ms-1">{{$loc}}</span>
                                </div>
                            @endforeach
                        </div>


                        {{--<div class="col-12">

                            <b class="py-1 mt-1 d-block">Ort</b>
                            @foreach($locations as $loc)
                                <div class="py-1 point red-on-hover"

                                     data-id="{{$loc}}"
                                     data-filter="location"
                                >
                                    <label class="position-relative d-inline-block">
                                        <i class="fal fa-square bg-white text-dark"></i>
                                        <i class="fas fa-check  position-absolute text-danger checkSign "
                                           data-checked="false"
                                           style="left: 0; bottom: 6px; display:none"></i>
                                    </label>
                                    <span class="ms-1">{{$loc}}</span>
                                </div>
                            @endforeach

                        </div>--}}
                    </div>
                    {{--row--}}
                </div>
            </div>
            {{--col-4 end--}}
            <div class="col-lg-8 col-12 text mt-lg-0 mt-4">

                <table class="table table-striped table-borderless ">
                    <thead>
                    <tr class="fw-bold">
                        <td>Wir suchen</td>
                        {{--                        <td class="d-xxl-table-cell d-none never-wrap">Online seit</td>--}}
                        <td class="d-xxl-table-cell d-none">Firma</td>
                        <td class="d-sm-table-cell d-none">Ort</td>
                        <td class="d-md-table-cell d-none">Art</td>
                        <td></td>
                    </tr>
                    </thead>
                    <tbody class=" text-small-2 ">
                    @foreach($jobs as $job)
                        @if($job instanceof \App\Models\Umantis\Job)

                            <tr class="align-middle point"
                                data-job="{{ $job->id ?? '' }}"
                                data-location="{{ $job->work_location ?? '' }}"
                                data-time="{{ $job->working_time_scope ?? '' }}"
                                data-field="{{ $job->job_category ?? '' }}"
                                data-bundesland="{{ $job->federal_state ?? '' }}"
                                data-company="{{ $job->company ? $job->company->title : '' }}"
                                data-level="{{$job->level ? $job->level->title: ''}}"
                                style="height:3rem;"
                            >

                                <td>{{ $job->job_title ?? 'N/A' }}</td>
                                {{--        <td class="d-xxl-table-cell d-none">{{ $job->publication_date ? $job->publication_date->format('Y-m-d') : 'N/A' }}</td>--}}
                                <td class="d-xxl-table-cell d-none">{{  $job->company ? $job->company->title : '' }}</td>
                                <td class="d-sm-table-cell d-none">{{ $job->work_location ?? 'N/A' }}</td>
                                <td class="d-md-table-cell d-none text-nowrap">

                                    {{  $job->working_time_and_hours }}

                                </td>


                                <td>
                                    <a href="{{ url()->current() . "/". $job->seo_url }}"
                                       id="link-{{ $job->id ?? '' }}" target="_blank">
                                        <i class="fas fa-chevron-right text-danger"></i>
                                    </a>
                                </td>
                            </tr>

                        @endif

                    @endforeach
                    <tr id="alert-no-results" class="d-none">
                        <td colspan="6" class="p-0 bg-white">
                            <div class="alert alert-danger m-0 text-center bg-brown1">
                                Keine Ergenbisse zu Ihrer Suche
                            </div>
                        </td>
                    </tr>
                    </tbody>

                </table>
            </div>
            <!--Initiativbewerbung	-->
            <div class="col-12 mb-2 mt-4">
                <section class=" bg-brown1 py-4 px-5">
                    <img class="w-100 " src="{{url('img/karriere/initiativ-bewerbung.jpg')}}"
                         alt="Initiativbewerbung">
                    <div class="mt-3" id="bewerbungsform">
                        <h1 class="py-3 title-smaller">Initiativbewerbung</h1>
                        <p>
                            Du willst deine Qualifikationen als Fach- und/oder Führungskraft unter Beweis stellen
                            oder deinen Berufseinstieg bei uns
                            starten?
                            Dann
                            freuen wir uns über Deine aussagekräftige Bewerbung!
                        </p>
                        <a class="btn btn-brown mt-2"
                           href="https://karriere.mediaprint.at/Vacancies/InitiativeApplication/CheckLogin/9999"
                           target="_blank">
                            Bewerben
                        </a>
                    </div>
                </section>
            </div>
            <!--Karriere Cards	-->

            @include('frontend.karriere.cards')

        </div>
    </div>

@endsection
@section('scripts')
    <script src="{{url('/inc/js/carrier/carrier-filter.js') . '?'.now()->timestamp}}"></script>
@endsection
