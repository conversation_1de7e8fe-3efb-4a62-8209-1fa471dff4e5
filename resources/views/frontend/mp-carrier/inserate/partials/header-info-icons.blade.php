<div
    class="border-top border-bottom d-flex justify-content-between flex-md-row flex-column py-2 mb-5 border-2 border-dark py-3">
    @if($job->work_location)
        <div class="d-flex align-items-center mb-lg-0 mb-3">
            {{-- <img src="{{url('img/karriere/inserate/icons/location.png')}}" alt="location icon" class="main-icons me-3">--}}

            <svg class="me-3"
                 version="1.1"
                 viewBox="0 0 402.33334 560.22668"
                 xmlns="http://www.w3.org/2000/svg"
                 xmlns:svg="http://www.w3.org/2000/svg"

            >

                <g transform="matrix(1.3333333,0,0,-1.3333333,0,560.22667)">
                    <g transform="scale(0.1)">
                        <path
                            d="m 1508.78,1905.81 c -438.65,-0.1 -794.331,355.41 -794.436,794.06 -0.106,438.65 355.406,794.34 794.056,794.44 438.65,0.11 794.34,-355.4 794.44,-794.05 0,-0.13 0,-0.26 0,-0.39 0,-438.54 -355.51,-794.06 -794.06,-794.06 m 1508.76,794.06 C 3013.7,3533.14 2335.09,4205.53 1501.82,4201.7 673.973,4197.88 3.8125,3527.72 0,2699.87 c 0,-26.84 2.625,-53 4.03516,-79.49 H 0 C 42.2734,2116.29 196.547,1627.99 451.57,1191.12 873.48,476.422 1508.75,0 1508.75,0 c 0,0 635.27,476.391 1057.19,1191.12 255.04,436.91 409.31,925.25 451.57,1429.39 h -4.04 c 1.41,26.49 4.04,52.65 4.04,79.49"
                        />
                    </g>
                </g>
            </svg>


            <span class="fs-3 fw-bold">{{mb_strtoupper(trans($job->work_location), 'UTF-8')}}</span>
        </div>
    @endif

    @if( $job->home_office_option == 'ja')
        <div class="d-flex align-items-center mb-lg-0 mb-3">
            {{--            <img src="{{url('img/karriere/inserate/icons/hybrid.png')}}" alt="hybrid icon" class="main-icons me-3">--}}

            <svg class="me-3"
                 version="1.1"

                 viewBox="0 0 773.35999 520.22668"
                 xmlns="http://www.w3.org/2000/svg"
                 xmlns:svg="http://www.w3.org/2000/svg"

            >

                <g transform="matrix(1.3333333,0,0,-1.3333333,0,520.22667)">
                    <g transform="scale(0.1)">
                        <path
                            d="m 4930.65,1093.72 c -0.01,-44.98 -36.47,-81.44 -81.46,-81.45 H 975.754 c -54.977,0.01 -99.535,44.57 -99.551,99.54 v 2398.3 c 0.016,59.98 48.633,108.6 108.61,108.61 H 4822.16 c 59.98,-0.01 108.6,-48.63 108.61,-108.61 z m 23.04,2808 H 846.52 c -142.547,0 -258.102,-115.56 -258.102,-258.1 V 965.5 c 0,-142.551 115.555,-258.109 258.102,-258.109 h 4107.17 c 142.54,0 258.1,115.558 258.1,258.109 v 0 2678.12 c 0,142.54 -115.56,258.1 -258.1,258.1 v 0"
                        />
                        <path
                            d="M 0,504.328 V 299.648 C 0,134.148 134.16,0 299.652,0 H 5500.52 c 165.49,0 299.65,134.148 299.65,299.648 v 0 204.68 H 0"
                        />
                    </g>
                </g>
            </svg>

            <span class="fs-3 fw-bold">HYBRID</span>
        </div>
    @endif

    @if($job->working_time_scope)
        <div class="d-flex align-items-center">
            {{--    <img src="{{url('img/karriere/inserate/icons/arbeitszeiten.png')}}" alt="arbeitszeiten icon" class="main-icons me-3">--}}

            <svg class="me-3"
                 version="1.1"
                 viewBox="0 0 560.22668 560.22668"
                 xmlns="http://www.w3.org/2000/svg"
                 xmlns:svg="http://www.w3.org/2000/svg"
            >

                <g transform="matrix(1.3333333,0,0,-1.3333333,0,560.22667)">
                    <g transform="scale(0.1)">
                        <path
                            d="m 2210.73,2100.84 c -0.02,-60.67 -49.19,-109.84 -109.86,-109.86 H 1407.3 c -60.67,0.02 -109.84,49.23 -109.81,109.9 0.02,60.64 49.17,109.8 109.81,109.82 h 583.61 v 923.28 c 0,60.67 49.19,109.86 109.86,109.86 60.68,0 109.86,-49.19 109.86,-109.86 z m 1483.03,-109.86 c -60.67,0.02 -109.84,49.23 -109.81,109.9 0.02,60.64 49.17,109.8 109.81,109.82 h 507.95 c -56.34,1075.39 -915.59,1934.66 -1990.98,1991.01 v -507.94 c -0.02,-60.68 -49.23,-109.85 -109.9,-109.82 -60.64,0.02 -109.8,49.18 -109.82,109.82 v 507.94 C 915.641,4145.38 56.3867,3286.17 0,2210.8 h 507.945 c 60.676,0 109.864,-49.19 109.864,-109.86 0,-60.68 -49.188,-109.86 -109.864,-109.86 L 0,1991.08 C 56.3828,915.75 915.578,56.5508 1990.91,0.171875 V 507.949 c 0,60.672 49.19,109.86 109.86,109.86 60.68,0 109.86,-49.188 109.86,-109.86 v 0 V 0 c 1075.35,56.3516 1934.56,915.57 1990.91,1990.91 l -507.78,0.07"

                        />
                    </g>
                </g>
            </svg>

            <span class="fs-3 fw-bold">{{mb_strtoupper(trans($job->working_time_and_hours), 'UTF-8')}}</span>
        </div>
    @endif
</div>
