<!DOCTYPE html>
<html lang="de">
<head>
    <title>{{  env("APP_TITLE") }}</title>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">


    <style>

        @page {
            margin: 0
        }


        html {
            font-size: 14px;
            font-family: sans-serif;
        }


        .fw-bold {
            font-weight: bold;
        }


        .text-center {
            /* //  display: table-cell;*/
            text-align: center;
        }

        .centered-table {
            margin: 0 auto;
            width: 100%;

        }

        .text-center {
            text-align: center;
        }

        .text-end {
            text-align: right;
        }

        .fs-4 {
            font-size: 17px;
        }


        .margin-b {
            margin-bottom: 7px;
        }

        .no-break {

            page-break-inside: avoid;

        }

        ul{
            margin: 0 10px;

            padding: 0 10px;

        }

    </style>
</head>

<body>


<div>


    @if($headerImage)
        <div>
            <img src="{{$headerImage}}" alt="" style="width: 100%">
        </div>
    @endif


    <div style="margin: 0 13px!important;">

        <div class="margin-b">
            <h2>{{mb_strtoupper(trans($job->job_title ?? ""), 'UTF-8')}}</h2>

            <div class="fw-bold">
                {{trans($job->header_text ?? "")}}
            </div>
        </div>


        <table class="centered-table fw-bold fs-4 margin-b ">
            <tr>
                @if($job->work_location)
                    <td>
                        {{mb_strtoupper(trans($job->work_location), 'UTF-8')}}
                    </td>
                @endif

                @if($job->home_office_option == "Ja")
                    <td class="text-center">
                        HYBRID
                    </td>
                @endif

                @if($job->working_time_scope)
                    <td class="text-end">
                        {{mb_strtoupper(trans($job->working_time_scope), 'UTF-8')}}
                    </td>
                @endif
            </tr>
        </table>

        <div class="margin-b">
            <div class="fw-bold fs-4">
                {{$job->expectations_title }}
            </div>
            <div>
                {!! $job->expectations_list !!}
            </div>
            {{--         <table>
                         @foreach($job->tasks as $tasks)
                             <tr class="no-break">
                                 <td>
                                     <div><span>&bull;</span> {{$tasks}}</div>
                                 </td>
                             </tr>
                         @endforeach
                     </table>--}}
        </div>


        {{--
            Section 2
        --}}
        <div class="margin-b">

            <div class="fw-bold fs-4">
                {{$job->requirements_title }}
            </div>
            <div>
                {!! $job->requirements_list !!}
            </div>

            {{--       <div class="fw-bold fs-4">
                       DAS {{($job->anrede == "Du") ? "BRINGST DU" : "BRINGEN SIE"}} MIT:
                   </div>
                   <table>
                       @foreach($job->requirements as $requirement)
                           <tr class="no-break">
                               <td>
                                   <div><span>&bull;</span> {{$requirement}}</div>
                               </td>
                           </tr>
                       @endforeach
                   </table>--}}
        </div>

        {{--
          Section 3
        --}}
        <div class="margin-b">

            <div class="fw-bold fs-4">
                {{$job->offer_title }}
            </div>
            <div>
                @foreach($job->benefits as $bn)
                    <table class="no-break">
                        <tr>
                            <td>
                                &bull;
                                <span class="fw-bold">{{$bn->title}}</span>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                {{$bn->description}}
                            </td>
                        </tr>
                    </table>
                @endforeach

            </div>

            {{--    <div class="fw-bold fs-4">
                    DAS BIETEN WIR {{($job->anrede == "Du") ? "DIR" : "IHNEN"}} MIT:
                </div>

                <div>

                    @php
                        $benefits = $job->benefits;
                    @endphp



                    @if($benefits->count())

                        @foreach($benefits as $bn)
                            <table class="no-break">
                                <tr>
                                    <td>
                                        &bull;
                                        <span class="fw-bold">{{$bn->title}}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        {{$bn->text}}
                                    </td>
                                </tr>
                            </table>
                        @endforeach

                    @elseif(count($job->benefits_original))
                        <table>
                            @foreach($job->benefits_original as $point)
                                <tr class="no-break">
                                    <td>
                                        <div><span>&bull;</span> {{$point}}</div>
                                    </td>
                                </tr>
                            @endforeach
                        </table>
                    @endif
                </div>--}}

        </div>

        <div class="no-break">
            @if($job->call_to_action)
                {!!  $job->call_to_action !!}
            @endif
        </div>


    </div>{{--text content--}}


</div>


</body>

</html>
