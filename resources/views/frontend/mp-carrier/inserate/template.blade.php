@if($job instanceof \App\Models\Umantis\Job)

@php
    $job->anrede = "Du";
@endphp

    <!DOCTYPE html>
<html lang="de">
<head>
    <title>{{  env("APP_TITLE") }}</title>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="_token" content="{!! csrf_token() !!}"/>


    <meta property="og:title" content="{{$job->job_title}}">
    <meta property="og:description" content="{{trans($job->intro)}}">
    <meta property="og:image" content="">
    <meta property="og:url" content="{{url()->current()}}">
    <meta property="og:type" content="website">

    <link rel="stylesheet" href="{{url('inc/css/frontend_fonts.css')}}">
    <link rel="stylesheet" href="{{url('inc/bootstrap-5.2.2/dist/css/bootstrap.min.css')}}">
    <link rel="stylesheet" href="{{ url("inc/fontawesome-pro-6.5.2-min/dist/all.min.css") }}">
    <script src="{{ url("inc/fontawesome-pro-6.5.2-min/dist/all.min.js") }}" type="text/javascript"></script>
    <link rel="stylesheet" type="text/css" href='{{ url("inc/css/frontend.css") }}'/>


    <script src="{{url('inc/bootstrap-5.2.2/dist/js/bootstrap.bundle.min.js')}}"></script>


    <style>

        ul li {
            margin-bottom: 1rem;
        }

        .bg-mp-red {
            background-color: {{$color}};
        }

        .text-mp-red {
            color: {{$color}};
        }

        svg {
            fill: {{$color}}                      !important;
            height: 3rem;
        }


        html {
            font-size: 16px;
        }

        .separator {
            border: .1rem solid #000;
            opacity: 1;
        }

        /*
            Change icon in accordion on collapsed
        */
        .accordion .icon-open {
            display: none;
        }

        .accordion .collapsed .icon-close {
            display: none;
        }

        .accordion .collapsed .icon-open {
            display: block;
        }


        .big-button {
            padding: 1.7rem 1.7rem 1.3rem;
        }

        .main-icons {
            width: 4.8rem;
        }

        .popover {
            max-width: 30rem !important;
        }

        .popover-header {
            color: {{$color}};
            font-weight: bold;
            font-size: 1.3rem;
            text-align: center;
            white-space: nowrap;
        }

        .popover-body {
            text-align: center;
            background-color: #f0f0f0;
            font-size: 1.1rem;
        }

        .custom-inside-container {
            padding: 20px 20px 0;
        }

        @media (min-width: 768px) {
            .custom-inside-container {
                padding: 60px 80px 0;
            }

        }

        .btn-non-styled {
            border: unset;
            background-color: unset;
            padding: unset;
            margin: unset;
        }


        #contact-person--container {
            width: 100%;
            min-width: 320px;
        }


    </style>
</head>

<body>


@include('frontend.mp-carrier.inserate.partials.notifications')
@include('frontend.mp-carrier.inserate.partials.social-share-modal')


@if($job->hasContactPerson && $job->contact_email)

    @include('frontend.mp-carrier.inserate.partials.contact-person-email-form',
        [
            "formAction" => url(request()->segment(1)  . "/job/$job->id/send-email")
        ])
@endif



<!-- Modal -->

<div class="container  position-relative fs-4 " style="max-width: 1400px!important;">

    {{--https://cloudconvert.com/eps-to-svg--}}
    <div class="row">


        <div class="col-12 w-100 position-relative  overflow-hidden px-0">

            <img src="{{$job->header_image}}" alt="{{$job->job_title}}" class="w-100"
                 onerror="this.onerror=null;this.src={!! '\'' . url('img/karriere/inserate/placeholder.png').'\''  !!};"
            >
        </div>

        <div class="col-12">
            <div class="custom-inside-container">

                {{--
                    Title
                --}}
                <h1 class="fw-bold my-4 title-smaller   display-5" id="main-title"
                    {{-- style="max-width:80%;"--}}
                >{{mb_strtoupper(trans($job->job_title), 'UTF-8')}}</h1>


                {{--
                    Header Text
                --}}
                <p class=" mb-5">
                    {!!  trans($job->intro)!!}
                </p>


                {{--
                Icons:  Location, Home Office, Type
                --}}

                @include('frontend.mp-carrier.inserate.partials.header-info-icons', ['job' => $job])



                {{--
                    Sections
                --}}


                <div class="row flex-lg-row-reverse">


                    @if($job->hasContactPerson)

                        <div class="col-lg-4 d-lg-block d-none">
                            <div class=" ps-lg-3 pb-lg-2 pb-5 ">
                                <div id="contact-person--container">
                                    @include('frontend.mp-carrier.inserate.partials.contact-person',['fullName' => $job->contactFullName, 'pic' => $job->contact_image, 'anrede' => $job->anrede, 'phone' => $job->contact_phone])
                                </div>
                            </div>
                        </div>

                    @endif
                    <div class=" @if($job->hasContactPerson) col-lg-8 @endif  col-12">
                        {{--Section 1--}}
                        @include('frontend.mp-carrier.inserate.partials.section',
                                    [
                                        'sectionTitle' => $job->expectations_title,
                                /*        "sectionPoints" => $job->tasks*/
                                        'sectionContent' => $job->expectations_list
                                    ])
                        <hr class="separator my-5">
                        {{--Section 2--}}
                        @include('frontend.mp-carrier.inserate.partials.section',
                                        [
                                            'sectionTitle' =>  $job->requirements_title,
                                /*            "sectionPoints" => $job->requirements*/
                                            'sectionContent' => $job->requirements_list,
                                        ])
                        <hr class="separator my-5">
                    </div>

                </div>


                <div class="row justify-content-between">
                    <section class="col-12">

                        <h2 class="bg-mp-red p-2 pb-0 text-white d-inline-block mb-4"> {{--H2--}}
                            DAS BIETEN WIR {{($job->anrede == "Du") ? "DIR" : "IHNEN"}}:
                        </h2>

                    </section>


                    @if($job->benefits->count())
                        <div class="py-3">
                            @include('frontend.mp-carrier.inserate.partials.benefits',[
                                    "benefits" => $job->benefits,
                                    "benefits_original" => []])
                        </div>
                    @endif
                    @if($job->offer_text)
                        <div class="py-3">
                            {!! $job->offer_text !!}
                        </div>
                    @endif
                </div>


                <section class="block-text my-4">
                    @if($job->footer_text)
                        {{$job->footer_text}}
                    @endif
                </section>


                <div class="fs-3 fw-bold">
                    {!!  $job->call_to_action !!}
                </div>

            </div>


            @if($job->hasContactPerson)
                <div class="d-lg-none d-block">
                    <div class=" ps-lg-3 pb-lg-2 pb-5 ">
                        <div id="contact-person--container">
                            @include('frontend.mp-carrier.inserate.partials.contact-person',
['fullName' => $job->contactFullName, 'pic' => $job->contact_image, 'anrede' => $job->anrede, 'phone' => $job->contact_phone])
                        </div>
                    </div>
                </div>
            @endif


            <div class="mb-5 mt-lg-5 py-5 text-center">
                <a class="big-button bg-mp-red text-white fs-1 fw-bold"
                   href="{{$job->apply_url}}" target="_blank">
                    JETZT BEWERBEN!
                </a>
            </div>

            <hr class="separator">

            <footer class="text-center my-5 py-5">

                {{--               <div class="row">
                                   <div class="col-lg-8 col-12 ps-0 pb-0  text-start" style="max-height:10rem;">
                                       <img
                                           src="{{$job->footer_img}}"
                                           alt="{{$job->company}}"
                                           onerror="this.onerror=null;this.style.display = 'none'"
                                           class="img-fluid" style="max-height: 100%;">
                                   </div>

                               </div>--}}

                <div class="row align-items-center">

                    <div class="col-lg-8 col-12 ps-0 pb-0  text-start" style="max-height:10rem;">
                        <img
                            src="{{$job->footer_img}}"
                            alt="{{$job->company}}"
                            onerror="this.onerror=null;this.style.display = 'none'"
                            class="img-fluid" style="max-height: 100%;">
                    </div>

                    @if($extra_footer_img)
                        <div class="col-lg-4 text-lg-end mt-3 mt-lg-0">
                            <img
                                src="{{$extra_footer_img}}"


                                class="img-fluid" style="max-height: 16rem">
                        </div>
                    @endif

                </div>


            </footer>

        </div> {{--Px-3--}}
    </div> {{--Col--}}


</div>


</div>


<script>


    const popoverTriggerList = document.querySelectorAll('[data-bs-toggle="popover"]')


    const popoverList = [...popoverTriggerList].map(popoverTriggerEl => new bootstrap.Popover(popoverTriggerEl))


    popoverTriggerList.forEach(function (popover) {


        const popoverInstance = bootstrap.Popover.getInstance(popover);

        popover.addEventListener('mouseover', function () {
            if (popoverInstance) {
                popoverInstance.show();
            }

            popoverTriggerList.forEach(function (otherPopover) {
                if (otherPopover !== popover) {
                    const otherPopoverInstance = bootstrap.Popover.getInstance(otherPopover);
                    if (otherPopoverInstance) {
                        otherPopoverInstance.hide();
                    }
                }
            });
        });

        popover.addEventListener('click', function () {
            if (popoverInstance) {
                popoverInstance.toggle();
            }

            popoverTriggerList.forEach(function (otherPopover) {
                if (otherPopover !== popover) {
                    const otherPopoverInstance = bootstrap.Popover.getInstance(otherPopover);
                    if (otherPopoverInstance) {
                        otherPopoverInstance.hide();
                    }
                }
            });
        });

        popover.addEventListener('mouseout', function () {
            if (popoverInstance) {
                popoverInstance.hide();
            }
        });
    });

    /**/

    document.querySelectorAll('.disable-submit-on-send').forEach(form => {
        form.addEventListener('submit', () => {
            form.querySelectorAll(`[type=submit]`).forEach((x) => {
                x.textContent = 'WIRD GESENDET'
                x.disabled = true;
            })
        })
    })


</script>

<script>
    @if(!empty($job->benefits_string))
    console.log('%cBENEFITS:{!! json_encode($job->benefits_string) !!}', 'background-color: #008000; color: #FFFFFF;');
    @endif
</script>

</body>

</html>
@endif
