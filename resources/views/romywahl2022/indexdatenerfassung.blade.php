@extends('romywahl2022.master')


@section('content')
    @if(env("APP_ENV") != "production" || intval(now()->format("YmdHis")) >= 20220213220000)
        <style>
            .container-fluid {
                max-width: 1600px;
                margin: 50px auto;
            }

            #sternen-gruppe-1 {
                background-image: none;
                background-color: #11100B;
            }

            .portrait-wrapper .form-check {
                position: absolute;
                top: 10px;
                right: 10px;
            }

            .portrait-wrapper .form-check input[type=radio] {
                transform: scale(3);
                box-shadow: none !important;
                cursor: pointer;
                border-color: #A16119 !important;
            }
        </style>


        <form action="{{ url("wahl2022/datenerfassung") }}" id="romyForm" name="romyForm" method="post">
            {{ csrf_field() }}

            @if (count($errors) > 0)
                <div class="row">
                    <div class="col-12 mb-3">
                        <div class="alert alert-warning">
                            <ul class="fa-ul mb-0">
                                @foreach ($errors->all() as $error)
                                    <li>
                                        <span class="fa-li"><i class="fas fa-exclamation-triangle"></i></span>{!! $error  !!}
                                    </li>
                                @endforeach
                            </ul>
                        </div>
                    </div>
                </div>
            @endif

            @if(Session::has('message'))
                <div class="row">
                    <div class="col-12 mb-3">
                        <div class="alert alert-success">
                            {!! Session::get('message') !!}
                        </div>
                    </div>
                </div>
            @endif

            <div class="row">
                @foreach($romyKategorien as $hkey => $hvalue)
                    <div class="col-12 col-md-6 px-5">
                        <div class="row">
                            <div class="col-12">
                                <div class="title-wrapper">
                                    <h2>{{ $hkey }}. {{ $hvalue }}</h2>
                                </div>
                            </div>
                        </div>
                        <div class="row row-cols-1 row-cols-md-5 mt-4">
                            @foreach($romyAuswahlen[$hkey] as $skey => $svalue)
                                <div class="offset-2 col-8 offset-md-0 col-md text-center mb-5 portrait-wrapper">
                                    <img src="{{ url("img/romywahl2022/portraits/".$svalue["pic"]) }}?v2" class="d-block w-100 mb-2 romy-portrait"/>
                                    {!! str_replace(";", "<br>", $svalue["name"]) !!}
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="selection{{$hkey}}" id="selection{{$hkey}}{{$skey}}" value="{{ $skey }}" {{ (old("selection".$hkey) == $skey) ? "checked" : "" }}>
                                        <label class="form-check-label" for="selection{{$hkey}}{{$skey}}"></label>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endforeach
            </div>


            <div class="row mt-5">
                <div class="col-12 text-center">
                    <button class="btn btn-warning btn-lg">Absenden</button>
                </div>
            </div>
        </form>
    @endif
@endsection
