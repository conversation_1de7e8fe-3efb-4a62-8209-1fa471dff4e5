@extends('romywahl2022.master')

@section('content')
    <!-- LIVE VERSION -->
    @if(env("APP_ENV") != "production" || intval(now()->format("YmdHis")) >= 20220213220000)
        <div class="row">
            <div class="col-12 mt-4">
                <div class="title-wrapper">
                    <h2>{{ $romyKategorie }}</h2>
                </div>
            </div>
        </div>

        <form action="{{ url("wahl2022/".$step) }}" id="romyForm" name="romyForm" method="post">
            {{ csrf_field() }}

            <div class="row row-cols-1 row-cols-md-5 mt-4">
                @foreach($romyAuswahl as $key => $value)
                    <div class="offset-2 col-8 offset-md-0 col-md text-center mb-5 portrait-wrapper">
                        <img src="{{ url("img/romywahl2022/portraits/".$value["pic"]) }}?v2" class="d-block w-100 mb-2 romy-portrait"/>
                        <div class="portrait-name">{!! str_replace(";", "<br>", $value["name"]) !!}</div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="selection" id="selection{{$key}}" value="{{ $key }}" {{ ((key_exists($step, $voting) && $voting[$step] == $key) || old("selection") == $key) ? "checked" : "" }}>
                            <label class="form-check-label" for="selection{{$key}}"></label>
                        </div>
                    </div>
                @endforeach
            </div>

            <div class="row">
                @if($captcha)
                    <div class="col-12 mb-3">
                        <div class="captcha">
                            <span>{!! captcha_img("flat") !!}</span>
                            <button type="button" class="btn btn-warning" id="captchaReload">
                                &#x21bb;
                            </button>
                        </div>
                    </div>
                    <div class="col-12 col-md-6">
                        <input type="text" name="captcha" id="captcha" class="form-control" placeholder="Sicherheitscode eingeben">
                    </div>
                @endif
            </div>

            <div class="row mt-5">
                @if (count($errors) > 0)
                    <div class="col-12 mb-3">
                        <div class="alert alert-warning">
                            <ul class="fa-ul mb-0">
                                @foreach ($errors->all() as $error)
                                    <li>
                                        <span class="fa-li"><i class="fas fa-exclamation-triangle"></i></span>{!! $error  !!}
                                    </li>
                                @endforeach
                            </ul>
                        </div>
                    </div>
                @endif

                <div class="col-12 text-md-end d-md-none">
                    <div class="d-grid d-md-block mb-3">
                        <button class="btn btn-warning btn-lg">{{ $nextButton["text"] }}
                            <i class="fas fa-caret-right fa-fw"></i></button>
                    </div>
                </div>
                <div class="col-12 col-md-4">
                    @if(!empty($step) && $step > 1)
                        <div class="d-grid d-md-block mb-3">
                            <a href="{{ url("wahl2022/".($step - 1)) }}" class="btn btn-link btn-lg">
                                <i class="fas fa-caret-left fa-fw"></i> Vorherige Kategorie
                            </a>
                        </div>
                    @endif
                </div>
                <div class="col-12 col-md-4 text-center d-none d-md-flex align-items-center justify-content-center step-counter mb-3">
                    Kategorie {{ $step }} von {{ $step_amount }}
                </div>
                <div class="col-12 col-md-4 text-md-end d-none d-md-block">
                    <div class="d-grid d-md-block">
                        <button class="btn btn-warning btn-lg">{{ $nextButton["text"] }}
                            <i class="fas fa-caret-right fa-fw"></i></button>
                    </div>
                </div>
            </div>
        </form>

        <div class="row logo-wrapper mt-3 mt-md-5">
            <div class="col-3 col-md">
                <a href="https://tv.orf.at/Romy" target="_blank">
                    <img src="{{ url("img/romywahl2022/logos/01_ORF.png") }}?v3" alt="ORF" class="d-block w-100"/>
                </a>
            </div>
            <div class="col-3 col-md">
                <a href="https://www.post.at/" target="_blank">
                    <img src="{{ url("img/romywahl2022/logos/02_Post.png") }}?v3" alt="Post" class="d-block w-100"/>
                </a>
            </div>
            <div class="col-3 col-md">
                <a href="https://www.steffl-vienna.at/" target="_blank">
                    <img src="{{ url("img/romywahl2022/logos/03_Steffl.png") }}?v3" alt="Steffl" class="d-block w-100"/>
                </a>
            </div>
            <div class="col-3 col-md">
                <a href="https://amazon.de/PrimeVideo" target="_blank">
                    <img src="{{ url("img/romywahl2022/logos/04_Prime.png") }}?v3" alt="Prime Video" class="d-block w-100"/>
                </a>
            </div>
            <div class="col-3 col-md">
                <a href="https://sky.at/originals" target="_blank">
                    <img src="{{ url("img/romywahl2022/logos/05_Sky.png") }}?v3" alt="Sky" class="d-block w-100"/>
                </a>
            </div>
            <div class="col-3 col-md">
                <a href="https://www.wempe.at/" target="_blank">
                    <img src="{{ url("img/romywahl2022/logos/06_Wempe.png") }}?v3" alt="Wempe" class="d-block w-100"/>
                </a>
            </div>
            <div class="col-3 col-md">
                <a href="https://www.lavazza.at/" target="_blank">
                    <img src="{{ url("img/romywahl2022/logos/07_Lavazza.png") }}?v3" alt="Lavazza" class="d-block w-100"/>
                </a>
            </div>
            <div class="col-3 offset-md-0 col-md">
                <a href="https://kurier.at/" target="_blank">
                    <img src="{{ url("img/romywahl2022/logos/08_Kurier.png") }}?v3" alt="KURIER" class="d-block w-100"/>
                </a>
            </div>
        </div>
    @endif
@endsection
