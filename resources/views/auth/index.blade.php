@extends('backend.master')
@section('content')
    <div class="row">


        <div class="col-12  mt-4 w-100 d-flex justify-content-center">
            <div class="card border" style="max-width: 40rem;">
                <form class="card-body" action="{{url('login')}}" method="post">
                    @csrf
                    @method('POST')

                    <h3 class="card-title mb-4">Anmelden</h3>



                    <p>
                        Durch Klicken auf die "Anmelden" Schaltfläche werden Sie zu unserem Anmeldeserver weitergeleitet.
                    </p>

                    <div class="form-check form-switch mb-1">
                        <input type="checkbox" class="form-check-input" id="remember_token" value="checked"
                               name="remember_token">
                        <label class="custom-control-label" for="remember_token">angemeldet bleiben </label>
                    </div>

       {{--             <div class="d-none">
                        {{$loginUrl}}
                    </div>

                    <div class="d-none">
                        {{$x}}
                    </div>--}}


                    <div class="d-flex justify-content-between align-items-center">
                        <div class="pt-3">
                            <a  href="{{url('login-legacy')}}" class="text-muted btn-block mt-3 ">LEGACY LOGIN</a>
                        </div>
                        <div>
                            <button type="submit" class="btn btn-success btn-block mt-3">ANMELDEN</button>
                        </div>
                    </div>




                </form>
            </div>
        </div>
    </div>
    <small class="d-block  text-center mt-2">IP: {{\Request::ip()}}</small>



    @if(session()->get("redirectUrl"))
        <div class="col-12 mt-3 text-center">
            Sie wollen zu {{session()->get("redirectUrl")}}
        </div>
    @endif

@endsection
