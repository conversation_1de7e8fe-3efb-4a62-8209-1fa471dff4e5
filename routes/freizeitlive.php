<?php

    use Illuminate\Support\Facades\Route;

    Route::get('/', [\App\Http\Controllers\Freizeitlive\FreizeitLiveController::class, 'index']);

    Route::get('ip', 'App\Http\Controllers\Frontend\PageController@getIp');
    Route::get('cookiepolicy', function(){
        return view('freizeitlive.cookiepolicy');
    });

    Route::get('impressionen', [\App\Http\Controllers\Freizeitlive\FreizeitLiveController::class, 'impressionen']);
    Route::get('show-me-all', [\App\Http\Controllers\Freizeitlive\FreizeitLiveController::class, 'index']);

    // erlebnisstation
    Route::get('post/{id?}/{seoUrl?}', [\App\Http\Controllers\Freizeitlive\FreizeitLiveController::class, 'showPost']);
    Route::get('highlights/{id?}/{seoUrl?}', [\App\Http\Controllers\Freizeitlive\FreizeitLiveController::class, 'showHighlight']);
    Route::get('speaker/{id?}/{seoUrl?}', [\App\Http\Controllers\Freizeitlive\FreizeitLiveController::class, 'showSpeaker']);



    Route::get('programm/{selected?}', [\App\Http\Controllers\Freizeitlive\FreizeitLiveController::class, 'program'])->where('selected', '[0-9]+');
