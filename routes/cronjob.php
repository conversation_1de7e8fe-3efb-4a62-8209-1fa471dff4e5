<?php

use App\Http\Controllers\Frontend\CronjobController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\WebChat\CronjobController as WebChatCronjob;


//Allgemein

Route::get("cronjob/send-errormail", [CronjobController::class, 'sendErrormail']);

//Cronjob Mediathek Importer

Route::get("cronjob/import/{param?}", [CronjobController::class, "importer"]);
Route::get("cronjob/sync-filter/{param?}", [CronjobController::class, "syncFilter"]);


Route::get("cronjob/mediathek-create-covers", [CronjobController::class, "createReplaceMediathekCovers"]);


Route::get("cronjob/umantis-jobs-update", [CronjobController::class, 'umantisJobsUpdate']);


// WebChat

Route::get("cronjob/delete-old-agents", [WebChatCronjob::class, 'markDisabledAgents']);
Route::get("cronjob/delete-old-chats", [WebChatCronjob::class, 'deleteOldChats']);


Route::get("cronjob/webchat-statistics-update", [WebChatCronjob::class, 'statisticsUpdate']);





