<?php

    use App\Http\Controllers\ZeigProfilAward\FrontendController;
    use Illuminate\Support\Facades\Route;

    Route::get('/', [FrontendController::class, "index"])->name('index');
    Route::get('cookiepolicy', [FrontendController::class, "cookiepolicy"]);
    Route::get('einreichen/{cat?}/{seo?}', [FrontendController::class, "einreichenIndex"]);
    Route::post('einreichen', [FrontendController::class, "einreichenPost"]);
    Route::get('/jury-voting/{hash?}', [FrontendController::class, "index"]);



    Route::post('voten', [FrontendController::class, "vote"]);
    Route::post('jury_voten', [FrontendController::class, "vote_jury"]);
    Route::get('download/{submit}', [FrontendController::class, "download"])->name('download.submit');
