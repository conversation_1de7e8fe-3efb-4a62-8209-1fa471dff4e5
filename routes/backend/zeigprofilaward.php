<?php

    use App\Http\Controllers\ZeigProfilAward\BackendController;
    use App\Http\Controllers\ZeigProfilAward\BackendLinkController;
    use Illuminate\Support\Facades\Route;
    use Illuminate\Support\Str;

    Route::get("backend/zeigprofilaward", [BackendController::class, 'index']);

    Route::get("backend/zeigprofilaward/category", [BackendController::class, 'categoryIndex']);
    Route::post("backend/zeigprofilaward/category", [BackendController::class, 'categoryPost']);

    Route::get("backend/zeigprofilaward/homepage", [BackendController::class, 'homepageIndex']);
    Route::post("backend/zeigprofilaward/homepage", [BackendController::class, 'homepagePost']);

    foreach(['partner'] as $section){
        Route::delete("backend/zeigprofilaward/$section/{id}/delete", [BackendController::class, Str::camel($section).'Delete']);
        Route::get("backend/zeigprofilaward/$section", [BackendController::class, Str::camel($section).'Index']);
        Route::post("backend/zeigprofilaward/$section", [BackendController::class, Str::camel($section).'Create']);
        Route::get("backend/zeigprofilaward/$section/{id}", [BackendController::class, Str::camel($section).'Show']);
        Route::post("backend/zeigprofilaward/$section/{id}", [BackendController::class, Str::camel($section).'Update']);
        Route::get("backend/zeigprofilaward/$section/{id}/active/{active}", [BackendController::class, Str::camel($section).'Active']);
        Route::post("backend/ajax/zeigprofilaward/$section/sort", [BackendController::class, Str::camel($section).'AjaxSort']);

        Route::get("backend/zeigprofilaward/$section/{id}/delete-image/{imagenumber}", [BackendController::class, Str::camel($section).'DeleteImage'])->where("imagenumber", "one|two");
    }


	Route::get('backend/zeigprofilaward/submits', [BackendController::class, 'submitsIndex']);
	Route::get('backend/zeigprofilaward/submits/{submit}', [BackendController::class, 'submitsShow']);


    Route::post("backend/zeigprofilaward/submits/{submit}/set_public_vote", [BackendController::class, 'publicVotingFreigabe']);
    Route::post("backend/zeigprofilaward/submits/{submit}/set_jury_vote", [BackendController::class, 'juryVotingFreigabe']);
    Route::post("backend/zeigprofilaward/submits/{submit}/set_epu_jury_vote", [BackendController::class, 'epuVotingFreigabe']);

    Route::post("backend/zeigprofilaward/submits/{submit}/update", [BackendController::class, 'submitDataUpdate']);

    Route::get("backend/zeigprofilaward/evaluation", [BackendController::class, 'submitAuswertungen']);
    Route::get("backend/zeigprofilaward/evaluation-jury", [BackendController::class, 'submitJuryAuswertungen']);

    Route::get('backend/zeigprofilaward/export/email', [BackendController::class, 'export_excel'])->name('zeigprofil.export.excel');

