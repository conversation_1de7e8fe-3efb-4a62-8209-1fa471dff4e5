<?php

	use App\Http\Controllers\Freizeitlive\FreizeitLiveBackendController;
	use App\Http\Controllers\Freizeitlive\FreizeitLiveBackendLinkController;
	use Illuminate\Support\Facades\Route;
	use Illuminate\Support\Str;

	Route::get("backend/freizeitlive", [FreizeitLiveBackendController::class, 'select']);
	Route::post("backend/ajax/freizeitlive-link-sort", [FreizeitLiveBackendLinkController::class, 'ajaxSort']);

	foreach(['post', 'partner', 'post-type', 'speaker'] as $section){

    if (in_array($section, ['post', 'speaker'])) {
        /*Links*/
        Route::get("backend/freizeitlive/$section/{model_id}/link", [FreizeitLiveBackendLinkController::class, 'index']);
        Route::post("backend/freizeitlive/$section/{model_id}/link", [FreizeitLiveBackendLinkController::class, 'create']);
        Route::get("backend/freizeitlive/$section/{model_id}/link/{id}", [FreizeitLiveBackendLinkController::class, 'show']);
        Route::get("backend/freizeitlive/$section/{model_id}/link/{id}/active/{active}", [FreizeitLiveBackendLinkController::class, 'active']);
        Route::post("backend/freizeitlive/$section/{model_id}/link/{id}", [FreizeitLiveBackendLinkController::class, 'update']);
        Route::delete("backend/freizeitlive/$section/{model_id}/link/{id}/delete", [FreizeitLiveBackendLinkController::class, 'delete']);
    }

		Route::delete("backend/freizeitlive/$section/{id}/delete", [FreizeitLiveBackendController::class, Str::camel($section).'Delete']);
		Route::get("backend/freizeitlive/$section", [FreizeitLiveBackendController::class, Str::camel($section).'Index']);
		Route::post("backend/freizeitlive/$section", [FreizeitLiveBackendController::class, Str::camel($section).'Create']);
		Route::get("backend/freizeitlive/$section/{id}", [FreizeitLiveBackendController::class, Str::camel($section).'Show']);
		Route::post("backend/freizeitlive/$section/{id}", [FreizeitLiveBackendController::class, Str::camel($section).'Update']);
		Route::get("backend/freizeitlive/$section/{id}/active/{active}", [FreizeitLiveBackendController::class, Str::camel($section).'Active']);
		Route::post("backend/ajax/freizeitlive/$section/sort", [FreizeitLiveBackendController::class, Str::camel($section).'AjaxSort']);

		Route::get("backend/freizeitlive/$section/{id}/delete-image/{imagenumber}", [FreizeitLiveBackendController::class, Str::camel($section).'DeleteImage'])->where("imagenumber", "one|two");
	}

	Route::get("backend/freizeitlive/program", [FreizeitLiveBackendController::class, 'programIndex']);
	Route::post("backend/freizeitlive/program/{program}", [FreizeitLiveBackendController::class, 'programPost']);
	Route::delete("backend/freizeitlive/program/{program}/delete", [FreizeitLiveBackendController::class, 'programDelete']);
	Route::post("backend/freizeitlive/program", [FreizeitLiveBackendController::class, 'programPostCreate']);
	Route::get("backend/freizeitlive/program/{program}", [FreizeitLiveBackendController::class, 'programShow']);

    Route::get("backend/freizeitlive/news", [FreizeitLiveBackendController::class, 'news']);
    Route::get("backend/freizeitlive/news/{news}", [FreizeitLiveBackendController::class, 'newsShow']);
    Route::delete("backend/freizeitlive/news/{news}/delete", [FreizeitLiveBackendController::class, 'newsDelete']);
    Route::post("backend/freizeitlive/news/{news}", [FreizeitLiveBackendController::class, 'newsUpdate']);
    Route::post("backend/freizeitlive/news", [FreizeitLiveBackendController::class, 'newsCreate']);
    Route::post("backend/ajax/freizeitlive/news/sort", [FreizeitLiveBackendController::class, 'newsAjaxSort']);

    Route::post("backend/freizeitlive/contents", [FreizeitLiveBackendController::class, 'contentsPost']);
