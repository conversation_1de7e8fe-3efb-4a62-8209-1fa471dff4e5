<?php


use App\Http\Controllers\Speakout\GalleryBackendController;
use App\Http\Controllers\Speakout\SpeakOutBackendController;
use Illuminate\Support\Facades\Route;


Route::get("backend/speakout", [SpeakOutBackendController::class, 'select']);

Route::get("backend/speakout/homepage", [SpeakOutBackendController::class, 'homepage']);
Route::post("backend/speakout/homepage", [SpeakOutBackendController::class, 'homepagePost']);

Route::get("backend/speakout/purposes", [SpeakOutBackendController::class, 'purposes']);
Route::post("backend/speakout/purposes", [SpeakOutBackendController::class, 'purposesPost']);

Route::get("backend/speakout/rs", [SpeakOutBackendController::class, 'rs']);
Route::post("backend/speakout/rs", [SpeakOutBackendController::class, 'rsPost']);

Route::get("backend/speakout/speaker", [SpeakOutBackendController::class, 'speakers']);
Route::get("backend/speakout/speaker/{speaker}", [SpeakOutBackendController::class, 'speaker']);
Route::delete("backend/speakout/speaker/{speaker}/delete", [SpeakOutBackendController::class, 'speakerDelete']);
Route::post("backend/speakout/speaker/{speaker}", [SpeakOutBackendController::class, 'speakerUpdate']);
Route::post("backend/speakout/speaker", [SpeakOutBackendController::class, 'speakerCreate']);




Route::get("backend/speakout/news", [SpeakOutBackendController::class, 'news']);
Route::get("backend/speakout/news/{news}", [SpeakOutBackendController::class, 'newsShow']);
Route::delete("backend/speakout/news/{news}/delete", [SpeakOutBackendController::class, 'newsDelete']);
Route::post("backend/speakout/news/{news}", [SpeakOutBackendController::class, 'newsUpdate']);
Route::post("backend/speakout/news", [SpeakOutBackendController::class, 'newsCreate']);
Route::post("backend/ajax/speakout/news/sort", [SpeakOutBackendController::class, 'newsAjaxSort']);

Route::get("backend/speakout/green-world", [SpeakOutBackendController::class, 'greenWorld']);
Route::get("backend/speakout/green-world/{greenWorld}", [SpeakOutBackendController::class, 'greenWorldShow']);
Route::delete("backend/speakout/green-world/{greenWorld}/delete", [SpeakOutBackendController::class, 'greenWorldDelete']);
Route::post("backend/speakout/green-world/{greenWorld}", [SpeakOutBackendController::class, 'greenWorldUpdate']);
Route::post("backend/speakout/green-world", [SpeakOutBackendController::class, 'greenWorldCreate']);


Route::delete("backend/speakout/partner/{partner}/delete", [SpeakOutBackendController::class, 'partnerDelete']);
Route::get("backend/speakout/partner", [SpeakOutBackendController::class, 'partnerIndex']);
Route::post("backend/speakout/partner", [SpeakOutBackendController::class, 'partnerCreate']);
Route::get("backend/speakout/partner/{partner}", [SpeakOutBackendController::class, 'partnerShow']);
Route::post("backend/speakout/partner/{partner}", [SpeakOutBackendController::class, 'partnerUpdate']);
Route::post("backend/ajax/speakout/partner/sort", [SpeakOutBackendController::class, 'partnerAjaxSort']);

Route::get("backend/speakout/document", [SpeakOutBackendController::class, 'docs']);
Route::post("backend/speakout/document", [SpeakOutBackendController::class, 'docsPost']);


Route::get("backend/speakout/program", [SpeakOutBackendController::class, 'programIndex']);
Route::post("backend/speakout/program/{program}", [SpeakOutBackendController::class, 'programPost']);
Route::delete("backend/speakout/program/{program}/delete", [SpeakOutBackendController::class, 'programDelete']);
Route::post("backend/speakout/program", [SpeakOutBackendController::class, 'programPostCreate']);

Route::post("backend/speakout/contents", [SpeakOutBackendController::class, 'contentsPost']);


Route::get("backend/speakout/press", [SpeakOutBackendController::class, 'pressAll']);
Route::get("backend/speakout/press/new", [SpeakOutBackendController::class, 'pressNew']);
Route::get("backend/speakout/press/{speakoutPress}", [SpeakOutBackendController::class, 'pressShow']);
Route::post("backend/speakout/press/{speakoutPress}", [SpeakOutBackendController::class, 'pressShowPost']);
Route::delete("backend/speakout/press/{speakoutPress}/delete", [SpeakOutBackendController::class, 'pressDelete']);


// Test

Route::get("backend/speakout/test", [SpeakOutBackendController::class, 'test']);
Route::get("backend/speakout/gallery", [GalleryBackendController::class, 'index']);
Route::get("backend/speakout/gallery/{id}", [GalleryBackendController::class, 'show'])->where('id', '([0-9]+|new)');
Route::post("backend/speakout/gallery/{id}", [GalleryBackendController::class, 'store'])->where('id', '([0-9]+|new)');
Route::delete("backend/speakout/gallery/{id}/delete", [GalleryBackendController::class, 'delete']) ;
Route::post("backend/ajax/speakout/gallery/displayed-images", [GalleryBackendController::class, 'ajaxDisplayedImages']);
