<?php

/*
|--------------------------------------------------------------------------
| Backend Routes, setzen Login voraus
|--------------------------------------------------------------------------
*/


use App\Http\Controllers\Backend\LoginController;
use App\Http\Controllers\Mediathek\MediathekApiController;
use Illuminate\Support\Facades\Route;


//Frontend das Login benötigt


// Admin Start
Route::get('backend', [App\Http\Controllers\Backend\PageController::class, 'index']);




/*
 * Insertion Manager -> Mp Inserate manager
 */
Route::get('backend/insertion-manager', [\App\Http\Controllers\JobAdsManager\JobAdsManagerController::class, 'index']);
Route::get('backend/insertion-manager/benefit/{id?}', [\App\Http\Controllers\JobAdsManager\BenefitController::class, 'form']);
Route::post('backend/insertion-manager/benefit/{id?}', [\App\Http\Controllers\JobAdsManager\BenefitController::class, 'save']);
Route::delete('backend/insertion-manager/benefit/{id}/delete', [\App\Http\Controllers\JobAdsManager\BenefitController::class, 'delete']);
Route::post('backend/ajax/insertion-manager/sort/benefit', [\App\Http\Controllers\JobAdsManager\BenefitController::class, 'ajaxSort']);
Route::get('backend/insertion-manager/person/{id?}', [\App\Http\Controllers\JobAdsManager\ContactPersonController::class, 'form']);
Route::post('backend/insertion-manager/person/{id?}', [\App\Http\Controllers\JobAdsManager\ContactPersonController::class, 'save']);
Route::delete('backend/insertion-manager/person/{id}/delete', [\App\Http\Controllers\JobAdsManager\ContactPersonController::class, 'delete']);

Route::get('backend/monitor-{medium}', [App\Http\Controllers\Monitor\SliderController::class, 'gallery']);

Route::post('backend/monitor-{medium}/edit', [App\Http\Controllers\Monitor\SliderController::class, 'edit']);
Route::post('backend/monitor-{medium}/create', [App\Http\Controllers\Monitor\SliderController::class, 'create']);
Route::post('backend/monitor-{medium}/delete', [App\Http\Controllers\Monitor\SliderController::class, 'delete']);

// Markenportfolio
Route::get('backend/brandportfolios', 'App\Http\Controllers\Backend\BrandPortfolioController@index');
Route::get('backend/brandportfolios/create', 'App\Http\Controllers\Backend\BrandPortfolioController@store');
Route::get('backend/brandportfolios/{brand}/edit', 'App\Http\Controllers\Backend\BrandPortfolioController@edit');
Route::put('backend/brandportfolios/{brand}', 'App\Http\Controllers\Backend\BrandPortfolioController@update');
Route::get('backend/brandportfolios/{brand}/delete', 'App\Http\Controllers\Backend\BrandPortfolioController@delete');
Route::get('backend/brandportfolios/{brand}/restore', 'App\Http\Controllers\Backend\BrandPortfolioController@restore');

// Admin User
Route::get('backend/user', 'App\Http\Controllers\Backend\UserController@index');
Route::get('backend/user/{user}/edit', 'App\Http\Controllers\Backend\UserController@edit');
Route::put('backend/user/{user}', 'App\Http\Controllers\Backend\UserController@update');
Route::get('backend/user/{user}/delete', 'App\Http\Controllers\Backend\UserController@delete');
Route::get('backend/user/{user}/restore', 'App\Http\Controllers\Backend\UserController@restore');

// Logs
Route::get('backend/logs', function (){
    return view("backend.logging.index");
});
Route::get('backend/logs/maillog', 'App\Http\Controllers\Backend\LogController@mail');
Route::post('backend/logs/maillog', 'App\Http\Controllers\Backend\LogController@mail');
Route::get('backend/logs/wserror', 'App\Http\Controllers\Backend\LogController@wserror');
Route::post('backend/logs/wserror', 'App\Http\Controllers\Backend\LogController@wserror');
Route::get('backend/logs/wsaction/{orderId?}', 'App\Http\Controllers\Backend\LogController@wsaction');
Route::post('backend/logs/wsaction', 'App\Http\Controllers\Backend\LogController@wsaction');


Route::get('backend/mediathek/{any?}', [MediathekApiController::class, 'mediathek_view'])->where('any', '.*');


Route::post("backend/ajax/link-bookmark", "App\Http\Controllers\Backend\ShortlabelController@linkBookmark");

// Hinweistext-Verwaltung
Route::get('backend/alertmessages', 'App\Http\Controllers\Backend\AlertMessageController@index');
Route::get('backend/alertmessages/{alertMessage}/edit', 'App\Http\Controllers\Backend\AlertMessageController@edit');
Route::put('backend/alertmessages/{alertMessage}', 'App\Http\Controllers\Backend\AlertMessageController@update');

// Settings -> Datenschutz-Verwaltung
Route::get('backend/dataprivacies', 'App\Http\Controllers\Backend\DataPrivacyController@index');
Route::get('backend/dataprivacies/create', 'App\Http\Controllers\Backend\DataPrivacyController@store');
Route::get('backend/dataprivacies/{dataprivacy}/edit', 'App\Http\Controllers\Backend\DataPrivacyController@edit');
Route::put('backend/dataprivacies/{dataprivacy}', 'App\Http\Controllers\Backend\DataPrivacyController@update');
Route::get('backend/dataprivacies/{dataprivacy}/delete', 'App\Http\Controllers\Backend\DataPrivacyController@delete');
Route::get('backend/dataprivacies/{dataprivacy}/download', 'App\Http\Controllers\Backend\DataPrivacyController@download');
Route::post('backend/dataprivacies/ajax/sortdataprivacies', 'App\Http\Controllers\Backend\DataPrivacyController@sortDataPrivacy');

// Settings -> Impressum-Verwaltung
Route::get('backend/imprints', 'App\Http\Controllers\Backend\ImprintController@index');
Route::get('backend/imprints/{medium}/edit', 'App\Http\Controllers\Backend\ImprintController@edit');
Route::put('backend/imprints/{medium}', 'App\Http\Controllers\Backend\ImprintController@update');

// Settings -> AGB-Verwaltung
Route::get('backend/agbs', 'App\Http\Controllers\Backend\AgbController@index');
Route::get('backend/agbs/create', 'App\Http\Controllers\Backend\AgbController@store');
Route::get('backend/agbs/{agb}/edit', 'App\Http\Controllers\Backend\AgbController@edit');
Route::put('backend/agbs/{agb}', 'App\Http\Controllers\Backend\AgbController@update');
Route::get('backend/agbs/{agb}/delete', 'App\Http\Controllers\Backend\AgbController@delete');
Route::get('backend/agbs/{agb}/download', 'App\Http\Controllers\Backend\AgbController@download');
Route::post('backend/agbs/ajax/sortAgb', 'App\Http\Controllers\Backend\AgbController@sortAgb');

// Auswertung ROMY VOTING 2022
Route::get('backend/romyvoting/cleanup', 'App\Http\Controllers\Backend\RomyVotingController@cleanUp');
Route::get('backend/romyvoting/{jahr?}', 'App\Http\Controllers\Backend\RomyVotingController@index');

// Auswertung NÖ Tourismusvoting 2022
Route::get('backend/tourismusvoting/cleanup', 'App\Http\Controllers\Backend\TourismusVotingController@cleanUp');
Route::get('backend/tourismusvoting', 'App\Http\Controllers\Backend\TourismusVotingController@index');


//FAQ
Route::get("backend/faq", "App\Http\Controllers\Backend\PageController@faq");

//Googlemaps
Route::get("backend/googlemaps", "App\Http\Controllers\Backend\PageController@googlemaps");

//Video MP2025
Route::get('backend/bebest2025', "App\Http\Controllers\Backend\PageController@bebest2025");


Route::get("backend/faq/download/{id}", "App\Http\Controllers\Backend\PageController@download");
Route::get("backend/phone", "App\Http\Controllers\Backend\PageController@phone");
Route::get("backend/phone/json", "App\Http\Controllers\Backend\PageController@json");








