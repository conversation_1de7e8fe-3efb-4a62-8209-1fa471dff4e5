<?php

use App\Http\Controllers\Kidskrone\KidskroneAdminSlideshowController;
use App\Http\Controllers\Kidskrone\KidskroneAdminVideoController;
use App\Http\Controllers\Kidskrone\KidskroneAttachmentController;
use Illuminate\Support\Facades\Route;

/*
*  Kidskrone New with covers
*/

Route::get('backend/kidskrone/slideshow', [KidskroneAdminSlideshowController::class, 'index']);
Route::post('backend/kidskrone/slideshow', [KidskroneAdminSlideshowController::class, 'save']);
Route::delete('backend/kidskrone/slideshow/{image_id}/delete', [KidskroneAdminSlideshowController::class, 'delete']);
Route::post('backend/kidskrone/slideshow/js', [KidskroneAdminSlideshowController::class, 'jsHandler']);
Route::get('backend/kidskrone', [KidskroneAdminVideoController::class, 'index']);

Route::get('backend/kidskrone/create', [KidskroneAdminVideoController::class, 'create']);
Route::post('backend/kidskrone/delete-pdf', [KidskroneAdminVideoController::class, 'delete_pdf']);
Route::post('backend/ajax/kidskrone/js', [KidskroneAdminVideoController::class, 'save_js']);

// checked

Route::post('backend/kidskrone/{video}', [KidskroneAdminVideoController::class, 'update']);
Route::get('backend/kidskrone/{video}', [KidskroneAdminVideoController::class, 'show']);
Route::delete('backend/kidskrone/{video}/delete', [KidskroneAdminVideoController::class, 'delete']);
Route::post('backend/kidskrone/{video}/thumbnail', [KidskroneAdminVideoController::class, 'selectThumbnail']);
/*Create*/
Route::get('backend/kidskrone/{video}/attachment', [KidskroneAttachmentController::class, 'create']);
Route::get('backend/kidskrone/attachment/{attachment}', [KidskroneAttachmentController::class, 'show']);
Route::delete('backend/kidskrone/attachment/{attachment}/delete', [KidskroneAttachmentController::class, 'attachmentDelete']);
Route::post('backend/kidskrone/attachment/{attachment}', [KidskroneAttachmentController::class, 'update']);

if (env('APP_ENV') === 'local') Route::get('backend/kidskrone/local', [KidskroneAdminVideoController::class, 'local']);
