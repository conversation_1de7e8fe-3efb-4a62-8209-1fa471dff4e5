<?php

use Illuminate\Support\Facades\Route;

Route::get('backend/webchat/{index?}', [\App\Http\Controllers\WebChat\WebChatBackendController::class, 'index']);
Route::post('backend/webchat', [\App\Http\Controllers\WebChat\WebChatBackendController::class, 'save']);
Route::post('backend/webchat/js', [\App\Http\Controllers\WebChat\WebChatBackendController::class, 'save_js']);
Route::post('backend/webchat/text-boilerplate/{id?}', [\App\Http\Controllers\WebChat\WebChatBackendController::class, 'saveTextBoilerplate']);
Route::delete('backend/webchat/delete-boilerplate/{id}', [\App\Http\Controllers\WebChat\WebChatBackendController::class, 'deleteTextBoilerplate']);
Route::post('backend/webchat/forward-team-email', [\App\Http\Controllers\WebChat\WebChatBackendController::class, 'createTeam']);
Route::post('backend/webchat/forward-team-email/{team}', [\App\Http\Controllers\WebChat\WebChatBackendController::class, 'updateTeam']);
Route::delete('backend/webchat/forward-team-email/{team}/delete', [\App\Http\Controllers\WebChat\WebChatBackendController::class, 'deleteTeam']);
Route::post('backend/webchat/reasons/{reason}', [\App\Http\Controllers\WebChat\WebChatBackendController::class, 'updateReason']);
Route::post('backend/webchat/reasons', [\App\Http\Controllers\WebChat\WebChatBackendController::class, 'createReason']);
Route::post('backend/webchat/opening-times/{media}', [\App\Http\Controllers\WebChat\WebChatBackendController::class, 'openingTimesSave']);


Route::get('backend/webchat-agent', [App\Http\Controllers\WebChat\AgentController::class, 'index']);
Route::post('backend/webchat-agent', [App\Http\Controllers\WebChat\AgentController::class, 'addNewAgent']);
Route::put('backend/webchat-agent/{agent}', [App\Http\Controllers\WebChat\AgentController::class, 'agentUpdate']);
Route::delete('backend/webchat-agent/{agent}/delete', [App\Http\Controllers\WebChat\AgentController::class, 'delete']);
Route::post("backend/ajax/webchat-agent/users", [App\Http\Controllers\WebChat\AgentController::class, 'ajaxSearchUsers']);
Route::get("backend/webchat-login", [App\Http\Controllers\WebChat\AgentController::class, 'showLoginMask']);
Route::post("backend/webchat-login", [App\Http\Controllers\WebChat\AgentController::class, 'login']);



Route::get('backend/webchat-statistics/{index?}', [App\Http\Controllers\WebChat\WebChatStatisticsController::class, 'index']);
Route::get('backend/webchat-statistics/{index}/download', [App\Http\Controllers\WebChat\WebChatStatisticsController::class, 'download']);
