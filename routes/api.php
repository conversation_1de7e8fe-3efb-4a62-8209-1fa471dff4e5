<?php


use App\Http\Controllers\Api\KroneCockpitController;
use App\Http\Controllers\Mediathek\MediathekApiController;
use App\Http\Controllers\WebChat\WebChatController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\EpaperApiController;
use App\Http\Controllers\Api\ADLoginController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

if (env('APP_ENV') == 'production'):
    header("Access-Control-Allow-Origin: *");
    header("Access-Control-Allow-Headers: *");
endif;


Route::get("get_all_krone", [KronecockpitController::class, 'getTodayKroneAustria']);





Route::group(['middleware' => ['apiAccessControl']], function () {


    Route::post('mediathek/add_remove_favorites', [MediathekApiController::class, 'add_remove_favorites']);
    Route::post('mediathek/send_email', [MediathekApiController::class, 'send_email']);
    Route::get('mediathek/get-media', [MediathekApiController::class, 'getMedia']);
/*    if (env('APP_ENV') == 'local'):
        $user = App\Models\User::find(36);
        request()->merge(['AuthUser' => $user]);
    endif;*/
    Route::get('mediathek/get-starting-data', [MediathekApiController::class, 'getStartingData']);
    Route::post('mediathek/create_ebook', [MediathekApiController::class, 'create']);
    Route::post('mediathek/delete_epaper', [MediathekApiController::class, 'delete']);
    Route::post('mediathek/set_bookmark', [MediathekApiController::class, 'set_bookmark']);


});

// Route::get('test', "App\Http\Controllers\Api\MediathekController@test");
/*Mediathek*/

Route::group(['middleware' => ['jwt.verify']], function () {
});


Route::post('/mediathek', [EpaperApiController::class, 'get_page']);
Route::get('/mediathek/{date}/{media}/{page?}', [EpaperApiController::class, 'get_catalog_page']);

Route::post('/adlogin', [ADLoginController::class, 'login'])->middleware('throttle:30,1');

Route::get('kidskrone/get-all', [\App\Http\Controllers\Kidskrone\KidskroneApiController::class, 'get_all']);
Route::post('kidskrone/email-send', [\App\Http\Controllers\Kidskrone\KidskroneApiController::class, 'emailSend']);


Route::get('impressum/{medium}', 'App\Http\Controllers\Api\ImprintApiController@index');


Route::get('gl-ausgabe-importer', [EpaperApiController::class, 'gl_ausgabe_importer']);


Route::get('web-chat-status', [WebChatController::class, 'chatStatusApi']);

