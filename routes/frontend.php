<?php

    use App\Http\Controllers\Monitor\SliderController;
    use App\Http\Controllers\WebChat\WebChatController;
    use Illuminate\Support\Facades\Route;


    if(env('APP_ENV') == 'local'){
        Route::get('test-local', [App\Http\Controllers\Backend\PageController::class, 'testLocal']);
    }


    Route::get('reload-captcha', 'App\Http\Controllers\Frontend\CaptchaController@reloadCaptcha');
    Route::get('ip', 'App\Http\Controllers\Frontend\PageController@getIp');


    //MEDIAPRINT.AT

    Route::get('web-chat-agent/{log?}', [WebChatController::class, 'redirect']);

    /*
     * NEW WEB-CHAT
     */
    Route::get('web-chat-app/{any?}', [WebChatController::class, 'webChatFrontend'])->where('any', '.*');

    Route::get('test', "App\Http\Controllers\TestController@index");
    /*   Route::get('/test', function(){return view('errors.whoops');});*/


    if(env('APP_ENV') != 'production'){

        /*test*/

        Route::get('tus', [App\Http\Controllers\Backend\VimeoController::class, 'tus']);
        Route::post('tus-create', [App\Http\Controllers\Backend\VimeoController::class, 'tus_create']);

        Route::get('vimeo', [App\Http\Controllers\Backend\VimeoController::class, 'index']);
        Route::get('vimeo/create', [App\Http\Controllers\Backend\VimeoController::class, 'create']);
        Route::post('vimeo/create', [App\Http\Controllers\Backend\VimeoController::class, 'post_create']);
        Route::get('vimeo/success', [App\Http\Controllers\Backend\VimeoController::class, 'success']);
        Route::get('vimeo/edit/{video_id}', [App\Http\Controllers\Backend\VimeoController::class, 'show']);
        Route::post('vimeo/edit/{video_id}', [App\Http\Controllers\Backend\VimeoController::class, 'update']);
        Route::post('vimeo/whitelist/{video_id}/{action}/{domain}', [App\Http\Controllers\Backend\VimeoController::class, 'setWhitelist']);
        Route::delete('vimeo/delete/{video_id}', [App\Http\Controllers\Backend\VimeoController::class, 'delete']);
        Route::get('vimeo/test', [App\Http\Controllers\Backend\VimeoController::class, 'test']);
    }


    Route::get('mitarbeiterbefragung', function(){
        return view('frontend.mabf');
    });
    Route::get('/', function(){
        return view("frontend.home-page");
    });
    Route::get('markenportfolio', "App\Http\Controllers\Frontend\PageController@markenportfolio");
    Route::get('markenportfolio/kronen-zeitung', function(){
        return view('frontend.markenportfolio.krone');
    });
    Route::get('markenportfolio/kurier', function(){
        return view('frontend.markenportfolio.kurier');
    });
    Route::get('news-area', "App\Http\Controllers\Frontend\PageController@news_area");
    Route::get('unternehmen', "App\Http\Controllers\Frontend\PageController@unternehmen");
    Route::group(['prefix' => 'b2b-services'], function(){
        Route::get('/vermarktung', function(){
            return view('frontend.b2b-services.vermarktung');
        });
        Route::get('/druck', function(){
            return view('frontend.b2b-services.druck');
        });
        Route::get('/logistik', function(){
            return view('frontend.b2b-services.logistik');
        });
        Route::get('/vertrieb', function(){
            return view('frontend.b2b-services.vertrieb');
        });
        Route::get('/it-consulting', function(){
            return view('frontend.b2b-services.it-consulting');
        });
        Route::get('/einkauf', function(){
            return view('frontend.b2b-services.einkauf');
        });
        Route::get('/kundenmanagement', function(){
            return view('frontend.b2b-services.kundenmanagement');
        });
        Route::get('/corporate-publishing', function(){
            return view('frontend.b2b-services.corporate-publishing');
        });
    });
    Route::get('abowelt', function(){
        return view('frontend.abowelt');
    });

    //KONTAKTSEITE
    Route::get('kontakt', "App\Http\Controllers\Frontend\PageController@kontakt");
    Route::post('kontakt', "App\Http\Controllers\Frontend\PageController@kontaktPost")->middleware(['throttle:10,60']);

    // Footer
    Route::get('datenschutz', "App\Http\Controllers\Frontend\PageController@datenschutz");
    Route::get('datenschutz/{dataprivacy_id?}', "App\Http\Controllers\Frontend\PageController@datenschutz");
    Route::get('agb', "App\Http\Controllers\Frontend\PageController@agb");
    Route::get('agb/{agb_id?}', 'App\Http\Controllers\Frontend\PageController@agb');
    Route::get('impressum', 'App\Http\Controllers\Frontend\PageController@impressum');
    Route::get('cookiepolicy', function(){
        return view('frontend.cookiepolicy');
    });
    Route::get('barrierefreiheit', function(){
        return view('frontend.barrierefreiheit');
    });

    Route::get('hilfe', function(){
        return view('frontend.hilfe');
    });
    Route::get('cacheclear', "App\Http\Controllers\Frontend\PageController@cacheclear");
    Route::get("sitemap", function(){
        return response()->view("frontend.sitemap")->header('Content-Type', 'text/xml');
    });


    //ANMELDUNG FÜR DIVERSE FEIERN
    Route::get('anmeldung/{event}/statistik', "App\Http\Controllers\Frontend\PageController@getEventAnmeldungStatistik");
    Route::get('anmeldung/{event}/{hash?}', "App\Http\Controllers\Frontend\PageController@getEventAnmeldung");
    Route::post('anmeldung/{event}', "App\Http\Controllers\Frontend\PageController@postEventAnmeldung");


    //ANMELDUNG DIGIPRINT-OPENHOUSE
    Route::get("digiprint-openhouse/{token}", "App\Http\Controllers\Frontend\PageController@getDigiprintOpenhouseRegistration");
    Route::post("digiprint-openhouse/{token}", "App\Http\Controllers\Frontend\PageController@postDigiprintOpenhouseRegistration");
    /*redirectDigiprintOpenhouseRegistrationSuccess is missing*/
    Route::get("digiprint-openhouse/{token}/erfolgreich", "App\Http\Controllers\Frontend\PageController@redirectDigiprintOpenhouseRegistrationSuccess");


    //PERMISSION
    Route::get('permission/{transaktionsid}', "App\Http\Controllers\Frontend\PermissionController@index");
    Route::post('permission/{transaktionsid}', "App\Http\Controllers\Frontend\PermissionController@store");

    /*
     *  form -> b2b-services/druck
     *  Druck Anmeldung
     *
     */
    Route::post('ajax/geschaeftskunden/druck/sendanmeldung', 'App\Http\Controllers\Frontend\PageController@druckSendAnmeldung');


    /*
    |--------------------------------------------------------------------------
    | Backend Routes, die kein Login voraussetzen
    |--------------------------------------------------------------------------
    */



    Route::get('weihnachten', function(){
        return view("frontend.weihnachten");
    });


    //Widerruf
    // Login
    Route::get('widerruf', 'App\Http\Controllers\Frontend\PageController@widerruf');
    Route::post('widerruf', 'App\Http\Controllers\Frontend\PageController@widerrufPost');

    //VERALTETER BROWSER
    Route::get('oldbrowser', function(){
        return view('errors.oldbrowser');
    });
    Route::get('presse/{medium}/{id}', "App\Http\Controllers\Frontend\PageController@presseShow");


    //KATALOG Mediathek
    Route::get('katalog/{dir}', [App\Http\Controllers\Frontend\PageController::class, "katalog"]);
    Route::get('katalog/{dir}/download', "App\Http\Controllers\Frontend\PageController@katalogDownload");

    Route::get('2021/mitarbeiterbefragung', function(){
        return view('frontend.mabf-video');
    });


    //Rss Viewer old
    Route::get("rssviewer/{medium}", [SliderController::class, 'rssViewer']);

    Route::get("monitor/rss/{medium}", [SliderController::class, 'rssViewer']);
    Route::get('monitor/infoscreen/{medium}/{playlist?}/{check?}', [SliderController::class, 'infoScreen']);

    //Redirect bebest25
    Route::get('bebest2025', function(){
        return redirect("backend/bebest2025");
    });


    Route::get("maps/sample1", "App\Http\Controllers\Frontend\PageController@MapsSample1");


    Route::get("covid-impfung", "App\Http\Controllers\Frontend\PageController@covid_impfung");

    Route::get("applyaccess", "App\Http\Controllers\Frontend\PageController@applyaccess");

    Route::get("kidskrone/download/{filename}", "App\Http\Controllers\Frontend\PageController@download_kidskrone_pdf");

    /*
     *    Karriere
     */
    Route::group(['prefix' => 'karriere'], function(){
        Route::get('/', function(){
            return view('frontend.karriere.index');
        });
        Route::get('/arbeiten_bei_mediaprint', function(){
            return view('frontend.karriere.arbeiten_bei_mp');
        });
        Route::get('/lehre_bei_mediaprint', function(){
            return view('frontend.karriere.lehre');
        });

/*        Route::get('/bewerben', "App\Http\Controllers\Frontend\CarrierController@index");
        Route::get('/bewerben/{job_id}', "App\Http\Controllers\Frontend\CarrierController@show");
        Route::get('/bewerben/{job_id}/pdf/{only_show?}', [App\Http\Controllers\Frontend\CarrierController::class, "downloadPdf"]);
        Route::get('/bewerbung', "App\Http\Controllers\Frontend\CarrierController@karriere_framesite");
        Route::get('/bewerbung/{job_id}', "App\Http\Controllers\Frontend\CarrierController@karriere_framesite");*/

        /*
         * Carrier NEW
         */
        Route::get('/bewerben', [App\Http\Controllers\Frontend\MpCarrierController::class, 'index']);
        Route::get('/bewerben/{seo_ur}', [App\Http\Controllers\Frontend\MpCarrierController::class, 'show']);
        Route::get('/bewerben/{seo_ur}/pdf/{only_show?}', [App\Http\Controllers\Frontend\MpCarrierController::class, 'downloadPdf']);
        Route::get('/update', [App\Http\Controllers\Frontend\MpCarrierController::class, 'update']);
        Route::get('/companies', [App\Http\Controllers\Frontend\MpCarrierController::class, 'get_companies']);
        Route::get('/job-kurier-xml/{company?}', [App\Http\Controllers\Frontend\MpCarrierController::class, 'job_kurier_xml']);
        Route::post('/job/{job_id}/send-email', [App\Http\Controllers\Frontend\MpCarrierController::class, 'sendEmail']);
        /*
         * Carrier NEW --end
         */


        Route::get('/linkedin-xml-feeds', [App\Http\Controllers\Frontend\CarrierController::class, 'linkedInXMLFeed']);
        /*
         * Email send , new Inserate
         */
        Route::post('/contact-person/{id}/email', [App\Http\Controllers\JobAdsManager\ContactPersonController::class, 'sendEmail']);
    });



    /*
     *    Umantis
     */
    Route::group(['prefix' => 'umantis'], function () {

        Route::get('/widget/{seo}', [App\Http\Controllers\Umantis\PageController::class, 'index']);
    });
