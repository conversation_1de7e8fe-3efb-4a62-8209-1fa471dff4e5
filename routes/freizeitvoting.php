<?php

use Illuminate\Support\Facades\Route;


Route::get('jahreswahl/gasthaus/{freizeit_gasthaus_id}', 'App\Http\Controllers\Freizeitvoting\FreizeitVotingController@indexGasthausJahreswahl');
Route::post('jahreswahl/gasthaus/{freizeit_gasthaus_id}', 'App\Http\Controllers\Freizeitvoting\FreizeitVotingController@storeGasthausJahreswahl');

//FREIZEIT VOTING 2022
Route::get('/', 'App\Http\Controllers\Freizeitvoting\FreizeitVotingController@index');
Route::get('ip', 'App\Http\Controllers\Frontend\PageController@getIp');
Route::get('import-gasthaeuser', 'App\Http\Controllers\Freizeitvoting\FreizeitVotingController@importGasthaeuser');
Route::get('import-gerichte', 'App\Http\Controllers\Freizeitvoting\FreizeitVotingController@importGerichte');
Route::get('gasthaeuser', 'App\Http\Controllers\Freizeitvoting\FreizeitVotingController@indexWirtshaeuser');
Route::post('gasthaeuser', 'App\Http\Controllers\Freizeitvoting\FreizeitVotingController@storeWirtshaeuser');
Route::get('beste-gerichte', 'App\Http\Controllers\Freizeitvoting\FreizeitVotingController@indexBesteGerichte');
Route::post('beste-gerichte', 'App\Http\Controllers\Freizeitvoting\FreizeitVotingController@storeBesteGerichte');
Route::get('gasthaus-nominieren', 'App\Http\Controllers\Freizeitvoting\FreizeitVotingController@indexWirtshaeuserNominieren');
Route::post('gasthaus-nominieren', 'App\Http\Controllers\Freizeitvoting\FreizeitVotingController@storeWirtshaeuserNominieren');
Route::get('cookiepolicy', function () {
    return view('freizeitvoting.cookiepolicy');
});
