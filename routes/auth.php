<?php

use App\Http\Controllers\Auth\AuthController;
use App\Http\Controllers\Backend\LoginController;
use Illuminate\Support\Facades\Route;




# Old login
Route::get('login-legacy', [LoginController::class, 'index']);
Route::post('login-legacy', [LoginController::class, 'login'])->middleware('limited');
Route::post('sms_verify', [LoginController::class, 'sms_verify']);;



Route::get('login', [App\Http\Controllers\Auth\AuthController::class, 'index']);
Route::post('login', [App\Http\Controllers\Auth\AuthController::class, 'login']);
Route::get('backend/logout', [AuthController::class, 'logout']);
Route::get('/oauth-callback', [App\Http\Controllers\Auth\AuthController::class, 'oauthCallback']);
