<?php

    use Illuminate\Support\Facades\Route;


    //FAQ für MA
    Route::get('faq', function(){ return redirect('backend/faq'); });


    /************************************************************
     ******************* WEITERLEITUNGEN *************************
     ************************************************************/

    // EINZELHANDEL

    Route::get('einzelhandel', function(){ return redirect('http://einzelhandel.mediaprint.at'); });
    Route::get('einzelhandel/admin', function(){ return redirect('http://einzelhandel.mediaprint.at/admin'); });
    Route::get('ehabmelden', function(){ return redirect('http://einzelhandel.mediaprint.at/newsletterabmelden'); });

    // TIERECKE
    Route::get('tierecke', function(){ return redirect('http://tierecke.mediaprint.at'); });

    //MEINE ZEITUNG KRONE
    Route::get('meinezeitung', function(){ return redirect('http://meinekrone.mediaprint.at'); });
    Route::get('meinezeitung/admin', function(){ return redirect('http://meinekrone.mediaprint.at/admin'); });

    //MEINE ZEITUNG KURIER
    Route::get('meinezeitung/kurier', function(){ return redirect('http://meinkurier.mediaprint.at'); });
    Route::get('meinezeitung/kurier/admin', function(){ return redirect('http://meinkurier.mediaprint.at/admin'); });

    //LOGISTIK
    Route::get('logistik', function(){ return redirect('http://logistik.mediaprint.at'); });
    //Route::get('logistik/{?param}', function () { return redirect('http://logistik.mediaprint.at'); });

    //KURIERANZEIGEN
    Route::get('kurieranzeigen_erweiterung', function(){ return redirect('http://kurieranzeigen.at/kurieranzeigen_erweiterung'); });
    Route::get('kurieranzeigenabmelden', function(){ return redirect('http://kurieranzeigen.at/newsletterabmelden'); });
    Route::get('tarifrechner/kurier', function(){ return redirect('http://www.kurieranzeigen.at/kurieranzeigen_erweiterung/beilagenrechner_kurier'); });

    //Wortanzeigen
    Route::get('wortanzeigen', function(){ return redirect('https://online.mediaprint.at/online/mediaprint'); });

    //Kroneanzeigen Weiterleitungen
    Route::get('kronebunt', function(){ return redirect('https://business.krone.at/portfolio/print/krone-bunt'); });
    Route::get('supplement', function(){ return redirect('https://business.krone.at/mediasolutions/krone-beilagen'); });
    Route::get('sonderwerbeformen', function(){ return redirect('https://business.krone.at/mediasolutions/sonderwerbeformen'); });
    Route::get('geo-service', function(){ return redirect('http://business.krone.at'); });
    Route::get('tvwoche', function(){ return redirect('http://business.krone.at'); });
    Route::get('wediwi', function(){ return redirect('http://business.krone.at'); });




