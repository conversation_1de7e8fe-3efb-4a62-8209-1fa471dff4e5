<?php

    use Illuminate\Support\Facades\Route;

    //KMU Aktionen
    Route::get("gastro", "App\Http\Controllers\Frontend\KMUController@index");
    Route::post("gastro", "App\Http\Controllers\Frontend\KMUController@store");
    Route::get("event/gastro", "App\Http\Controllers\Frontend\KMUController@index");
    Route::post("event/gastro", "App\Http\Controllers\Frontend\KMUController@store");
    Route::get("gastro/download/{encrypted_string}", "App\Http\Controllers\Frontend\KMUController@download_kmu");
    Route::get("gastro/download/cron/daily", "App\Http\Controllers\Frontend\KMUController@mail_send_kmu_daily");
    Route::get("gastro/download/daily/{date?}", "App\Http\Controllers\Frontend\KMUController@download_kmu_daily");
    //Script Ausgabe
    Route::get("gastro/script", "App\Http\Controllers\Frontend\KMUController@getScript");


    /*
	 *  Offline per 30.07.2020
		Route::get("friseur", "Frontend\KMUController@index");
		Route::post("friseur", "Frontend\KMUController@store");
		Route::get("event/friseur", "Frontend\KMUController@index");
		Route::post("event/friseur", "Frontend\KMUController@store");
	*/
    Route::get("friseur/download/{encrypted_string}", "App\Http\Controllers\Frontend\KMUController@download_kmu");
    Route::get("friseur/download/cron/daily", "App\Http\Controllers\Frontend\KMUController@mail_send_kmu_daily");
    Route::get("friseur/download/daily/{date?}", "App\Http\Controllers\Frontend\KMUController@download_kmu_daily");
    //Script Ausgabe
    Route::get("friseur/script", "App\Http\Controllers\Frontend\KMUController@getScript");

    Route::get("hotel", "App\Http\Controllers\Frontend\KMUController@index");
    Route::post("hotel", "App\Http\Controllers\Frontend\KMUController@store");
    Route::get("event/hotel", "App\Http\Controllers\Frontend\KMUController@index");
    Route::post("event/hotel", "App\Http\Controllers\Frontend\KMUController@store");
    Route::get("hotel/download/{encrypted_string}", "App\Http\Controllers\Frontend\KMUController@download_kmu");
    Route::get("hotel/download/cron/daily", "App\Http\Controllers\Frontend\KMUController@mail_send_kmu_daily");
    Route::get("hotel/download/daily/{date?}", "App\Http\Controllers\Frontend\KMUController@download_kmu_daily");
    //Script Ausgabe
    Route::get("hotel/script", "App\Http\Controllers\Frontend\KMUController@getScript");


    Route::get("firma", "App\Http\Controllers\Frontend\KMUController@index");
    Route::post("firma", "App\Http\Controllers\Frontend\KMUController@store");
    Route::get("event/firma", "App\Http\Controllers\Frontend\KMUController@index");
    Route::post("event/firma", "App\Http\Controllers\Frontend\KMUController@store");
    Route::get("firma/download/{encrypted_string}", "App\Http\Controllers\Frontend\KMUController@download_kmu");
    Route::get("firma/download/cron/daily", "App\Http\Controllers\Frontend\KMUController@mail_send_kmu_daily");
    Route::get("firma/download/daily/{date?}", "App\Http\Controllers\Frontend\KMUController@download_kmu_daily");
    //Script Ausgabe
    Route::get("firma/script", "App\Http\Controllers\Frontend\KMUController@getScript");
