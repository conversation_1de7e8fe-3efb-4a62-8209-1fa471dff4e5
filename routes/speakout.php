<?php

use App\Http\Controllers\Speakout\SpeakOutPageController;
use Illuminate\Support\Facades\Route;

Route::get('/', [SpeakOutPageController::class, "index"]);
Route::get('speaker/{speaker?}', [SpeakOutPageController::class, "speaker"]);
Route::get('highlights/{news?}', [SpeakOutPageController::class, "news"]);
Route::get('gruene-welt/{news?}', [SpeakOutPageController::class, "greenWorld"]);
Route::get('cookiepolicy', [SpeakOutPageController::class, "cookiepolicy"]);



Route::get('document/{documentName}', [SpeakOutPageController::class, "showDocument"]);


Route::get('programm/{selected?}', [SpeakOutPageController::class, "program"])->where('selected', '[0-9]+');






Route::get('werbematerialien', [SpeakOutPageController::class, "press"]);
Route::get('werbematerialien/{speakoutPress}/download', [SpeakOutPageController::class, "pressDownload"]);

Route::get('impressionen', function (){
    redirect('impressionen-video');
});
Route::get('impressionen-video', [SpeakOutPageController::class, "insights"]);

Route::get('impressionen-fotogalerie', [SpeakOutPageController::class, "fotogalerie"]);
