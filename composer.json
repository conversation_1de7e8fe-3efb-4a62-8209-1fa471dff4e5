{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"ext-json": "*", "ext-simplexml": "*", "barryvdh/laravel-dompdf": "^2.0", "donatj/phpuseragentparser": "*", "fideloper/proxy": "^4.4", "fruitcake/laravel-cors": "^2.0", "geocoder-php/photon-provider": "0.5.0", "guzzlehttp/guzzle": "^7.0.1", "guzzlehttp/psr7": "^2.6", "intervention/image": "^2.7", "laravel/framework": "^8.12", "laravel/tinker": "^2.5", "mews/captcha": "^3.2", "mp-itweb/vimeo-sdk": "dev-master", "mpdf/mpdf": "^8.0", "php-http/guzzle7-adapter": "^1.0", "php-http/message": "^1.16", "php-http/message-factory": "^1.1", "rap2hpoutre/fast-excel": "^3.2", "ext-libxml": "*", "mp-itweb/mp-oauth": "dev-main"}, "repositories": [{"type": "git", "url": "https://gitlab.mediaprint.co.at/webgruppe/vimeo-sdk.git", "options": {"ssl": {"verify_peer": false}}}, {"type": "git", "url": "https://gitlab.mediaprint.co.at/webgruppe/mp-oauth.git"}], "require-dev": {"barryvdh/laravel-ide-helper": "^2.13", "facade/ignition": "^2.5", "fakerphp/faker": "^1.9.1", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.2", "nunomaduro/collision": "^5.0", "phpunit/phpunit": "^9.3.3"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"php-http/discovery": true}}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"], "post-install-cmd": ["@php artisan vendor:publish --tag=vimeo-sdk-js --force", "@php artisan vendor:publish --tag=mp-oauth-config"], "post-update-cmd": ["@php artisan vendor:publish --tag=vimeo-sdk-js --force", "@php artisan vendor:publish --tag=mp-oauth-config"]}}